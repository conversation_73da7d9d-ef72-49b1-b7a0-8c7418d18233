# Apple爬虫用户信息配置示例
# 复制此文件为 application.properties 并修改为你的实际信息

spring.application.name=crawler

# Apple登录爬虫配置
apple.login.username=<EMAIL>
apple.login.password=your_password
apple.login.headless=true

# Apple iPhone购买配置
apple.iphone.buyUrl=https://www.apple.com.cn/shop/buy-iphone/iphone-16
apple.iphone.model=iphone-16
apple.iphone.modelDisplayName=iPhone 16
apple.iphone.color=black
apple.iphone.colorDisplayName=\u9ED1\u8272
apple.iphone.storage=128gb
apple.iphone.storageDisplayName=128GB \u811A\u6CE8
apple.iphone.tradeIn=false
apple.iphone.appleCare=false

# Apple用户信息配置
apple.user.username=<EMAIL>
apple.user.password=your_password
apple.user.firstName=\u4F60\u7684\u540D\u5B57
apple.user.lastName=\u4F60\u7684\u59D3\u6C0F
apple.user.phone=\u4F60\u7684\u624B\u673A\u53F7
apple.user.email=<EMAIL>
apple.user.address=\u4F60\u7684\u5730\u5740
apple.user.city=\u4F60\u7684\u57CE\u5E02
apple.user.province=\u4F60\u7684\u7701\u4EFD
apple.user.postalCode=\u90AE\u653F\u7F16\u7801
apple.user.idCardNumber=\u8EAB\u4EFD\u8BC1\u53F7\u7801
apple.user.idCardName=\u8EAB\u4EFD\u8BC1\u59D3\u540D

# Apple文件路径配置
apple.auth.file.path=apple-auth.json
apple.screenshot.dir=./AppleScreenshots

# Apple视频录制配置
apple.record.video=true
apple.video.dir=videos

# Apple取货信息重试配置
apple.pickup.retry.count=3

# Apple网络错误检测配置
apple.network.error.detection=true

# Apple浏览器模式配置
apple.use.persistent.context=false

# 日志级别配置
logging.level.com.crawler.apple=DEBUG
