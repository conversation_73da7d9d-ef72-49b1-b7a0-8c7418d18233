# Maven
target/
!target/applecrawler-0.0.1-SNAPSHOT.jar
!target/applecrawler-0.0.1-SNAPSHOT.jar.original

# IDE
.idea/
.vscode/
*.iml
*.ipr
*.iws

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
docs/
*.md
!README.md

# Scripts
*.sh

# Logs
*.log

# Temporary files
*.tmp
*.temp

# Node modules (if any)
node_modules/

# Package files
package-lock.json

# Videos (too large for Docker context)
videos/

# Screenshots (optional, can be included if needed)
# AppleScreenshots/

# User sessions (will be mounted as volume)
# users/
# my-apple-session/
