package com.crawler.test;

import com.crawler.message.CrawlerMessage;
import com.crawler.user.UserProfileManager;
import com.crawler.worker.CrawlerWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 测试自动配置增强功能
 * 当NettyHttp模式缺少httpStockChecker配置时，自动使用Playwright模式补全
 */
public class AutoConfigEnhancementTest {

    private static final Logger logger = LoggerFactory.getLogger(AutoConfigEnhancementTest.class);

    public static void main(String[] args) {
        String testUsername = "<EMAIL>";

        try {
            logger.info("=== 自动配置增强测试开始 ===");

            // 1. 检查用户配置是否存在
            UserProfileManager userProfileManager = new UserProfileManager();
            userProfileManager.initialize();

            if (!userProfileManager.userProfileExists(testUsername)) {
                logger.error("测试用户 {} 不存在，请先创建用户配置", testUsername);
                return;
            }

            // 2. 备份并删除现有的会话文件中的httpStockChecker配置（模拟缺少配置的情况）
            Path sessionFile = userProfileManager.getSessionFilePath(testUsername);
            if (Files.exists(sessionFile)) {
                logger.info("备份现有会话文件: {}", sessionFile);
                Path backupFile = Paths.get(sessionFile.toString() + ".backup");
                Files.copy(sessionFile, backupFile);

                // 读取会话文件并移除httpStockChecker配置
                String content = Files.readString(sessionFile);
                if (content.contains("httpStockChecker")) {
                    // 简单地移除httpStockChecker部分来模拟缺少配置
                    content = content.replaceAll(",\\s*\"httpStockChecker\"\\s*:\\s*\\{[^}]*\\}", "");
                    content = content.replaceAll("\"httpStockChecker\"\\s*:\\s*\\{[^}]*\\},?", "");
                    Files.writeString(sessionFile, content);
                    logger.info("已移除httpStockChecker配置，模拟缺少配置的情况");
                }
            } else {
                logger.warn("会话文件不存在: {}", sessionFile);
            }

            // 3. 创建消息队列和Worker
            BlockingQueue<CrawlerMessage> messageQueue = new LinkedBlockingQueue<>();

            // 使用HYBRID模式
            CrawlerWorker worker = new CrawlerWorker(testUsername, messageQueue,
                    userProfileManager, true, CrawlerWorker.WorkerMode.HYBRID);

            // 4. 创建库存检查消息
            UserProfileManager.UserProfile userProfile = userProfileManager.loadUserProfile(testUsername);
            if (userProfile == null) {
                logger.error("无法加载用户配置: {}", testUsername);
                return;
            }

            CrawlerMessage stockCheckMessage = new CrawlerMessage.Builder()
                    .messageType(CrawlerMessage.MessageType.CHECK_IPHONE17PRO_STOCK)
                    .targetSpecificUser(testUsername)
                    .userConfig(userProfile.getUserConfig())
                    .iphoneConfig(userProfile.getIphoneConfig())
                    .pickupLocationConfig(userProfile.getPickupLocationConfig())
                    .priority(1)
                    .build();

            // 5. 添加消息到队列
            messageQueue.offer(stockCheckMessage);
            logger.info("已添加库存检查消息到队列");

            // 6. 启动Worker线程
            Thread workerThread = new Thread(worker);
            workerThread.start();

            // 7. 等待一段时间让Worker处理消息
            logger.info("等待Worker处理消息...");
            Thread.sleep(30000); // 等待30秒

            // 8. 停止Worker
            worker.shutdown();
            workerThread.interrupt();

            // 9. 检查会话文件是否已经包含httpStockChecker配置
            if (Files.exists(sessionFile)) {
                String finalContent = Files.readString(sessionFile);
                if (finalContent.contains("httpStockChecker")) {
                    logger.info("✅ 测试成功！会话文件已包含httpStockChecker配置");

                    // 显示配置内容
                    int start = finalContent.indexOf("\"httpStockChecker\"");
                    if (start != -1) {
                        int end = finalContent.indexOf("}", start) + 1;
                        String config = finalContent.substring(start, end);
                        logger.info("httpStockChecker配置: {}", config);
                    }
                } else {
                    logger.warn("❌ 测试失败！会话文件仍然缺少httpStockChecker配置");
                }
            }

            logger.info("=== 自动配置增强测试完成 ===");

        } catch (Exception e) {
            logger.error("测试过程中发生异常: {}", e.getMessage(), e);
        }
    }
}
