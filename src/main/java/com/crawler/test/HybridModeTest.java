package com.crawler.test;

import com.crawler.config.AppleIphoneConfig;
import com.crawler.config.ApplePickupLocationConfig;
import com.crawler.config.AppleUserConfig;
import com.crawler.message.CrawlerMessage;
import com.crawler.user.UserProfileManager;
import com.crawler.worker.CrawlerWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * HYBRID模式测试程序
 * 验证CrawlerWorker在HYBRID模式下的智能任务分配功能
 */
public class HybridModeTest {

    private static final Logger logger = LoggerFactory.getLogger(HybridModeTest.class);

    public static void main(String[] args) {
        logger.info("开始测试 CrawlerWorker HYBRID 模式");

        try {
            // 1. 创建测试配置
            AppleUserConfig userConfig = createTestUserConfig();
            AppleIphoneConfig iphoneConfig = createTestIphoneConfig();
            ApplePickupLocationConfig pickupConfig = createTestPickupConfig();

            // 2. 创建消息队列和用户管理器
            BlockingQueue<CrawlerMessage> messageQueue = new LinkedBlockingQueue<>();
            UserProfileManager userProfileManager = new UserProfileManager();

            // 3. 创建HYBRID模式的CrawlerWorker
            CrawlerWorker hybridWorker = new CrawlerWorker(
                    "<EMAIL>",
                    messageQueue,
                    userProfileManager,
                    true, // headless
                    CrawlerWorker.WorkerMode.HYBRID
            );

            logger.info("创建的HYBRID Worker: {}", hybridWorker);

            // 4. 创建测试消息
            createTestMessages(messageQueue, userConfig, iphoneConfig, pickupConfig);

            // 5. 启动工作线程
            Thread hybridThread = new Thread(hybridWorker, "Hybrid-Test-Worker");
            hybridThread.start();

            logger.info("HYBRID模式测试线程已启动，将运行10秒...");

            // 6. 运行一段时间
            Thread.sleep(10000); // 运行10秒

            // 7. 停止工作线程
            hybridWorker.shutdown();

            try {
                hybridThread.join(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 8. 输出统计信息
            logger.info("HYBRID Worker 统计信息: {}", hybridWorker.getStats());
            logger.info("HYBRID模式测试完成");

        } catch (Exception e) {
            logger.error("HYBRID模式测试失败: {}", e.getMessage(), e);
        }
    }

    private static AppleUserConfig createTestUserConfig() {
        AppleUserConfig config = new AppleUserConfig();
        config.setUsername("<EMAIL>");
        config.setPassword("Aa112211");
        config.setFirstName("涛");
        config.setLastName("曹");
        config.setPhone("13888858880");
        config.setEmail("<EMAIL>");
        return config;
    }

    private static AppleIphoneConfig createTestIphoneConfig() {
        AppleIphoneConfig config = new AppleIphoneConfig();
        config.setBuyUrl("https://www.apple.com.cn/shop/buy-iphone/iphone-17");
        config.setModelDisplayName("iPhone 17");
        config.setModel("iphone-17");
        config.setColor("black");
        config.setColorDisplayName("黑色");
        config.setStorage("256gb");
        config.setStorageDisplayName("256GB");
        return config;
    }

    private static ApplePickupLocationConfig createTestPickupConfig() {
        ApplePickupLocationConfig config = new ApplePickupLocationConfig();
        config.setProvince("yunnan");
        config.setProvinceDisplayName("云南");
        config.setCity("kunming");
        config.setCityDisplayName("昆明");
        config.setDistrict("wuhua");
        config.setDistrictDisplayName("五华区");
        config.setStoreName("Apple 昆明");
        config.setStoreCode("R670");
        config.setStoreDisplayName("Apple 昆明");
        return config;
    }

    private static void createTestMessages(BlockingQueue<CrawlerMessage> messageQueue,
                                           AppleUserConfig userConfig,
                                           AppleIphoneConfig iphoneConfig,
                                           ApplePickupLocationConfig pickupConfig) {

        String username = "<EMAIL>";

        // 1. 健康检查消息 - 系统级操作
        CrawlerMessage healthCheck = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.HEALTH_CHECK)
                .targetSpecificUser(username)
                .priority(0)
                .build();

        // 2. 库存检查消息 - 应该使用NettyHttp模式
        CrawlerMessage stockCheck = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.CHECK_IPHONE17PRO_STOCK)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(1)
                .build();

        // 3. 会话验证消息 - 应该使用NettyHttp模式
        CrawlerMessage sessionValidation = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.VALIDATE_SESSION)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(2)
                .build();

        // 4. 登录消息 - 应该使用Playwright模式
        CrawlerMessage login = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.LOGIN_AND_GENERATE_SESSION)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(3)
                .build();

        // 5. 加购物车消息 - 应该使用Playwright模式
        CrawlerMessage addToCart = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.ADD_IPHONE17PRO_TO_CART)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(4)
                .build();

        // 添加消息到队列
        messageQueue.offer(healthCheck);
        messageQueue.offer(stockCheck);
        messageQueue.offer(sessionValidation);
        messageQueue.offer(login);
        messageQueue.offer(addToCart);

        logger.info("已创建 {} 个测试消息", messageQueue.size());
        logger.info("消息类型和预期执行模式:");
        logger.info("1. HEALTH_CHECK -> 系统级操作");
        logger.info("2. CHECK_IPHONE17PRO_STOCK -> NettyHttp模式（高效）");
        logger.info("3. VALIDATE_SESSION -> NettyHttp模式（快速验证）");
        logger.info("4. LOGIN_AND_GENERATE_SESSION -> Playwright模式（复杂交互）");
        logger.info("5. ADD_IPHONE17PRO_TO_CART -> Playwright模式（页面操作）");
        logger.info("");
        logger.info("请观察日志中的模式选择信息...");
    }
}
