package com.crawler.test;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.crawler.apple.NettyHttpBusinessFlow;
import com.crawler.config.AppleIphoneConfig;
import com.crawler.config.ApplePickupLocationConfig;
import com.crawler.config.AppleUserConfig;
import com.crawler.message.CrawlerMessage;
import com.crawler.user.UserProfileManager;
import com.crawler.worker.CrawlerWorker;

/**
 * NettyHttp模式测试程序
 * 用于验证NettyHttp模式的基本功能
 */
public class NettyHttpModeTest {

    private static final Logger logger = LoggerFactory.getLogger(NettyHttpModeTest.class);

    public static void main(String[] args) {
        try {
            logger.info("开始测试 NettyHttp 模式...");

            // 创建测试用户配置
            AppleUserConfig userConfig = new AppleUserConfig();
            userConfig.setUsername("<EMAIL>");
            userConfig.setPassword("testpassword");

            AppleIphoneConfig iphoneConfig = new AppleIphoneConfig();
            iphoneConfig.setBuyUrl("https://www.apple.com.cn/shop/buy-iphone/iphone-16");
            iphoneConfig.setModelDisplayName("iPhone 16 Pro");

            ApplePickupLocationConfig pickupConfig = new ApplePickupLocationConfig();
            // 设置取货地点配置（根据实际的ApplePickupLocationConfig类的方法调整）

            // 创建消息队列和用户管理器
            BlockingQueue<CrawlerMessage> messageQueue = new LinkedBlockingQueue<>();
            UserProfileManager userProfileManager = new UserProfileManager();

            // 测试NettyHttpBusinessFlow
            testNettyHttpBusinessFlow(userConfig, iphoneConfig, pickupConfig);

            // 测试CrawlerWorker NettyHttp模式
            testCrawlerWorkerNettyHttpMode(messageQueue, userProfileManager, userConfig, iphoneConfig, pickupConfig);

            logger.info("NettyHttp 模式测试完成");

        } catch (Exception e) {
            logger.error("测试过程中发生异常: {}", e.getMessage(), e);
        }
    }

    private static void testNettyHttpBusinessFlow(AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig,
            ApplePickupLocationConfig pickupConfig) {
        try {
            logger.info("测试 NettyHttpBusinessFlow...");

            NettyHttpBusinessFlow businessFlow = new NettyHttpBusinessFlow(
                    "testuser", userConfig, iphoneConfig, pickupConfig);

            // 测试会话验证
            Path sessionPath = Paths.get("users/testuser/apple-auth.json");
            if (sessionPath.toFile().exists()) {
                businessFlow.validateSession(sessionPath)
                        .thenAccept(isValid -> {
                            logger.info("会话验证结果: {}", isValid ? "有效" : "无效");
                        })
                        .exceptionally(throwable -> {
                            logger.error("会话验证失败: {}", throwable.getMessage());
                            return null;
                        });
            } else {
                logger.info("会话文件不存在，跳过会话验证测试");
            }

            logger.info("NettyHttpBusinessFlow 测试完成");

        } catch (Exception e) {
            logger.error("NettyHttpBusinessFlow 测试失败: {}", e.getMessage(), e);
        }
    }

    private static void testCrawlerWorkerNettyHttpMode(BlockingQueue<CrawlerMessage> messageQueue,
            UserProfileManager userProfileManager,
            AppleUserConfig userConfig,
            AppleIphoneConfig iphoneConfig,
            ApplePickupLocationConfig pickupConfig) {
        try {
            logger.info("测试 CrawlerWorker NettyHttp 模式...");

            // 创建NettyHttp模式的CrawlerWorker
            CrawlerWorker worker = new CrawlerWorker(
                    "testuser",
                    messageQueue,
                    userProfileManager,
                    true, // headless
                    CrawlerWorker.WorkerMode.NETTY_HTTP);

            logger.info("创建的Worker: {}", worker);

            // 创建测试消息
            CrawlerMessage loginMessage = CrawlerMessage.createLoginMessage(
                    "testuser", userConfig, iphoneConfig, pickupConfig);

            CrawlerMessage stockCheckMessage = new CrawlerMessage.Builder()
                    .messageType(CrawlerMessage.MessageType.CHECK_IPHONE17PRO_STOCK)
                    .targetSpecificUser("testuser")
                    .userConfig(userConfig)
                    .iphoneConfig(iphoneConfig)
                    .pickupLocationConfig(pickupConfig)
                    .build();

            CrawlerMessage addToCartMessage = CrawlerMessage.createAddToCartMessage(
                    "testuser", userConfig, iphoneConfig, pickupConfig);

            // 添加消息到队列
            messageQueue.offer(loginMessage);
            messageQueue.offer(stockCheckMessage);
            messageQueue.offer(addToCartMessage);

            logger.info("已添加 {} 个测试消息到队列", messageQueue.size());

            // 启动worker线程进行短暂测试
            Thread workerThread = new Thread(worker);
            workerThread.start();

            // 等待一段时间让worker处理消息
            Thread.sleep(5000);

            // 停止worker
            worker.shutdown();
            workerThread.interrupt();

            logger.info("CrawlerWorker NettyHttp 模式测试完成");
            logger.info("Worker 统计信息: {}", worker.getStats());

        } catch (Exception e) {
            logger.error("CrawlerWorker NettyHttp 模式测试失败: {}", e.getMessage(), e);
        }
    }
}
