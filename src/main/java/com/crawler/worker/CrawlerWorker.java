package com.crawler.worker;

import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.crawler.apple.AppleBusinessFlow;
import com.crawler.apple.NettyHttpBusinessFlow;
import com.crawler.apple.NettyHttpStockChecker;
import com.crawler.config.AppleWatchConfig;
import com.crawler.message.CrawlerMessage;
import com.crawler.user.UserProfileManager;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.ColorScheme;
import com.microsoft.playwright.options.Geolocation;

/**
 * 用户专属爬虫工作线程
 * 每个线程绑定一个用户，包含持久的浏览器实例，处理该用户的所有任务
 */
public class CrawlerWorker implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(CrawlerWorker.class);

    /**
     * 工作线程运行模式
     */
    public enum WorkerMode {
        /** 使用 Playwright 驱动浏览器执行任务 */
        PLAYWRIGHT,
        /** 使用 Netty 直接发送 HTTP 请求 */
        NETTY_HTTP,
        /** 混合模式：根据任务类型自动选择最适合的执行方式 */
        HYBRID
    }

    /** 用户名 */
    private final String username;

    /** 消息队列 */
    private final BlockingQueue<CrawlerMessage> messageQueue;

    /** 用户配置管理器 */
    private final UserProfileManager userProfileManager;

    /** 是否无头模式 */
    private final boolean headless;

    /** 工作模式 */
    private final WorkerMode workerMode;

    // 持久的浏览器实例
    private Playwright playwright;
    private Browser browser;
    private volatile boolean browserInitialized = false;

    /** 停止标志 */
    private final AtomicBoolean shouldStop = new AtomicBoolean(false);

    /** 线程状态 */
    private volatile WorkerStatus status = WorkerStatus.STOPPED;

    /** 最后处理的消息时间 */
    private volatile LocalDateTime lastProcessTime;

    /** 处理的消息总数 */
    private volatile long processedMessageCount = 0;

    /** 处理成功的消息数 */
    private volatile long successMessageCount = 0;

    /** 处理失败的消息数 */
    private volatile long failedMessageCount = 0;

    /** 队列轮询超时时间（秒） */
    private static final long POLL_TIMEOUT_SECONDS = 5;

    // Apple Watch 相关功能临时禁用开关（服务层也有一层保护，这里双保险）
    private static final boolean WATCH_FEATURE_ENABLED = false;

    /** Netty HTTP 库存检查客户端 */
    private static final NettyHttpStockChecker NETTY_STOCK_CHECKER = new NettyHttpStockChecker();

    /** 用于跟踪是否检测到514错误 */
    private volatile boolean http514ErrorDetected = false;

    private boolean networkErrorDetection = true;

    // 存储捕获的fulfillment-messages请求headers
    private volatile Map<String, String> capturedFulfillmentHeaders = null;

    public CrawlerWorker(String username, BlockingQueue<CrawlerMessage> messageQueue,
            UserProfileManager userProfileManager, boolean headless) {
        this(username, messageQueue, userProfileManager, headless, WorkerMode.HYBRID);
    }

    public CrawlerWorker(String username, BlockingQueue<CrawlerMessage> messageQueue,
            UserProfileManager userProfileManager, boolean headless, WorkerMode workerMode) {
        this.username = username;
        this.messageQueue = messageQueue;
        this.userProfileManager = userProfileManager;
        this.headless = headless;
        this.workerMode = workerMode != null ? workerMode : WorkerMode.HYBRID;

        if (this.workerMode == WorkerMode.PLAYWRIGHT || this.workerMode == WorkerMode.HYBRID) {
            // PLAYWRIGHT和HYBRID模式都需要浏览器实例
            initializeBrowser();
        }
    }

    /**
     * 初始化持久的浏览器实例
     */
    private synchronized void initializeBrowser() {
        if (workerMode == WorkerMode.NETTY_HTTP) {
            return;
        }
        if (browserInitialized) {
            return;
        }

        try {
            logger.info("用户 {} 初始化浏览器实例...", username);

            // 创建 Playwright 实例
            playwright = Playwright.create();

            // 使用普通的浏览器启动模式，带反检测参数
            BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                    .setHeadless(headless)
                    .setArgs(Arrays.asList(
                            "--disable-blink-features=AutomationControlled",
                            "--start-maximized",
                            "--disable-webrtc"))
                    .setTimeout(120000);

            browser = playwright.chromium().launch(launchOptions);

            browserInitialized = true;
            logger.info("用户 {} 浏览器实例初始化完成", username);

        } catch (Exception e) {
            logger.error("用户 {} 初始化浏览器实例失败: {}", username, e.getMessage(), e);
            browserInitialized = false;
        }
    }

    @Override
    public void run() {
        logger.info("用户线程 {} 启动", username);
        status = WorkerStatus.RUNNING;

        try {
            while (!shouldStop.get() && !Thread.currentThread().isInterrupted()) {
                try {
                    // 从队列中获取消息，带超时以便能响应停止信号
                    CrawlerMessage message = messageQueue.poll(POLL_TIMEOUT_SECONDS, TimeUnit.SECONDS);

                    if (message == null) {
                        // 没有消息，继续轮询
                        status = WorkerStatus.WAITING;
                        continue;
                    }

                    // 只处理属于当前用户的消息
                    if (!username.equals(message.getTargetUsername()) &&
                            message.getTargetType() != CrawlerMessage.TargetType.ALL_USERS &&
                            message.getTargetType() != CrawlerMessage.TargetType.SYSTEM) {
                        // 不是当前用户的消息，重新放回队列
                        messageQueue.offer(message);
                        continue;
                    }

                    // 检查是否是停止消息
                    if (message.getMessageType() == CrawlerMessage.MessageType.STOP_WORKER) {
                        logger.info("用户线程 {} 收到停止信号", username);
                        break;
                    }

                    if (!supportsMessageForCurrentMode(message)) {
                        // 当前线程模式不支持该消息类型，重新入队等待其他线程处理
                        messageQueue.offer(message);
                        status = WorkerStatus.WAITING;
                        try {
                            Thread.sleep(100); // 避免因反复获取同一消息导致忙等
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue;
                    }

                    status = WorkerStatus.PROCESSING;
                    lastProcessTime = LocalDateTime.now();
                    processedMessageCount++;

                    logger.info("用户 {} 开始处理消息: {}", username, message.getMessageType());

                    // 直接处理消息
                    boolean success = processMessage(message);

                    // 统计处理结果
                    if (success) {
                        successMessageCount++;
                    } else {
                        failedMessageCount++;

                        // 如果需要重试且还可以重试，则重新放回队列
                        if (message.canRetry()) {
                            message.incrementRetryCount();
                            messageQueue.offer(message);
                            logger.warn("用户 {} 消息需要重试，已重新放入队列（重试次数: {}/{}）",
                                    username, message.getRetryCount(), message.getMaxRetryCount());
                        }
                    }

                } catch (InterruptedException e) {
                    logger.info("用户线程 {} 被中断", username);
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("用户线程 {} 处理消息时发生未预期异常: {}", username, e.getMessage(), e);
                    // 继续运行，不因为单个异常而停止整个工作线程
                }
            }
        } finally {
            status = WorkerStatus.STOPPED;

            // 关闭浏览器实例
            closeBrowser();

            logger.info("用户线程 {} 已停止，处理统计 - 总计: {}, 成功: {}, 失败: {}",
                    username, processedMessageCount, successMessageCount, failedMessageCount);
        }
    }

    private boolean supportsMessageForCurrentMode(CrawlerMessage message) {
        if (workerMode == WorkerMode.PLAYWRIGHT || workerMode == WorkerMode.HYBRID) {
            return true; // PLAYWRIGHT和HYBRID模式支持所有消息类型
        }

        // 纯NETTY_HTTP模式的支持检查
        CrawlerMessage.MessageType type = message.getMessageType();
        switch (type) {
            case CHECK_IPHONE17PRO_STOCK:
            case HEALTH_CHECK:
            case STOP_WORKER:
            case VALIDATE_SESSION:
                return true;
            case ADD_IPHONE17PRO_TO_CART:
            case CLEAR_SHOPPING_BAG:
            case LOGIN_AND_GENERATE_SESSION:
            case UPDATE_SESSION_COOKIE:
                logger.debug("用户 {} 的纯 NettyHttp 线程对消息类型 {} 支持有限", username, type);
                return true;
            case EXECUTE_PURCHASE_FLOW:
            case ADD_APPLE_WATCH_TO_CART:
                // 这些操作对纯NettyHttp模式来说过于复杂，暂不支持
                logger.debug("用户 {} 的纯 NettyHttp 线程暂不支持复杂操作 {}", username, type);
                return false;
            default:
                logger.debug("用户 {} 的纯 NettyHttp 线程跳过消息类型 {}", username, type);
                return false;
        }
    }

    /**
     * 根据消息类型决定使用哪种执行模式
     * 这是HYBRID模式的核心逻辑
     */
    private ExecutionMode getExecutionModeForMessage(CrawlerMessage.MessageType messageType) {
        switch (messageType) {
            case CHECK_IPHONE17PRO_STOCK:
                // 库存检查使用NettyHttp模式，更高效
                return ExecutionMode.NETTY_HTTP;

            case VALIDATE_SESSION:
                // 会话验证使用NettyHttp模式，快速检查
                return ExecutionMode.NETTY_HTTP;

            case LOGIN_AND_GENERATE_SESSION:
            case UPDATE_SESSION_COOKIE:
                // 登录和会话刷新使用Playwright模式，需要完整的浏览器交互
                return ExecutionMode.PLAYWRIGHT;

            case EXECUTE_PURCHASE_FLOW:
            case ADD_IPHONE17PRO_TO_CART:
            case ADD_APPLE_WATCH_TO_CART:
            case CLEAR_SHOPPING_BAG:
                // 复杂的页面交互使用Playwright模式
                return ExecutionMode.PLAYWRIGHT;

            case HEALTH_CHECK:
            case STOP_WORKER:
            case ORDER_SUBMITTED_LOG:
                // 系统级操作不需要特定模式
                return ExecutionMode.SYSTEM;

            default:
                // 默认使用Playwright模式
                return ExecutionMode.PLAYWRIGHT;
        }
    }

    /**
     * 执行模式枚举（用于HYBRID模式的内部决策）
     */
    private enum ExecutionMode {
        PLAYWRIGHT,
        NETTY_HTTP,
        SYSTEM
    }

    /**
     * 处理单个消息
     */
    private boolean processMessage(CrawlerMessage message) {
        try {
            // 根据工作模式决定执行方式
            ExecutionMode executionMode = determineExecutionMode(message);

            switch (message.getMessageType()) {
                case LOGIN_AND_GENERATE_SESSION:
                    return executionMode == ExecutionMode.NETTY_HTTP ? processLoginNettyHttp(message)
                            : processLogin(message);

                case EXECUTE_PURCHASE_FLOW:
                    return processPurchase(message);

                case UPDATE_SESSION_COOKIE:
                    return executionMode == ExecutionMode.NETTY_HTTP ? processLoginNettyHttp(message)
                            : processLogin(message); // 重新登录就是更新会话

                case ADD_IPHONE17PRO_TO_CART:
                    return executionMode == ExecutionMode.NETTY_HTTP ? processAddToCartNettyHttp(message)
                            : processAddToCart(message);

                case ADD_APPLE_WATCH_TO_CART:
                    return processAddWatchToCart(message);

                case CLEAR_SHOPPING_BAG:
                    return executionMode == ExecutionMode.NETTY_HTTP ? processClearShoppingBagNettyHttp(message)
                            : processClearShoppingBag(message);

                case CHECK_IPHONE17PRO_STOCK:
                    return processStockCheck(message, executionMode);

                case VALIDATE_SESSION:
                    return processValidateSession(message, executionMode);

                case HEALTH_CHECK:
                    logger.info("用户 {} 健康检查通过", username);
                    return true;

                case ORDER_SUBMITTED_LOG:
                    // 仅记录日志（可扩展为持久化到数据库/文件）
                    logger.info("用户 {} 下单完成日志：params={}", username, message.getParameters());
                    return true;

                default:
                    logger.warn("用户 {} 不支持的消息类型: {}", username, message.getMessageType());
                    return false;
            }
        } catch (Exception e) {
            logger.error("用户 {} 处理消息时发生异常: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据工作模式和消息类型确定执行模式
     */
    private ExecutionMode determineExecutionMode(CrawlerMessage message) {
        switch (workerMode) {
            case PLAYWRIGHT:
                return ExecutionMode.PLAYWRIGHT;
            case NETTY_HTTP:
                return ExecutionMode.NETTY_HTTP;
            case HYBRID:
                ExecutionMode recommendedMode = getExecutionModeForMessage(message.getMessageType());
                logger.debug("用户 {} HYBRID模式为消息类型 {} 选择执行模式: {}",
                        username, message.getMessageType(), recommendedMode);
                return recommendedMode;
            default:
                return ExecutionMode.PLAYWRIGHT;
        }
    }

    /**
     * 处理登录任务
     */
    private boolean processLogin(CrawlerMessage message) {
        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始登录生成会话", username);

            context = createContextWithoutSession();
            Page page = context.newPage();

            // 设置网络监听器（如果启用）
            if (networkErrorDetection) {
                setupConsoleAndNetworkListeners(page);
            }

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig(), messageQueue);
            // 设置捕获的headers
            // businessFlow.setCapturedFulfillmentHeaders(capturedFulfillmentHeaders);
            businessFlow.loginAndGenerateSession();

            logger.info("用户 {} 登录会话生成成功", username);

            // // 登录成功后，主动调用一次库存检查来补全httpStockChecker配置
            // try {
            // logger.info("用户 {} 登录后补全库存检查配置...", username);
            // businessFlow.checkIphone17ProStock();

            // // 重新保存会话文件以触发enhanceAuthFile
            // logger.info("用户 {} 重新保存会话文件以补全httpStockChecker配置...", username);
            // Path sessionFile = userProfileManager.getSessionFilePath(username);
            // businessFlow.saveSessionStatePublic(context, sessionFile);

            // logger.info("用户 {} 库存检查配置补全成功", username);
            // } catch (Exception e) {
            // logger.warn("用户 {} 补全库存检查配置失败，但不影响登录结果: {}", username, e.getMessage());
            // }

            return true;

        } catch (Exception e) {
            logger.error("用户 {} 登录失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理购买任务
     */
    private boolean processPurchase(CrawlerMessage message) {
        // 检查会话文件是否存在
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法执行购买", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始执行购买流程", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);

            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig(), messageQueue);
            // 设置捕获的headers
            businessFlow.setCapturedFulfillmentHeaders(capturedFulfillmentHeaders);

            int repeatCount = Math.max(1, resolveRepeatOrderCount(message));

            for (int attempt = 1; attempt <= repeatCount; attempt++) {
                if (attempt > 1) {
                    logger.info("用户 {} 根据配置开始第 {}/{} 次重复下单", username, attempt, repeatCount);
                } else {
                    logger.info("用户 {} 开始执行第 {}/{} 次下单", username, attempt, repeatCount);
                }

                businessFlow.executePurchaseFlow();
                logger.info("用户 {} 第 {}/{} 次下单流程执行完成", username, attempt, repeatCount);
            }

            logger.info("用户 {} 购买流程全部执行完成，共 {} 次", username, repeatCount);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} 购买流程失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    private int resolveRepeatOrderCount(CrawlerMessage message) {
        Integer repeatCount = null;

        if (message != null) {
            repeatCount = message.getParameter(CrawlerMessage.PARAM_REPEAT_ORDER_COUNT, Integer.class);
        }

        if (repeatCount != null && repeatCount > 0) {
            return repeatCount;
        }

        try {
            UserProfileManager.UserProfile profile = userProfileManager.loadUserProfile(username);
            if (profile != null && profile.getTasks() != null && profile.getTasks().getPurchaseFlow() != null) {
                Integer configured = profile.getTasks().getPurchaseFlow().getRepeatOrderCount();
                if (configured != null && configured > 0) {
                    return configured;
                }
            }
        } catch (Exception e) {
            logger.warn("读取用户 {} 重复下单配置失败: {}", username, e.getMessage());
        }

        return 1;
    }

    /**
     * 处理加入购物车任务
     */
    private boolean processAddToCart(CrawlerMessage message) {
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法加入购物车", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始执行加入购物车流程", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig(), messageQueue);
            // 设置捕获的headers
            businessFlow.setCapturedFulfillmentHeaders(capturedFulfillmentHeaders);
            businessFlow.addIphone17ProToCart();

            logger.info("用户 {} 加入购物车流程执行完成", username);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} 加入购物车流程失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理 Apple Watch 加入购物车任务
     */
    private boolean processAddWatchToCart(CrawlerMessage message) {
        if (!WATCH_FEATURE_ENABLED) {
            logger.warn("用户 {} 的 Apple Watch 加购流程已被临时禁用，跳过执行", username);
            return false;
        }
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法加入手表购物车", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始执行 Apple Watch 加入购物车流程", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig(), messageQueue);
            // 设置捕获的headers
            businessFlow.setCapturedFulfillmentHeaders(capturedFulfillmentHeaders);
            AppleWatchConfig watchConfig = message.getWatchConfig();
            if (watchConfig == null) {
                logger.error("用户 {} 未配置 Apple Watch 购买信息，无法执行加购", username);
                return false;
            }

            businessFlow.addAppleWatchUltraToCart(watchConfig);

            logger.info("用户 {} Apple Watch 加入购物车流程执行完成", username);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} Apple Watch 加入购物车流程失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理清空购物袋任务
     */
    private boolean processClearShoppingBag(CrawlerMessage message) {
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法清空购物袋", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始执行清空购物袋流程", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig(), messageQueue);
            // 设置捕获的headers
            businessFlow.setCapturedFulfillmentHeaders(capturedFulfillmentHeaders);
            businessFlow.clearShoppingBag();

            logger.info("用户 {} 清空购物袋流程执行完成", username);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} 清空购物袋流程失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理库存检查任务
     * 支持根据执行模式选择NettyHttp或Playwright方式
     */
    private boolean processStockCheck(CrawlerMessage message, ExecutionMode executionMode) {
        // 检查会话文件是否存在
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法检查库存", username);
            return false;
        }

        Path sessionPath = userProfileManager.getSessionFilePath(username);

        try {
            String modeDesc = executionMode == ExecutionMode.NETTY_HTTP ? "NettyHttp" : "Playwright";
            logger.info("用户 {} 使用 {} 模式检查 iPhone17Pro 库存", username, modeDesc);

            if (executionMode == ExecutionMode.NETTY_HTTP) {
                // 使用NettyHttp模式进行库存检查
                return processStockCheckNettyHttp(sessionPath);
            } else {
                // 使用Playwright模式进行库存检查（如果需要的话）
                logger.warn("用户 {} Playwright模式的库存检查功能待实现，回退到NettyHttp模式", username);
                return processStockCheckNettyHttp(sessionPath);
            }

        } catch (Exception e) {
            logger.error("用户 {} 库存检查失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用NettyHttp模式进行库存检查
     */
    private boolean processStockCheckNettyHttp(Path sessionPath) {
        try {
            Map<String, String> headerOverrides = capturedFulfillmentHeaders;
            NettyHttpStockChecker.StockResult stockResult = NETTY_STOCK_CHECKER.checkStock(sessionPath,
                    headerOverrides);

            if (stockResult == null) {
                logger.error("用户 {} 库存检查返回空结果", username);
                return false;
            }

            logger.debug("库存详情:\n{}", stockResult.getMessage());

            if (stockResult.hasStock()) {
                logger.warn("🎉 重要！用户 {} 的iPhone17Pro有库存了！耗时 {}ms", username, stockResult.getDurationMs());
                if (stockResult.getMessage() != null && !stockResult.getMessage().isEmpty()) {
                    logger.warn("库存详情:\n{}", stockResult.getMessage());
                }

                // 发现库存时，向所有用户派发购买任务
                try {
                    List<com.crawler.user.UserProfileManager.UserProfile> enabledUsers = userProfileManager
                            .getAllEnabledUserProfiles();

                    int successCount = 0;
                    for (var profile : enabledUsers) {
                        Map<String, Object> parameters = resolveRepeatOrderParameters(profile);

                        CrawlerMessage.Builder builder = new CrawlerMessage.Builder()
                                .messageType(CrawlerMessage.MessageType.EXECUTE_PURCHASE_FLOW)
                                .targetSpecificUser(profile.getUsername())
                                .userConfig(profile.getUserConfig())
                                .iphoneConfig(profile.getIphoneConfig())
                                .pickupLocationConfig(profile.getPickupLocationConfig())
                                .priority(3);

                        if (parameters != null && !parameters.isEmpty()) {
                            builder.parameters(parameters);
                        }

                        CrawlerMessage purchaseMsg = builder.build();

                        if (messageQueue.offer(purchaseMsg)) {
                            successCount++;
                        } else {
                            logger.warn("将购买任务提交到队列失败，用户: {}", profile.getUsername());
                        }
                    }
                    logger.warn("📤 已向所有启用用户派发购买任务，共 {} 个", successCount);
                } catch (Exception dispatchEx) {
                    logger.error("派发购买任务给所有用户失败: {}", dispatchEx.getMessage(), dispatchEx);
                }

            } else {
                logger.info("用户 {} iPhone17Pro暂时无库存，耗时 {}ms", username, stockResult.getDurationMs());
            }

            return true;

        } catch (Exception e) {
            // 检查是否是因为缺少httpStockChecker配置导致的失败
            if (e.getMessage() != null && e.getMessage().contains("会话文件缺少 httpStockChecker 配置")) {
                logger.warn("用户 {} NettyHttp模式库存检查失败，缺少httpStockChecker配置，自动发送Playwright模式补全任务", username);
                return handleMissingHttpStockCheckerConfig();
            }

            logger.error("用户 {} 库存检查失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理缺少httpStockChecker配置的情况
     * 发送一个Playwright模式的库存检查任务来补全配置
     */
    private boolean handleMissingHttpStockCheckerConfig() {
        try {
            logger.info("用户 {} 开始使用Playwright模式补全httpStockChecker配置", username);

            // 获取用户配置信息
            UserProfileManager.UserProfile userProfile = userProfileManager.loadUserProfile(username);
            if (userProfile == null) {
                logger.error("用户 {} 配置不存在，无法补全httpStockChecker配置", username);
                return false;
            }

            // 创建一个Playwright模式的库存检查消息
            CrawlerMessage playwrightStockCheckMessage = new CrawlerMessage.Builder()
                    .messageType(CrawlerMessage.MessageType.CHECK_IPHONE17PRO_STOCK)
                    .targetSpecificUser(username)
                    .userConfig(userProfile.getUserConfig())
                    .iphoneConfig(userProfile.getIphoneConfig())
                    .pickupLocationConfig(userProfile.getPickupLocationConfig())
                    .priority(0) // 高优先级，立即处理
                    .parameters(Map.of("forcePlaywrightMode", true, "configEnhancement", true))
                    .build();

            // 直接在当前线程中执行Playwright模式的库存检查来补全配置
            boolean configEnhanced = processStockCheckPlaywright(playwrightStockCheckMessage);

            if (configEnhanced) {
                logger.info("用户 {} httpStockChecker配置补全成功，重试NettyHttp模式库存检查", username);

                // 配置补全成功后，重试NettyHttp模式的库存检查
                Path sessionPath = userProfileManager.getSessionFilePath(username);
                return processStockCheckNettyHttpRetry(sessionPath);
            } else {
                logger.warn("用户 {} httpStockChecker配置补全失败", username);
                return false;
            }

        } catch (Exception e) {
            logger.error("用户 {} 处理缺少httpStockChecker配置时发生异常: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 重试NettyHttp模式的库存检查（配置补全后）
     */
    private boolean processStockCheckNettyHttpRetry(Path sessionPath) {
        try {
            logger.info("用户 {} 重试NettyHttp模式库存检查", username);
            Map<String, String> headerOverrides = capturedFulfillmentHeaders;
            NettyHttpStockChecker.StockResult stockResult = NETTY_STOCK_CHECKER.checkStock(sessionPath,
                    headerOverrides);

            if (stockResult == null) {
                logger.error("用户 {} 重试库存检查返回空结果", username);
                return false;
            }

            logger.debug("重试库存详情:\n{}", stockResult.getMessage());

            if (stockResult.hasStock()) {
                logger.warn("🎉 重要！用户 {} 的iPhone17Pro有库存了！（重试成功）耗时 {}ms", username, stockResult.getDurationMs());
                if (stockResult.getMessage() != null && !stockResult.getMessage().isEmpty()) {
                    logger.warn("库存详情:\n{}", stockResult.getMessage());
                }
                // 这里可以添加库存发现后的处理逻辑
            } else {
                logger.info("用户 {} iPhone17Pro暂时无库存（重试结果），耗时 {}ms", username, stockResult.getDurationMs());
            }

            return true;

        } catch (Exception e) {
            logger.error("用户 {} 重试NettyHttp库存检查失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用Playwright模式进行库存检查（用于补全httpStockChecker配置）
     */
    private boolean processStockCheckPlaywright(CrawlerMessage message) {
        BrowserContext context = null;

        try {
            logger.info("用户 {} 使用Playwright模式进行库存检查以补全配置", username);

            // 创建带会话的浏览器上下文
            Path sessionFile = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionFile);
            if (context == null) {
                logger.error("用户 {} 创建浏览器上下文失败", username);
                return false;
            }

            Page page = context.newPage();

            // 设置网络监听器来捕获fulfillment-messages请求
            setupStockCheckNetworkListener(page);

            // 创建AppleBusinessFlow并执行库存检查
            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig(), messageQueue);

            // 设置已捕获的headers（如果有的话）
            businessFlow.setCapturedFulfillmentHeaders(capturedFulfillmentHeaders);

            // 执行库存检查
            boolean stockCheckResult = businessFlow.checkIphone17ProStock();

            // 重新保存会话文件以触发enhanceAuthFile
            logger.info("用户 {} 重新保存会话文件以补全httpStockChecker配置", username);
            businessFlow.saveSessionStatePublic(context, sessionFile);

            logger.info("用户 {} Playwright模式库存检查完成，配置补全状态: {}", username,
                    capturedFulfillmentHeaders != null ? "成功" : "部分成功");

            return true; // 返回true表示配置补全过程完成，不管库存结果如何

        } catch (Exception e) {
            logger.error("用户 {} Playwright模式库存检查失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭浏览器上下文失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 设置库存检查的网络监听器
     */
    private void setupStockCheckNetworkListener(Page page) {
        page.onRequest(request -> {
            String url = request.url();
            if (url.contains("fulfillment-messages")) {
                Map<String, String> headers = request.headers();
                if (headers.containsKey("x-aos-ui-fetch-call-1")) {
                    capturedFulfillmentHeaders = headers;
                    logger.info("用户 {} 成功捕获fulfillment-messages请求headers，x-aos-ui-fetch-call-1: {}",
                            username, headers.get("x-aos-ui-fetch-call-1"));
                }
            }
        });
    }

    private Map<String, Object> resolveRepeatOrderParameters(UserProfileManager.UserProfile profile) {
        if (profile == null || profile.getTasks() == null || profile.getTasks().getPurchaseFlow() == null) {
            return null;
        }

        Integer repeatCount = profile.getTasks().getPurchaseFlow().getRepeatOrderCount();
        if (repeatCount == null || repeatCount <= 1) {
            return null;
        }

        Map<String, Object> params = new HashMap<>();
        params.put(CrawlerMessage.PARAM_REPEAT_ORDER_COUNT, repeatCount);
        return params;
    }

    /**
     * 处理会话验证任务
     * 支持根据执行模式选择NettyHttp或Playwright方式
     */
    private boolean processValidateSession(CrawlerMessage message, ExecutionMode executionMode) {
        try {
            String modeDesc = executionMode == ExecutionMode.NETTY_HTTP ? "NettyHttp" : "Playwright";
            logger.debug("用户 {} 使用 {} 模式验证会话", username, modeDesc);

            if (executionMode == ExecutionMode.NETTY_HTTP) {
                // 使用NettyHttp模式进行会话验证
                return processValidateSessionNettyHttp();
            } else {
                // 使用Playwright模式进行会话验证（基本检查）
                return userProfileManager.userSessionExists(username);
            }

        } catch (Exception e) {
            logger.error("用户 {} 会话验证失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用NettyHttp模式进行会话验证
     */
    private boolean processValidateSessionNettyHttp() {
        try {
            if (!userProfileManager.userSessionExists(username)) {
                return false;
            }

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            NettyHttpBusinessFlow businessFlow = new NettyHttpBusinessFlow(
                    username, null, null, null);

            // 使用NettyHttp验证会话有效性
            boolean isValid = businessFlow.validateSession(sessionPath)
                    .get(5, java.util.concurrent.TimeUnit.SECONDS);

            logger.debug("用户 {} NettyHttp会话验证结果: {}", username, isValid ? "有效" : "无效");
            return isValid;

        } catch (Exception e) {
            logger.error("用户 {} NettyHttp会话验证失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建带会话的浏览器上下文
     */
    private BrowserContext createContextWithSession(Path sessionPath) {
        ensureBrowserAvailable();

        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setStorageStatePath(sessionPath)
                .setViewportSize(null);

        // 应用真实环境参数
        String realUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                + "AppleWebKit/537.36 (KHTML, like Gecko) "
                + "Chrome/********* Safari/537.36";
        contextOptions
                .setUserAgent(realUserAgent)
                .setLocale("zh-CN")
                .setTimezoneId("Asia/Shanghai")
                .setColorScheme(ColorScheme.LIGHT)
                .setExtraHTTPHeaders(Map.of(
                        "Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8",
                        "Sec-CH-UA-Platform", "\"macOS\""))
                .setGeolocation(new Geolocation(31.2304, 121.4737));

        BrowserContext context = browser.newContext(contextOptions);

        // 添加反检测脚本
        context.addInitScript("Object.defineProperty(navigator, 'webdriver', { get: () => false });");
        context.grantPermissions(Arrays.asList("geolocation", "notifications"));

        return context;
    }

    /**
     * 创建无会话的浏览器上下文
     */
    private BrowserContext createContextWithoutSession() {
        ensureBrowserAvailable();

        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setViewportSize(null);

        // 应用真实环境参数
        String realUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                + "AppleWebKit/537.36 (KHTML, like Gecko) "
                + "Chrome/********* Safari/537.36";
        contextOptions
                .setUserAgent(realUserAgent)
                .setLocale("zh-CN")
                .setTimezoneId("Asia/Shanghai")
                .setColorScheme(ColorScheme.LIGHT)
                .setExtraHTTPHeaders(Map.of(
                        "Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8",
                        "Sec-CH-UA-Platform", "\"macOS\""))
                .setGeolocation(new Geolocation(31.2304, 121.4737));

        BrowserContext context = browser.newContext(contextOptions);

        // 添加反检测脚本
        context.addInitScript("Object.defineProperty(navigator, 'webdriver', { get: () => false });");
        context.grantPermissions(Arrays.asList("geolocation", "notifications"));

        return context;
    }

    /**
     * 设置控制台消息和网络请求监听器
     * 
     * @param page 页面对象
     */
    private void setupConsoleAndNetworkListeners(Page page) {
        // 监听控制台消息
        page.onConsoleMessage(msg -> {
            String text = msg.text();
            if (text.contains("514") || text.contains("Server Error") || text.contains("服务器错误")) {
                logger.warn("控制台检测到服务器错误: {}", text);
                http514ErrorDetected = true;
            }
        });

        // 监听网络请求，捕获fulfillment-messages请求的headers
        page.onRequest(request -> {
            String url = request.url();
            if (url.contains("fulfillment-messages") && url.contains("MYEV3CH")) {
                // 捕获fulfillment-messages请求的headers
                Map<String, String> headers = request.headers();
                if (headers.containsKey("x-aos-ui-fetch-call-1")) {
                    capturedFulfillmentHeaders = headers;
                    logger.info("成功捕获fulfillment-messages请求headers，x-aos-ui-fetch-call-1: {}",
                            headers.get("x-aos-ui-fetch-call-1"));
                }
            }
        });

        // 监听网络响应
        page.onResponse(response -> {
            int status = response.status();
            String url = response.url();

            if (status == 514) {
                logger.warn("检测到HTTP 514错误，URL: {}", url);
                http514ErrorDetected = true;
            } else if (status >= 500) {
                logger.warn("检测到服务器错误 {}: {}", status, url);
                // 其他5xx错误也可能需要刷新
                if (url.contains("apple.com") && (status == 502 || status == 503 || status == 504)) {
                    http514ErrorDetected = true;
                }
            }
        });

        // 监听网络请求失败
        page.onRequestFailed(request -> {
            String failure = request.failure();
            String url = request.url();
            logger.warn("网络请求失败: {} - {}", url, failure);

            // 如果是关键请求失败，也标记需要刷新
            if (url.contains("apple.com") && (failure.contains("timeout") || failure.contains("connection"))) {
                logger.warn("关键请求失败，标记需要刷新");
                http514ErrorDetected = true;
            }
        });

        logger.info("控制台和网络监听器已设置");
    }

    /**
     * 确保浏览器实例可用
     */
    private void ensureBrowserAvailable() {
        if (workerMode == WorkerMode.NETTY_HTTP) {
            return;
        }
        if (!browserInitialized || playwright == null || browser == null) {
            logger.warn("用户 {} 浏览器实例不可用，重新初始化", username);
            closeBrowser();
            initializeBrowser();
        }
    }

    /**
     * 优雅停止工作线程
     */
    public void shutdown() {
        logger.info("请求停止用户线程 {}", username);
        shouldStop.set(true);
    }

    /**
     * 强制停止工作线程
     */
    public void forceShutdown() {
        logger.warn("强制停止用户线程 {}", username);
        shouldStop.set(true);

        // 关闭浏览器实例
        closeBrowser();

        Thread.currentThread().interrupt();
    }

    /**
     * 关闭持久浏览器实例
     */
    private synchronized void closeBrowser() {
        if (workerMode == WorkerMode.NETTY_HTTP) {
            return;
        }
        logger.info("用户 {} 关闭浏览器实例...", username);

        if (browser != null) {
            try {
                browser.close();
            } catch (Exception e) {
                logger.warn("关闭浏览器时出错: {}", e.getMessage());
            }
            browser = null;
        }

        if (playwright != null) {
            try {
                playwright.close();
            } catch (Exception e) {
                logger.warn("关闭 Playwright 时出错: {}", e.getMessage());
            }
            playwright = null;
        }

        browserInitialized = false;
        logger.info("用户 {} 浏览器实例已关闭", username);
    }

    // Getters
    public String getUsername() {
        return username;
    }

    public String getWorkerId() {
        return username; // 现在工作线程ID就是用户名
    }

    public WorkerStatus getStatus() {
        return status;
    }

    public LocalDateTime getLastProcessTime() {
        return lastProcessTime;
    }

    public long getProcessedMessageCount() {
        return processedMessageCount;
    }

    public long getSuccessMessageCount() {
        return successMessageCount;
    }

    public long getFailedMessageCount() {
        return failedMessageCount;
    }

    public int getQueueSize() {
        return messageQueue.size();
    }

    /**
     * 获取工作线程统计信息
     */
    public WorkerStats getStats() {
        return new WorkerStats(
                username,
                status,
                processedMessageCount,
                successMessageCount,
                failedMessageCount,
                lastProcessTime,
                messageQueue.size());
    }

    /**
     * NettyHttp模式：处理登录任务
     */
    private boolean processLoginNettyHttp(CrawlerMessage message) {
        try {
            logger.info("用户 {} 使用 NettyHttp 模式处理登录任务", username);

            // NettyHttp模式下，登录操作比较复杂，需要处理表单提交、验证码等
            // 目前暂不支持完整的登录流程，建议使用 Playwright 模式进行登录
            logger.warn("用户 {} NettyHttp 模式暂不支持复杂登录操作，建议使用 Playwright 模式", username);

            // 可以在这里添加简化的会话验证逻辑
            Path sessionPath = userProfileManager.getSessionFilePath(username);
            NettyHttpBusinessFlow businessFlow = new NettyHttpBusinessFlow(
                    username, message.getUserConfig(), message.getIphoneConfig(), message.getPickupLocationConfig());

            // 验证现有会话是否有效
            try {
                boolean isValid = businessFlow.validateSession(sessionPath).get(10,
                        java.util.concurrent.TimeUnit.SECONDS);
                if (isValid) {
                    logger.info("用户 {} 现有会话仍然有效", username);
                    return true;
                } else {
                    logger.warn("用户 {} 现有会话已失效，需要重新登录", username);
                    return false;
                }
            } catch (Exception e) {
                logger.error("用户 {} 验证会话时出错: {}", username, e.getMessage());
                return false;
            }

        } catch (Exception e) {
            logger.error("用户 {} NettyHttp 登录失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * NettyHttp模式：处理加入购物车任务
     */
    private boolean processAddToCartNettyHttp(CrawlerMessage message) {
        try {
            logger.info("用户 {} 使用 NettyHttp 模式处理加入购物车任务", username);

            // 检查会话文件是否存在
            if (!userProfileManager.userSessionExists(username)) {
                logger.error("用户 {} 缺少会话文件，无法执行 NettyHttp 加购物车", username);
                return false;
            }

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            NettyHttpBusinessFlow businessFlow = new NettyHttpBusinessFlow(
                    username, message.getUserConfig(), message.getIphoneConfig(), message.getPickupLocationConfig());

            // 首先验证会话是否有效
            try {
                boolean isValid = businessFlow.validateSession(sessionPath).get(5,
                        java.util.concurrent.TimeUnit.SECONDS);
                if (!isValid) {
                    logger.error("用户 {} 会话已失效，无法执行加购物车操作", username);
                    return false;
                }
            } catch (Exception e) {
                logger.error("用户 {} 验证会话时出错: {}", username, e.getMessage());
                return false;
            }

            // NettyHttp模式下的加购物车操作需要调用Apple的购物车API
            // 这需要分析Apple网站的API调用模式，目前暂不支持
            logger.warn("用户 {} NettyHttp 模式的加购物车功能正在开发中，建议使用 Playwright 模式", username);
            return false;

        } catch (Exception e) {
            logger.error("用户 {} NettyHttp 加购物车失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * NettyHttp模式：处理清空购物袋任务
     */
    private boolean processClearShoppingBagNettyHttp(CrawlerMessage message) {
        try {
            logger.info("用户 {} 使用 NettyHttp 模式处理清空购物袋任务", username);

            // 检查会话文件是否存在
            if (!userProfileManager.userSessionExists(username)) {
                logger.error("用户 {} 缺少会话文件，无法执行 NettyHttp 清空购物袋", username);
                return false;
            }

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            NettyHttpBusinessFlow businessFlow = new NettyHttpBusinessFlow(
                    username, message.getUserConfig(), message.getIphoneConfig(), message.getPickupLocationConfig());

            // 首先验证会话是否有效
            try {
                boolean isValid = businessFlow.validateSession(sessionPath).get(5,
                        java.util.concurrent.TimeUnit.SECONDS);
                if (!isValid) {
                    logger.error("用户 {} 会话已失效，无法执行清空购物袋操作", username);
                    return false;
                }
            } catch (Exception e) {
                logger.error("用户 {} 验证会话时出错: {}", username, e.getMessage());
                return false;
            }

            // NettyHttp模式下的清空购物袋操作需要调用Apple的购物车API
            // 这需要分析Apple网站的API调用模式，目前暂不支持
            logger.warn("用户 {} NettyHttp 模式的清空购物袋功能正在开发中，建议使用 Playwright 模式", username);
            return false;

        } catch (Exception e) {
            logger.error("用户 {} NettyHttp 清空购物袋失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String toString() {
        return "CrawlerWorker{" +
                "username='" + username + '\'' +
                ", workerMode=" + workerMode +
                ", status=" + status +
                ", processedMessages=" + processedMessageCount +
                ", queueSize=" + messageQueue.size() +
                '}';
    }

    /**
     * 工作线程状态枚举
     */
    public enum WorkerStatus {
        /** 已停止 */
        STOPPED,
        /** 运行中 */
        RUNNING,
        /** 等待消息 */
        WAITING,
        /** 处理消息中 */
        PROCESSING
    }

    /**
     * 工作线程统计信息
     */
    public static class WorkerStats {
        private final String workerId;
        private final WorkerStatus status;
        private final long processedMessageCount;
        private final long successMessageCount;
        private final long failedMessageCount;
        private final LocalDateTime lastProcessTime;
        private final int queueSize;

        public WorkerStats(String workerId, WorkerStatus status, long processedMessageCount,
                long successMessageCount, long failedMessageCount,
                LocalDateTime lastProcessTime, int queueSize) {
            this.workerId = workerId;
            this.status = status;
            this.processedMessageCount = processedMessageCount;
            this.successMessageCount = successMessageCount;
            this.failedMessageCount = failedMessageCount;
            this.lastProcessTime = lastProcessTime;
            this.queueSize = queueSize;
        }

        // Getters
        public String getWorkerId() {
            return workerId;
        }

        public WorkerStatus getStatus() {
            return status;
        }

        public long getProcessedMessageCount() {
            return processedMessageCount;
        }

        public long getSuccessMessageCount() {
            return successMessageCount;
        }

        public long getFailedMessageCount() {
            return failedMessageCount;
        }

        public LocalDateTime getLastProcessTime() {
            return lastProcessTime;
        }

        public int getQueueSize() {
            return queueSize;
        }

        @Override
        public String toString() {
            return "WorkerStats{" +
                    "workerId='" + workerId + '\'' +
                    ", status=" + status +
                    ", processed=" + processedMessageCount +
                    ", success=" + successMessageCount +
                    ", failed=" + failedMessageCount +
                    ", queueSize=" + queueSize +
                    ", lastProcess=" + lastProcessTime +
                    '}';
        }
    }
}
