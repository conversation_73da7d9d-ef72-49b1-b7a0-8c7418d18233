package com.crawler.apple;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandler;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.pool.ChannelHealthChecker;
import io.netty.channel.pool.FixedChannelPool;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.pool.ChannelPoolHandler;
import io.netty.handler.codec.http.DefaultFullHttpRequest;
import io.netty.handler.codec.http.FullHttpResponse;
import io.netty.handler.codec.http.HttpClientCodec;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpHeaderValues;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpVersion;
import io.netty.handler.codec.http.HttpContentDecompressor;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.channel.pool.FixedChannelPool.AcquireTimeoutAction;
import io.netty.util.CharsetUtil;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.Promise;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 可复用的 Netty HTTP 库存查询器。
 * <p>
 * 读取用户会话文件中的 httpStockChecker 配置，并使用 Netty 实现高并发、可复用连接的库存检查。
 */
public class NettyHttpStockChecker {

    private static final Logger logger = LoggerFactory.getLogger(NettyHttpStockChecker.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final NioEventLoopGroup EVENT_LOOP_GROUP = new NioEventLoopGroup(Math.max(2, Runtime.getRuntime().availableProcessors()));

    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                EVENT_LOOP_GROUP.shutdownGracefully(0, 3, TimeUnit.SECONDS).syncUninterruptibly();
            } catch (Exception ignored) {
            }
        }));
    }

    private static final Map<InetSocketAddress, FixedChannelPool> POOLS = new ConcurrentHashMap<>();

    /**
     * 同步库存查询方法，默认超时时间 20 秒。
     */
    public StockResult checkStock(Path sessionFile,
                                  Map<String, String> headerOverrides) throws Exception {
        return checkStock(sessionFile, headerOverrides, 20, TimeUnit.SECONDS);
    }

    /**
     * 可指定超时时间的同步库存查询方法。
     */
    public StockResult checkStock(Path sessionFile,
                                  Map<String, String> headerOverrides,
                                  long timeout,
                                  TimeUnit unit) throws Exception {
        return checkStockAsync(sessionFile, headerOverrides)
                .get(timeout, unit);
    }

    /**
     * 异步库存查询方法。
     */
    public CompletableFuture<StockResult> checkStockAsync(Path sessionFile,
                                                          Map<String, String> headerOverrides) throws Exception {
        RequestParameters requestParameters = loadRequestParameters(sessionFile, headerOverrides);
        URI uri = URI.create(requestParameters.url());
        boolean ssl = "https".equalsIgnoreCase(uri.getScheme());
        int port = uri.getPort() > 0 ? uri.getPort() : (ssl ? 443 : 80);
        String host = Objects.requireNonNull(uri.getHost(), "URL 缺少 host");

        Instant start = Instant.now();
        logger.info("🔍 使用 Netty 检查库存，用户会话文件: {}", sessionFile);
        logger.debug("请求 URL: {}", requestParameters.url());

        return performRequest(host, port, ssl, uri, requestParameters)
                .thenApply(payload -> {
                    long durationMs = Duration.between(start, payload.endTime()).toMillis();
                    logger.info("📊 Netty 返回状态码: {}，耗时: {}ms", payload.status(), durationMs);
                    if (payload.status() == 200) {
                        return parseStockResponse(payload.body(), durationMs);
                    }
                    return new StockResult(false, "请求失败: HTTP " + payload.status(), null, durationMs);
                });
    }

    private RequestParameters loadRequestParameters(Path sessionFile,
                                                    Map<String, String> headerOverrides) throws IOException {
        if (sessionFile == null || !Files.exists(sessionFile)) {
            throw new IOException("会话文件不存在: " + sessionFile);
        }

        JsonNode rootNode;
        try (var reader = Files.newBufferedReader(sessionFile)) {
            rootNode = objectMapper.readTree(reader);
        }

        JsonNode httpNode = rootNode.path("httpStockChecker");
        if (httpNode.isMissingNode() || !httpNode.isObject()) {
            throw new IllegalStateException("会话文件缺少 httpStockChecker 配置: " + sessionFile);
        }

        String url = requireText(httpNode, "url");
        String referer = requireText(httpNode, "referer");
        String userAgent = requireText(httpNode, "userAgent");

        Map<String, String> headers = extractHeaders(httpNode);
        if (headerOverrides != null && !headerOverrides.isEmpty()) {
            headerOverrides.forEach((key, value) -> {
                if (key == null || value == null) {
                    return;
                }
                if (isManagedHeader(key)) {
                    return;
                }
                headers.put(key, value);
            });
        }

        String cookieHeader = buildCookieHeader(rootNode.path("cookies"));
        return new RequestParameters(url, referer, userAgent, headers, cookieHeader);
    }

    private static String requireText(JsonNode node, String fieldName) {
        JsonNode valueNode = node.get(fieldName);
        if (valueNode == null || !valueNode.isTextual()) {
            throw new IllegalStateException("httpStockChecker 缺少必填字段: " + fieldName);
        }
        return valueNode.asText();
    }

    private static Map<String, String> extractHeaders(JsonNode httpNode) {
        JsonNode headersNode = httpNode.get("headers");
        if (headersNode == null || !headersNode.isObject()) {
            throw new IllegalStateException("httpStockChecker.headers 必须存在且为对象");
        }

        Map<String, String> headers = new LinkedHashMap<>();
        Iterator<Map.Entry<String, JsonNode>> fields = headersNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String headerName = entry.getKey();
            if (isManagedHeader(headerName)) {
                continue;
            }
            headers.put(headerName, entry.getValue().asText());
        }
        return headers;
    }

    private static boolean isManagedHeader(String headerName) {
        String normalized = headerName.toLowerCase();
        return "cookie".equals(normalized)
                || "referer".equals(normalized)
                || "user-agent".equals(normalized)
                || "host".equals(normalized)
                || "connection".equals(normalized);
    }

    private static String buildCookieHeader(JsonNode cookiesNode) {
        if (cookiesNode == null || !cookiesNode.isArray()) {
            throw new IllegalStateException("apple-auth.json 缺少 cookies 数组");
        }
        StringBuilder cookieHeader = new StringBuilder();
        for (JsonNode cookie : cookiesNode) {
            JsonNode nameNode = cookie.get("name");
            JsonNode valueNode = cookie.get("value");
            if (nameNode == null || valueNode == null) {
                continue;
            }
            if (cookieHeader.length() > 0) {
                cookieHeader.append("; ");
            }
            cookieHeader.append(nameNode.asText()).append("=").append(valueNode.asText());
        }
        if (cookieHeader.length() == 0) {
            throw new IllegalStateException("会话文件中未找到可用的 Cookie");
        }
        return cookieHeader.toString();
    }

    private CompletableFuture<ResponsePayload> performRequest(String host,
                                                               int port,
                                                               boolean ssl,
                                                               URI uri,
                                                               RequestParameters params) throws SSLException {
        InetSocketAddress address = new InetSocketAddress(host, port);
        FixedChannelPool pool = POOLS.computeIfAbsent(address, key -> newPool(host, port, ssl));

        CompletableFuture<ResponsePayload> outer = new CompletableFuture<>();
        Future<Channel> acquireFuture = pool.acquire();

        acquireFuture.addListener(f -> {
            if (!f.isSuccess()) {
                outer.completeExceptionally(f.cause());
                return;
            }
            Channel ch = (Channel) acquireFuture.getNow();

            Promise<ResponsePayload> promise = ch.eventLoop().newPromise();
            ChannelInboundHandler responseHandler = new SimpleChannelInboundHandler<FullHttpResponse>() {
                @Override
                protected void channelRead0(ChannelHandlerContext ctx, FullHttpResponse msg) {
                    ByteBuf content = msg.content();
                    String body = content.toString(CharsetUtil.UTF_8);
                    int status = msg.status().code();
                    promise.trySuccess(new ResponsePayload(status, body, Instant.now()));
                }

                @Override
                public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
                    promise.tryFailure(cause);
                }
            };

            String handlerName = "respHandler-" + System.identityHashCode(responseHandler);
            ch.pipeline().addLast(handlerName, responseHandler);

            String path = uri.getRawPath() == null || uri.getRawPath().isEmpty() ? "/" : uri.getRawPath();
            if (uri.getRawQuery() != null && !uri.getRawQuery().isEmpty()) {
                path += "?" + uri.getRawQuery();
            }

            DefaultFullHttpRequest req = new DefaultFullHttpRequest(HttpVersion.HTTP_1_1, HttpMethod.GET, path);
            HttpHeaders headers = req.headers();
            headers.set(HttpHeaderNames.HOST, host);
            headers.set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);

            for (Map.Entry<String, String> e : params.headers().entrySet()) {
                headers.set(e.getKey(), e.getValue());
            }
            headers.set(HttpHeaderNames.COOKIE, params.cookieHeader());
            headers.set(HttpHeaderNames.REFERER, params.referer());
            headers.set(HttpHeaderNames.USER_AGENT, params.userAgent());

            ChannelFuture writeFuture = ch.writeAndFlush(req);

            long requestTimeoutSec = 15;
            ch.eventLoop().schedule(() -> {
                if (!promise.isDone()) {
                    promise.tryFailure(new java.util.concurrent.TimeoutException("request timeout"));
                }
            }, requestTimeoutSec, TimeUnit.SECONDS);

            promise.addListener(p -> {
                try {
                    ch.pipeline().remove(responseHandler);
                } catch (Exception ignored) {
                }

                if (p.isSuccess()) {
                    outer.complete((ResponsePayload) p.get());
                } else {
                    outer.completeExceptionally(p.cause());
                }

                pool.release(ch);
            });

            writeFuture.addListener(wf -> {
                if (!wf.isSuccess()) {
                    promise.tryFailure(wf.cause());
                }
            });
        });

        return outer;
    }

    private FixedChannelPool newPool(String host, int port, boolean ssl) {
        try {
            final SslContext sslCtx = ssl ? SslContextBuilder.forClient().build() : null;

            Bootstrap bootstrap = new Bootstrap()
                    .group(EVENT_LOOP_GROUP)
                    .channel(NioSocketChannel.class)
                    .remoteAddress(host, port)
                    .option(io.netty.channel.ChannelOption.TCP_NODELAY, true)
                    .option(io.netty.channel.ChannelOption.SO_KEEPALIVE, true)
                    .option(io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS, 10_000);

            ChannelPoolHandler poolHandler = new ChannelPoolHandler() {
                @Override
                public void channelReleased(Channel ch) {
                }

                @Override
                public void channelAcquired(Channel ch) {
                }

                @Override
                public void channelCreated(Channel ch) {
                    ChannelPipeline pipeline = ch.pipeline();
                    if (sslCtx != null) {
                        pipeline.addLast("ssl", sslCtx.newHandler(ch.alloc(), host, port));
                    }
                    pipeline.addLast("codec", new HttpClientCodec());
                    pipeline.addLast("decompressor", new HttpContentDecompressor());
                    pipeline.addLast("aggregator", new HttpObjectAggregator(5 * 1024 * 1024));
                    pipeline.addLast("readTimeout", new ReadTimeoutHandler(10));
                }
            };

            int maxConnections = 50;
            int maxPendingAcquires = 200;
            return new FixedChannelPool(
                    bootstrap,
                    poolHandler,
                    ChannelHealthChecker.ACTIVE,
                    AcquireTimeoutAction.FAIL,
                    5_000,
                    maxConnections,
                    maxPendingAcquires,
                    true
            );
        } catch (SSLException e) {
            throw new RuntimeException(e);
        }
    }

    private StockResult parseStockResponse(String jsonResponse, long durationMs) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonResponse);
            JsonNode bodyNode = rootNode.path("body");
            JsonNode contentNode = bodyNode.path("content");
            JsonNode pickupMessage = contentNode.path("pickupMessage");
            JsonNode storesNode = pickupMessage.path("stores");

            if (storesNode.isMissingNode() || !storesNode.isArray()) {
                return new StockResult(false, "响应中缺少门店信息", null, durationMs);
            }

            boolean hasStock = false;
            StringBuilder stockInfo = new StringBuilder();

            for (JsonNode store : storesNode) {
                String storeName = store.path("storeName").asText("未知门店");
                String storeNumber = store.path("storeNumber").asText("");
                boolean storeHasStock = false;

                JsonNode partsAvailability = store.path("partsAvailability");
                if (partsAvailability.isObject()) {
                    Iterator<Map.Entry<String, JsonNode>> parts = partsAvailability.fields();
                    while (parts.hasNext()) {
                        Map.Entry<String, JsonNode> part = parts.next();
                        String pickupDisplay = part.getValue().path("pickupDisplay").asText("").toLowerCase();
                        if (pickupDisplay.contains("available")) {
                            storeHasStock = true;
                            break;
                        }
                    }
                }

                if (storeHasStock) {
                    hasStock = true;
                }

                stockInfo.append(String.format("🏪 %s (%s): %s%n",
                        storeName,
                        storeNumber,
                        storeHasStock ? "✅ 有货" : "❌ 无货"));
            }

            return new StockResult(hasStock, stockInfo.toString().trim(), pickupMessage, durationMs);
        } catch (Exception e) {
            logger.error("解析库存响应失败: {}", e.getMessage(), e);
            return new StockResult(false, "解析响应失败: " + e.getMessage(), null, durationMs);
        }
    }

    private record RequestParameters(String url,
                                     String referer,
                                     String userAgent,
                                     Map<String, String> headers,
                                     String cookieHeader) { }

    private record ResponsePayload(int status, String body, Instant endTime) { }

    public static class StockResult {
        private final boolean hasStock;
        private final String message;
        private final JsonNode rawData;
        private final long durationMs;

        public StockResult(boolean hasStock, String message, JsonNode rawData) {
            this(hasStock, message, rawData, -1);
        }

        public StockResult(boolean hasStock, String message, JsonNode rawData, long durationMs) {
            this.hasStock = hasStock;
            this.message = message;
            this.rawData = rawData;
            this.durationMs = durationMs;
        }

        public boolean hasStock() {
            return hasStock;
        }

        public String getMessage() {
            return message;
        }

        public JsonNode getRawData() {
            return rawData;
        }

        public long getDurationMs() {
            return durationMs;
        }

        @Override
        public String toString() {
            String durationInfo = durationMs > 0 ? String.format("⏱️ 检查耗时: %dms%n", durationMs) : "";
            return String.format("库存状态: %s%n%s%s",
                    hasStock ? "✅ 有货" : "❌ 无货",
                    durationInfo,
                    message);
        }
    }
}
