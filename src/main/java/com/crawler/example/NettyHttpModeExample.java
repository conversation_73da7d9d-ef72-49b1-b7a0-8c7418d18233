package com.crawler.example;

import com.crawler.config.AppleIphoneConfig;
import com.crawler.config.ApplePickupLocationConfig;
import com.crawler.config.AppleUserConfig;
import com.crawler.message.CrawlerMessage;
import com.crawler.user.UserProfileManager;
import com.crawler.worker.CrawlerWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * NettyHttp模式使用示例
 * 展示如何创建和使用NettyHttp模式的CrawlerWorker
 */
public class NettyHttpModeExample {

    private static final Logger logger = LoggerFactory.getLogger(NettyHttpModeExample.class);

    public static void main(String[] args) {
        logger.info("NettyHttp模式使用示例");

        // 1. 创建配置
        AppleUserConfig userConfig = createUserConfig();
        AppleIphoneConfig iphoneConfig = createIphoneConfig();
        ApplePickupLocationConfig pickupConfig = createPickupConfig();

        // 2. 创建消息队列和用户管理器
        BlockingQueue<CrawlerMessage> messageQueue = new LinkedBlockingQueue<>();
        UserProfileManager userProfileManager = new UserProfileManager();

        // 3. 创建HYBRID模式的CrawlerWorker（推荐）
        CrawlerWorker hybridWorker = new CrawlerWorker(
                "example_user",
                messageQueue,
                userProfileManager,
                true, // headless
                CrawlerWorker.WorkerMode.HYBRID);

        // 4. 创建NettyHttp模式的CrawlerWorker（纯HTTP模式）
        CrawlerWorker nettyWorker = new CrawlerWorker(
                "example_user_netty",
                messageQueue,
                userProfileManager,
                true, // headless
                CrawlerWorker.WorkerMode.NETTY_HTTP);

        // 5. 创建Playwright模式的CrawlerWorker（纯浏览器模式）
        CrawlerWorker playwrightWorker = new CrawlerWorker(
                "example_user_playwright",
                messageQueue,
                userProfileManager,
                true, // headless
                CrawlerWorker.WorkerMode.PLAYWRIGHT);

        logger.info("创建的HYBRID Worker: {}", hybridWorker);
        logger.info("创建的NettyHttp Worker: {}", nettyWorker);
        logger.info("创建的Playwright Worker: {}", playwrightWorker);

        // 6. 创建不同类型的消息
        createAndQueueMessages(messageQueue, userConfig, iphoneConfig, pickupConfig);

        // 7. 启动工作线程
        Thread hybridThread = new Thread(hybridWorker, "Hybrid-Worker");
        Thread nettyThread = new Thread(nettyWorker, "NettyHttp-Worker");
        Thread playwrightThread = new Thread(playwrightWorker, "Playwright-Worker");

        logger.info("启动HYBRID模式线程（推荐使用）");
        hybridThread.start();

        // 可选：启动其他模式的线程进行对比
        // nettyThread.start();
        // playwrightThread.start();

        // 8. 运行一段时间后停止
        try {
            Thread.sleep(10000); // 运行10秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 9. 停止工作线程
        hybridWorker.shutdown();
        // nettyWorker.shutdown();
        // playwrightWorker.shutdown();

        try {
            hybridThread.join(5000);
            // nettyThread.join(5000);
            // playwrightThread.join(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 10. 输出统计信息
        logger.info("HYBRID Worker 统计: {}", hybridWorker.getStats());
        // logger.info("NettyHttp Worker 统计: {}", nettyWorker.getStats());
        // logger.info("Playwright Worker 统计: {}", playwrightWorker.getStats());

        logger.info("示例程序结束");
    }

    private static AppleUserConfig createUserConfig() {
        AppleUserConfig config = new AppleUserConfig();
        config.setUsername("<EMAIL>");
        config.setPassword("example_password");
        return config;
    }

    private static AppleIphoneConfig createIphoneConfig() {
        AppleIphoneConfig config = new AppleIphoneConfig();
        config.setBuyUrl("https://www.apple.com.cn/shop/buy-iphone/iphone-16");
        config.setModelDisplayName("iPhone 16 Pro");
        return config;
    }

    private static ApplePickupLocationConfig createPickupConfig() {
        return new ApplePickupLocationConfig();
    }

    private static void createAndQueueMessages(BlockingQueue<CrawlerMessage> messageQueue,
            AppleUserConfig userConfig,
            AppleIphoneConfig iphoneConfig,
            ApplePickupLocationConfig pickupConfig) {

        // 库存检查消息 - NettyHttp模式支持
        CrawlerMessage stockCheckMessage = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.CHECK_IPHONE17PRO_STOCK)
                .targetSpecificUser("example_user")
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(1)
                .build();

        // 会话验证消息 - NettyHttp模式支持
        CrawlerMessage validateMessage = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.VALIDATE_SESSION)
                .targetSpecificUser("example_user")
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(2)
                .build();

        // 登录消息 - NettyHttp模式部分支持（会话验证）
        CrawlerMessage loginMessage = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.LOGIN_AND_GENERATE_SESSION)
                .targetSpecificUser("example_user")
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(3)
                .build();

        // 加购物车消息 - NettyHttp模式部分支持（开发中）
        CrawlerMessage addToCartMessage = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.ADD_IPHONE17PRO_TO_CART)
                .targetSpecificUser("example_user")
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(4)
                .build();

        // 购买流程消息 - 只有Playwright模式支持
        CrawlerMessage purchaseMessage = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.EXECUTE_PURCHASE_FLOW)
                .targetSpecificUser("example_user")
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .pickupLocationConfig(pickupConfig)
                .priority(5)
                .build();

        // 健康检查消息 - 两种模式都支持
        CrawlerMessage healthCheckMessage = new CrawlerMessage.Builder()
                .messageType(CrawlerMessage.MessageType.HEALTH_CHECK)
                .targetSpecificUser("example_user")
                .priority(0)
                .build();

        // 添加消息到队列
        messageQueue.offer(healthCheckMessage);
        messageQueue.offer(stockCheckMessage);
        messageQueue.offer(validateMessage);
        messageQueue.offer(loginMessage);
        messageQueue.offer(addToCartMessage);
        messageQueue.offer(purchaseMessage);

        logger.info("已添加 {} 个消息到队列", messageQueue.size());
        logger.info("HYBRID模式消息处理策略:");
        logger.info("- 健康检查: 系统级操作，无需特定模式");
        logger.info("- 库存检查: 自动选择NettyHttp模式（更高效）");
        logger.info("- 会话验证: 自动选择NettyHttp模式（快速验证）");
        logger.info("- 登录操作: 自动选择Playwright模式（需要完整浏览器交互）");
        logger.info("- 加购物车: 自动选择Playwright模式（复杂页面交互）");
        logger.info("- 购买流程: 自动选择Playwright模式（复杂页面交互）");
        logger.info("");
        logger.info("HYBRID模式的优势:");
        logger.info("- 自动为每个任务选择最适合的执行方式");
        logger.info("- 库存检查等高频操作使用NettyHttp，性能更好");
        logger.info("- 登录、购买等复杂操作使用Playwright，功能完整");
        logger.info("- 单个线程即可处理所有类型的任务");
    }
}
