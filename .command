./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"
./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"
./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"

java -jar target/applecrawler-0.0.1-SNAPSHOT.jar server

curl -X POST http://localhost:8080/api/queue/start       

curl -X POST "http://localhost:8080/api/queue/send/login/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/cart/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/watch-cart/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/purchase/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/clear-bag/<EMAIL>"



"iphoneConfig": {
        "buyUrl": "https://www.apple.com.cn/shop/buy-iphone/iphone-17",
        "checkoutUrl": "https://www.apple.com.cn/shop/bag",
        "model": "iphone-17",
        "modelDisplayName": "iPhone 17",
        "color": "black",
        "colorDisplayName": "薰衣草紫色",
        "storage": "256gb",
        "storageDisplayName": "256GB 脚注",
        "tradeIn": false,
        "appleCare": false,
        "checkinIndicatorText": "需要签到",
        "requiresModelSelection": false,
        "alternateColors": [
            {
                "color": "sage-green",
                "colorDisplayName": "鼠尾草绿色"
            },
            {
                "color": "mist-blue",
                "colorDisplayName": "青雾蓝色"
            },
            {
                "color": "white",
                "colorDisplayName": "白色"
            },
            {
                "color": "black",
                "colorDisplayName": "黑色"
            }
        ]
    },


    "pickupLocationConfig": {
        "province": "yunnan",
        "provinceDisplayName": "四川",
        "city": "kunming",
        "cityDisplayName": "成都",
        "district": "wuhua",
        "districtDisplayName": "武侯区",
        "storeName": "Apple 成都太古里",
        "storeCode": "R580",
        "storeDisplayName": "Apple 成都太古里"
    }


        "watchConfig": {
        "buyUrl": "https://www.apple.com.cn/shop/buy-watch/apple-watch-ultra/49mm-cellular-black-titanium-black-alpine-loop-small",
        "checkoutUrl": "https://www.apple.com.cn/shop/bag",
        "caseDisplayName": "黑色",
        "caseValue": "black",
        "bandStyleDisplayName": "高山回环式表带",
        "bandStyleValue": "alpineloop",
        "bandColorDisplayName": "黑色",
        "bandColorValue": "black",
        "bandSizeDisplayName": "S",
        "bandSizeValue": "small",
        "tradeIn": false,
        "appleCare": false
    },