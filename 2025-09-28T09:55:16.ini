2025-09-28T09:55:16.893+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 开始监控 iPhone 17 Pro 昆明库存...
2025-09-28T09:55:46.893+08:00 ERROR 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : iPhone 17 Pro 选购流程执行失败: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"


com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.WaitableResult.get(WaitableResult.java:52) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:132) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.sendMessage(Connection.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.sendMessage(ChannelOwner.java:118) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.FrameImpl.navigateImpl(FrameImpl.java:463) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.lambda$navigate$47(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.LoggingSupport.withLogging(LoggingSupport.java:47) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.withLogging(ChannelOwner.java:97) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:42) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.Page.navigate(Page.java:5491) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.crawler.apple.AppleBusinessFlow.checkIphone17ProStock(AppleBusinessFlow.java:228) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processStockCheck(CrawlerWorker.java:463) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processMessage(CrawlerWorker.java:222) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.run(CrawlerWorker.java:160) ~[!/:0.0.1-SNAPSHOT]
        at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
        at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
        at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]
Caused by: com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.Connection.dispatch(Connection.java:254) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.processOneMessage(Connection.java:211) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        ... 18 common frames omitted

2025-09-28T09:55:46.894+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 尝试切换颜色并检查库存: 白色
2025-09-28T09:56:00.692+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 检查是否显示昆明取货信息...
2025-09-28T09:56:00.696+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 检查是否显示昆明取货信息...
2025-09-28T09:56:10.697+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 当前颜色无库存
2025-09-28T09:56:10.697+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.worker.CrawlerWorker         : 用户 <EMAIL> iPhone17Pro暂时无库存
2025-09-28T09:56:16.823+08:00  INFO 44024 --- [crawler] [ScheduledTask-4] com.crawler.queue.MessageQueueManager    : 消息已提交到队列: 28c5b026-7dca-4a20-a12e-9281db732ff6 (类型: CHECK_IPHONE17PRO_STOCK, 目标: <EMAIL>)
2025-09-28T09:56:16.823+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.worker.CrawlerWorker         : 用户 <EMAIL> 开始处理消息: CHECK_IPHONE17PRO_STOCK
2025-09-28T09:56:16.823+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.worker.CrawlerWorker         : 用户 <EMAIL> 开始检查iPhone17Pro库存
2025-09-28T09:56:16.914+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : ✅ AppleBusinessFlow 初始化完成，用户: <EMAIL>
2025-09-28T09:56:16.914+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 开始监控 iPhone 17 Pro 昆明库存...
2025-09-28T09:56:46.917+08:00 ERROR 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : iPhone 17 Pro 选购流程执行失败: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"


com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.WaitableResult.get(WaitableResult.java:52) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:132) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.sendMessage(Connection.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.sendMessage(ChannelOwner.java:118) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.FrameImpl.navigateImpl(FrameImpl.java:463) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.lambda$navigate$47(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.LoggingSupport.withLogging(LoggingSupport.java:47) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.withLogging(ChannelOwner.java:97) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:42) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.Page.navigate(Page.java:5491) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.crawler.apple.AppleBusinessFlow.checkIphone17ProStock(AppleBusinessFlow.java:228) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processStockCheck(CrawlerWorker.java:463) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processMessage(CrawlerWorker.java:222) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.run(CrawlerWorker.java:160) ~[!/:0.0.1-SNAPSHOT]
        at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
        at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
        at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]
Caused by: com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.Connection.dispatch(Connection.java:254) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.processOneMessage(Connection.java:211) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        ... 18 common frames omitted

        页面加载完成超时问题


        集体收到消息，一起打开网页互相拥挤问题

        日志带线程用户帐号ID问题功能

        成功记录流程报错及载图功能

        购买成功记录功能

        重试多次下单功能

        软件加密，限时使用问题


https://secure10.www.apple.com.cn/shop/signIn/idms/authx?ssi=4AAABmZDAyuoBIEp1BdikHOK8sNDB-nTN3IQO_-dHtb1TSDxpZYmjorXNAAAAUmh0dHBzOi8vc2VjdXJlMTAud3d3LmFwcGxlLmNvbS5jbi9zaG9wL2NoZWNrb3V0L3N0YXJ0P3BsdG49MTU5NTgyNzR8fDtNRzhYNDtNWUVWM3wAAgGf7ubwyuVLLJWlTOqNY2I1fsn9SKZEQeb7lup3JF83DQ&up=true

https://secure10.www.apple.com.cn/shop/checkoutx/fulfillment?_a=continueFromFulfillmentToShipping&_m=checkout.fulfillment