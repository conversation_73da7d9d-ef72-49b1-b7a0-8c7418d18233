{"head": {"status": 200}, "body": {"meta": {"l": ["/checkout"], "h": {"x-aos-model-page": "checkoutPage", "modelVersion": "v2", "x-aos-stk": "JkuL5bWMBgqS0Kx7ZLG1tg5kKXY", "syntax": "graviton"}}, "abs": {"FulfillmentCartItem": {}, "ItemDelivery": {"b": {"storeLocatorView": {"id": {"op": "ADD", "args": [{"get": "d.id"}, "-storeLocatorView"]}, "events": [{"on": "click", "do": "a.storeLocator<PERSON>iew", "metric": "t.pickup<PERSON>old"}]}}, "t": {"pickupCold": {"microEvents": [{"key": "prop37", "value": "CheckAvailability", "slot": "Pickup", "feature": "StoreLocator"}], "macroEvents": "event29,event42"}}, "view": "/templates/web/shared/ItemDelivery.mustache"}}, "checkout": {"d": {"page": "Fulfillment", "acmiFromBag": false, "partnerFinanceFromBag": false}, "c": ["companion<PERSON><PERSON>", "fulfillment", "locationConsent", "session"], "companionBar": {"d": {"showCompanionBar": false, "showSecureCheckoutOverlay": false, "label": "显示订单摘要：", "amount": "RMB 5,399"}, "c": ["orderSummary", "orderDetails"], "orderSummary": {"d": {"editBagLink": null, "showTaxInclusivePrices": true, "continueBagLink": "https://www.apple.com.cn/shop/bag", "items": "1 件商品", "total": "RMB 5,399", "pickupSummary": true, "displayNetOfAllSavings": false, "title": "订单总计", "showEditBag": true, "editLabel": "Edit bag", "editLabelA11y": "Editing your bag", "totalNoShip": "RMB 5,399", "subtotal": "RMB 5,399", "showBuacMessage": true}, "b": {"editBagLink": {"id": "checkout-companionBar-orderSummary-editBagLink", "key": "d.editBagLink", "events": [{"on": "click", "do": [{"set": "^d.showCompanionBar", "to": false}, {"set": "^d.showSecureCheckoutOverlay", "to": true}], "metric": "t.editBagCompanionBar"}]}}, "t": {"editBagCompanionBar": {"microEvents": [{"key": "prop37", "value": "Edit", "slot": "Companion<PERSON>ar", "feature": "Summary"}]}}}, "orderDetails": {"d": {"id": "checkout-companionBar-orderDetails", "title": "订单详情"}, "c": ["fulfillmentCompanionBar", "pickupContactCompanionBar", "billingCompanionBar", "reviewCompanionBar"], "view": "/templates/web/shared/OrderDetails.mustache", "fulfillmentCompanionBar": {"d": {"label": "送货选项", "hideDetails": true}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-fulfillmentCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.fulfillmentCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "Fulfillment"}]}}, "p": {"enabled": false}}, "pickupContactCompanionBar": {"d": {"fapiaoType": "仅电子收据", "label": "取货", "fapiaoLabel": "发票：", "pickupContactLabel": "取货联系人：", "editLabel": "更改", "editLabelA11y": "更改取货联系人"}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-pickupContactCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.pickupContactCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "PickupContact"}]}}, "p": {"enabled": false}}, "billingCompanionBar": {"d": {"editLabel": "更改", "editLabelA11y": "更改付款详情", "label": "付款方式"}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-billingCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.billingCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "Payment"}]}}, "p": {"enabled": false}}, "reviewCompanionBar": {"d": {"editLabel": "查看", "editLabelA11y": "View review details", "label": "检查订单"}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-reviewCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.reviewCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "Review"}]}}, "p": {"enabled": false}}}}, "fulfillment": {"d": {"continueLabel": "继续填写取货详情", "enableContinue": true, "shouldShowChat": true}, "b": {"continue": {"id": "checkout-fulfillment-continue", "name": "continue", "events": [{"on": "click", "do": "a.continueFromFulfillmentToPickupContact", "metric": "t.fulfillmentContinueAction"}, {"on": "click", "condition": {"op": "EQ", "args": [{"get": "d.pickupTab.pickup.storeLocator.searchInput"}, ""]}, "metric": "t.emptySearchError"}]}}, "a": {"continueFromFulfillmentToPickupContact": {"url": "/shop/checkoutx/fulfillment?_a=continueFromFulfillmentToPickupContact&_m=checkout.fulfillment", "submit": true}}, "t": {"continueAction": {"microEvents": [{"key": "eVar21", "value": "transaction.co.checkout.continue.pickupcontact.label", "slot": "Pickup"}], "macroEvents": {"op": "JOIN", "args": [",", true, {"if": {"get": "d.selfPickupContact.selfContact.address.isDaytimePhoneSelected"}, "then": "event267", "else": {"if": {"get": "d.thirdPartyPickupContact.billingContact.address.isDaytimePhoneSelected"}, "then": "event267", "else": ""}}, {"if": {"op": "EQ", "args": [{"get": "d.pickupContactOptions.selectedPickupOption"}, "SELF"]}, "then": "event142", "else": {"if": {"op": "EQ", "args": [{"get": "d.pickupContactOptions.selectedPickupOption"}, "THIRDPARTY"]}, "then": "event143", "else": ""}}]}}, "fulfillmentContinueAction": {"microEvents": [{"key": "eVar21", "value": "transaction.co.checkout.continue.pickupcontact.label", "slot": "Pickup"}], "macroEvents": {"op": "JOIN", "args": [",", true, {"if": {"get": "d.consolidatedShipmentGroupSelected"}, "then": "event329", "else": ""}]}}, "emptySearchError": {"microEvents": [{"key": "eVar25", "value": "transaction.co.fulfillment.label.storelocator.search_input", "slot": "Pickup"}]}}, "c": ["footnotes", "faq", "fulfillmentOptions", "pickupTab"], "footnotes": {"d": {"tradeInDisclaimerEnabled": false, "bugattiTradeInFootNotes": ["  "], "hasPartnerFinanceInstallment": false, "isInstallmentsUpgradeAvailable": false}}, "faq": {"d": {"faq": [{"question": "我何时能收到订购的商品？", "answer": "输入所选省份，你可以查看预计送达日期或取货日期。下单后，你会知道最终确认的日期。所有预估信息根据商品供应情况和你选择的送货方式估算得出。<a href=\"https://www.apple.com.cn/shop/help/shipping_delivery\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery\" class=\"icon-wrapper\" target=\"_blank\"><span class=\"icon-copy\">进一步了解 Apple 送货与取货</span><span class=\"visuallyhidden\">(在新窗口中打开)</span><span aria-hidden=\"true\" class=\"icon icon-after icon-external\"></span></a>\n"}, {"question": "我需要支付多少运费？", "answer": "所有在线订单均可享受免费送货服务。"}, {"question": "我可以到 Apple Store 零售店提取订购的商品吗？", "answer": "可以。如果你选择取货，需要在结账时为商品选择取货门店和取货日期。并非所有商品均可使用取货服务。当你订购的商品备好并可取货时，我们会发送短信通知你。  <a href=\"https://www.apple.com.cn/shop/help/shipping_delivery\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery\" class=\"icon-wrapper\" target=\"_blank\">进一步了解 Apple 送货与取货<span class=\"visuallyhidden\">(在新窗口中打开)</span><span aria-hidden=\"true\" class=\"icon icon-after icon-external\"></span></a>"}, {"question": "我必须签收包裹吗？", "answer": "<p>承运商要求收货时需提供与订单收货人姓名相符的签名和身份证件或发货通知邮件。你可以联系承运商进一步了解详情。<a href=\"https://www.apple.com.cn/shop/help/shipping_delivery\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery\" class=\"icon-wrapper\" target=\"_blank\"><span class=\"icon-copy\">进一步了解 Apple 送货</span><span class=\"visuallyhidden\"> (在新窗口中打开)</span><span aria-hidden=\"true\" class=\"icon icon-after icon-external\" /></a></p>\n<p>对于国家补贴活动订单，你还需要配合快递员完成必要步骤，如现场开箱、激活产品及拍照存档。<a href=\"https://www.apple.com.cn/shop/browse/home/<USER>\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: home/guojiabutie\" class=\"more\" target=\"_blank\">进一步了解国家补贴活动<span class=\"a11y\"> (在新窗口中打开)</span></a></p>"}, {"question": "我如何跟踪订单？", "answer": "每一件你订购的商品从仓库发货，我们都会向你发送电子邮件或 iMessage 信息，其中包含承运商名称和物流跟踪编号。请确保已开启 iMessage 信息。你可以点击电子邮件或 iMessage 信息中的链接，到承运商的网站跟踪你的包裹状态。"}, {"question": "如果我无法现场签收包裹，该怎么办？", "answer": "承运商要求收货时需提供与订单收货人姓名相符的签名和身份证件或发货通知邮件。如果商品送达时你不在现场，承运商会通过短信或电话与你联系，并安排新的送货时间。<a href=\"https://www.apple.com.cn/shop/help/shipping_delivery\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery\" class=\"icon-wrapper\" target=\"_blank\"><span class=\"icon-copy\">进一步了解 Apple 送货</span><span class=\"visuallyhidden\"> (在新窗口中打开)</span><span aria-hidden=\"true\" class=\"icon icon-after icon-external\"></span></a>"}, {"question": "我何时会收到短信通知？", "answer": "我们会通过短信或 iMessage 信息将最新的送货状态发送至收件人的手机号码。我们将使用联系人电话号码来沟通有关订单或付款方面的事宜。请确保已开启 iMessage 信息。如果订单出现问题，我们也会联系你。通知发送时段为每天上午 8:00 至晚上 9:00。 <br /><br />若要停止接收短信通知，请致电 <span>400-666-8800</span> 联系我们。"}, {"question": "我的商品会在取货点保留多久？", "answer": "你的取货时限为 7 天。如果你未能在此时限内取货，我们将取消订单并退款至你的账户。"}], "faqTitle": "送货与取货常见问题解答"}}, "fulfillmentOptions": {"d": {"selectFulfillmentLocation": "RETAIL", "options": [{"name": "home", "value": "HOME", "title": "为我送货", "className": "icon icon-shipping"}, {"name": "retail", "value": "RETAIL", "title": "我要取货", "className": "icon icon-applestore"}]}, "b": {"selectFulfillmentLocation": {"id": "checkout-fulfillment-options-selectFulfillmentLocation", "key": "d.selectFulfillmentLocation", "submit": true, "events": [{"on": "set", "do": "a.selectFulfillmentLocationAction"}]}}, "a": {"selectFulfillmentLocationAction": {"url": "/shop/checkoutx/fulfillment?_a=selectFulfillmentLocationAction&_m=checkout.fulfillment.fulfillmentOptions", "submit": true}}, "p": {"enabled": true}}, "pickupTab": {"b": {"pickupTab": {"id": "checkout-fulfillment-pickupTab", "name": "pickupTab", "events": [{"on": "load", "metric": "t.pickupTab"}]}}, "t": {"pickupTab": {"microEvents": [{"key": "eVar21", "value": "PickupTabSelected", "slot": "Pickup"}]}}, "p": {"enabled": true}, "c": ["pickup"], "pickup": {"d": {"header": "Select your pickup store:"}, "c": ["items", "storeLocator"], "items": {"c": ["item-b48a31d9-d0e8-441a-8942-0a17cd5dbe22"], "item-b48a31d9-d0e8-441a-8942-0a17cd5dbe22": {"f": {"abstract": "/abs.FulfillmentCartItem"}, "d": {"hasCloudSubscription": false, "productUrl": "https://www.apple.com.cn/shop/product/MYEV3CH/A", "showSingleHeader": false, "height": "400", "canShareCartItem": false, "totalPrice": "RMB 5,399", "hasAppleCareInstallment": false, "id": "checkout-fulfillment-pickupTab-pickup-items-item-b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "src": "https://store.storeimages.cdn-apple.com/1/as-images.apple.com/is/iphone-16-black-select-202409?wid=800&hei=800&fmt=jpeg&qlt=90&fit=constrain&.v=Y1l1MmFiWkNUWmJ0WXkrVTNiMnhxZlFHMjdmeEI3ZzNwbjk3M1hjdHArT09wQXdCN3lGYWkxbXNwOVQ5K1pwWW1TNnJnd1E4OUN5Y0lMSWZvUEhWUE0zRHVuYWtoZGRva3FiMExnb0ZXTnhlNEM4Sm9PS0pvbERKcmp0Y0FWY2k", "unitPrice": "RMB 5,399", "productMetricId": "bcd105d5e2311ca6b4f9bcbcd162ad83", "showQuantity": false, "name": "iPhone 16 128GB 黑色", "productEvar1": "Pickup||MYEV3CH/A", "sku": "MYEV3CH/A", "viewDetailsLinkEnabled": false, "productFamilyDisplayName": null, "alt": "iPhone 16，展示机身背面、黑色外观、摄像头系统，以及机身正面、全面屏设计、灵动岛和显示屏细黑边框。", "isBlockProductDetails": false, "hasMusicSubscription": false, "sources": [{"srcSet": "https://store.storeimages.cdn-apple.com/1/as-images.apple.com/is/iphone-16-black-select-202409?wid=800&hei=800&fmt=jpeg&qlt=90&fit=constrain&.v=Y1l1MmFiWkNUWmJ0WXkrVTNiMnhxZlFHMjdmeEI3ZzNwbjk3M1hjdHArT09wQXdCN3lGYWkxbXNwOVQ5K1pwWW1TNnJnd1E4OUN5Y0lMSWZvUEhWUE0zRHVuYWtoZGRva3FiMExnb0ZXTnhlNEM4Sm9PS0pvbERKcmp0Y0FWY2k", "type": "image/jpeg"}], "width": "400"}, "c": ["itemDelivery"], "itemDelivery": {"f": {"abstract": "/abs.ItemDelivery"}, "d": {"id": "checkout-fulfillment-pickupTab-pickup-items-item-b48a31d9-d0e8-441a-8942-0a17cd5dbe22-itemDelivery", "sthEnabled": true, "ships": {"showShipLabel": false, "resolvedShipLabel": "", "isPrescription": false, "shipMethodQuote": [{"date": "最快今天送达 10:30 - 12:30", "postfix": "RMB 45", "tooltip": ""}, {"date": "明天", "postfix": "免费", "tooltip": ""}]}, "pickup": {"warmStateForPickup": true, "quote": "<span class=>今天</span>；Apple 昆明", "storeSelectorLinkText": "显示更多零售店", "isPickupStoreAvailable": true, "storeSelectorLinkTextA11y": "Show more stores near the postcode you entered", "dudeAttributeQuote": "今天", "dudeAttributeStore": "Apple 昆明", "enableStoreSelectorLink": true, "hasRemoveGiftMessageLink": false, "pickupLabel": "立即订购。取货 (店内)："}}, "a": {"storeLocatorView": {"url": "/shop/checkoutx/fulfillment?_a=storeLocatorView&_m=checkout.fulfillment.pickupTab.pickup.items.item-b48a31d9-d0e8-441a-8942-0a17cd5dbe22.itemDelivery"}}}}}, "storeLocator": {"d": {"showAllStores": false, "wasSelectStoreActionInvoked": false, "distanceUnit": "KM", "isLoading": false, "was": {"searchInput": "云南 昆明 西山区"}, "showStores": true, "searchInput": "云南 昆明 西山区", "storeLocatorClicked": true, "isDrivingDistancesEnabled": true, "mapApiKey": "******************************************************************************************************************************************************************************************************************************************************************************", "showMoreLink": "显示更多取货地点", "search": null, "singleStoreCountry": false, "storeSelectedMetricVal": "RetailStoreSelected - list", "selectStore": "R670", "_editing": false, "useFallbackDistances": false, "noOfVisibleStore": "5", "country": "China mainland", "mapCoordinates": [{"storeNumber": "R670", "latitude": "25.0389880510382", "longitude": "102.705827951431", "storeType": "APPLE"}, {"storeNumber": "R573", "latitude": "29.5189112726118", "longitude": "106.514226794243", "storeType": "APPLE"}, {"storeNumber": "R571", "latitude": "22.8136926892093", "longitude": "108.388355970383", "storeType": "APPLE"}, {"storeNumber": "R476", "latitude": "29.5807490819547", "longitude": "106.529719233513", "storeType": "APPLE"}, {"storeNumber": "R480", "latitude": "29.5609805914503", "longitude": "106.572221517563", "storeType": "APPLE"}, {"storeNumber": "R580", "latitude": "30.6544851278746", "longitude": "104.081184267998", "storeType": "APPLE"}, {"storeNumber": "R502", "latitude": "30.6521269459173", "longitude": "104.112405180931", "storeType": "APPLE"}], "countryCode": "CN", "mapsEnabled": true, "retailSelectedString": "MYEV3"}, "b": {"showAllStores": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-showAllStores", "key": "d.showAllStores", "submit": true}, "selectStore": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-selectStore", "key": "d.selectStore", "submit": true, "events": [{"on": "set", "do": "a.select"}, {"on": "load", "do": [{"set": "b.selectStore.p.focused", "to": {"get": "d.wasSelectStoreActionInvoked"}}], "metric": "t.retailStoreSelected"}]}, "searchInput": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-searchInput", "key": "d.searchInput", "name": "searchInput", "submit": true, "p": {"valid": {"if": {"op": "AND", "args": [{"op": "EQ", "args": [{"get": "d._editing"}, true]}, {"op": "EQ", "args": [{"val": "_"}, ""]}]}, "then": {"error": "请输入你的所在地，以查询可为此订单取货的地点。"}, "else": {"if": {"op": "OR", "args": [{"op": "EQ", "args": [{"get": "d._editing"}, false]}, {"op": "MATCH", "args": ["[A-Za-z 一-鿿]", {"val": "_"}]}]}, "then": {"error": ""}, "else": {"error": "Please enter a valid district."}}}}}, "search": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-search", "key": "search", "events": [{"on": "set", "do": "a.search"}]}}, "a": {"select": {"url": "/shop/checkoutx/fulfillment?_a=select&_m=checkout.fulfillment.pickupTab.pickup.storeLocator", "submit": true}, "search": {"url": "/shop/checkoutx/fulfillment?_a=search&_m=checkout.fulfillment.pickupTab.pickup.storeLocator", "submit": true}}, "t": {"retailStoreSelected": {"microEvents": [{"key": "prop37", "value": "instore|RetailStoreSelected", "slot": "Pickup", "feature": "StoreLocator"}], "macroEvents": "event37"}}, "c": ["searchResults", "storeSearch", "address"], "searchResults": {"d": {"retailStores": [{"storeId": "R670", "availability": {"availableNowForAllLines": true, "lineItemAvailability": [{"partId": "b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "availabilityQuote": "今天 可取货", "availableNowForLine": true, "partName": "iPhone 16 128GB 黑色"}], "storeAvailability": "今天 可取货", "sameAvailabilityForAllLineItems": true, "pickupMethod": "店内取货"}, "lockerBoxDetail": null, "storeDisabled": false, "pickupMessages": [{"description": "提取你的在线订单商品。你可以获得设置帮助，还能选购配件。", "pickupMethod": "店内"}], "storeName": "Apple 昆明", "storeHours": [{"storeDays": "周五-周六", "storeTimings": "10:00 - 22:30 "}, {"storeDays": "周一-周四, 周日", "storeTimings": "10:00 - 22:00 "}], "pickupMessageCommonLabel": "参加个人设置辅导，新机上手用起来。我们会发送电子邮件邀请你参加一对一在线辅导课程，在 Specialist 专家的指导下转移数据、探索新功能，还能获得个性化建议。", "imageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R670.png?resize=828:*&output-format=jpg", "specialHoursLabel": "特殊营业时间", "phoneNumber": "4006393602", "specialHours": [{"storeHolidayDays": "Sep 30", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 1", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 2", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 5", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 6", "storeHolidayTiming": "10:00 - 22:30 "}], "personalSetupDetail": "", "pupAuthorizedLabel": null, "storeMessages": "Wait times may vary.\nYou will be given an estimated wait time when you check in. This time may vary based on Specialist availability.", "distance": "7.57 km", "retailAddress": {"city": "昆明", "mode": "VIEW", "street": "昆明市五华区东风西路 11 号 ", "layout": [{"fields": [[{"name": "street", "type": "text", "label": "详细地址 "}, {"name": "district", "type": "text", "label": "区"}], [{"name": "city", "type": "text", "label": "城镇/城市"}]]}], "district": "五华区"}}, {"storeId": "R573", "availability": {"availableNowForAllLines": true, "lineItemAvailability": [{"partId": "b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "availabilityQuote": "今天 可取货", "availableNowForLine": true, "partName": "iPhone 16 128GB 黑色"}], "storeAvailability": "今天 可取货", "sameAvailabilityForAllLineItems": true, "pickupMethod": "店内取货"}, "lockerBoxDetail": null, "storeDisabled": false, "pickupMessages": [{"description": "提取你的在线订单商品。你可以获得设置帮助，还能选购配件。", "pickupMethod": "店内"}], "storeName": "Apple 重庆万象城", "storeHours": [{"storeDays": "周五-周六", "storeTimings": "10:00 - 22:30 "}, {"storeDays": "周一-周四, 周日", "storeTimings": "10:00 - 22:00 "}], "pickupMessageCommonLabel": "参加个人设置辅导，新机上手用起来。我们会发送电子邮件邀请你参加一对一在线辅导课程，在 Specialist 专家的指导下转移数据、探索新功能，还能获得个性化建议。", "imageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R573.png?resize=828:*&output-format=jpg", "specialHoursLabel": "特殊营业时间", "phoneNumber": "4006171215", "specialHours": [{"storeHolidayDays": "Sep 30", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 1", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 2", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 5", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 6", "storeHolidayTiming": "10:00 - 22:30 "}], "personalSetupDetail": "", "pupAuthorizedLabel": null, "storeMessages": "Wait times may vary.\nYou will be given an estimated wait time when you check in. This time may vary based on Specialist availability.", "distance": "630.79 km", "retailAddress": {"city": "重庆", "mode": "VIEW", "street": "重庆市九龙坡区谢家湾正街 55 号", "layout": [{"fields": [[{"name": "street", "type": "text", "label": "详细地址 "}, {"name": "district", "type": "text", "label": "区"}], [{"name": "city", "type": "text", "label": "城镇/城市"}]]}], "district": "九龙坡区"}}, {"storeId": "R476", "availability": {"availableNowForAllLines": true, "lineItemAvailability": [{"partId": "b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "availabilityQuote": "今天 可取货", "availableNowForLine": true, "partName": "iPhone 16 128GB 黑色"}], "storeAvailability": "今天 可取货", "sameAvailabilityForAllLineItems": true, "pickupMethod": "店内取货"}, "lockerBoxDetail": null, "storeDisabled": false, "pickupMessages": [{"description": "提取你的在线订单商品。你可以获得设置帮助，还能选购配件。", "pickupMethod": "店内"}], "storeName": "Apple 重庆北城天街", "storeHours": [{"storeDays": "周一-周日", "storeTimings": "10:00 - 22:00 "}], "pickupMessageCommonLabel": "参加个人设置辅导，新机上手用起来。我们会发送电子邮件邀请你参加一对一在线辅导课程，在 Specialist 专家的指导下转移数据、探索新功能，还能获得个性化建议。", "imageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R476.png?resize=828:*&output-format=jpg", "specialHoursLabel": "特殊营业时间", "phoneNumber": "4006171240", "specialHours": [], "personalSetupDetail": "", "pupAuthorizedLabel": null, "storeMessages": "Wait times may vary.\nYou will be given an estimated wait time when you check in. This time may vary based on Specialist availability.", "distance": "637.13 km", "retailAddress": {"city": "重庆", "mode": "VIEW", "street": "重庆市江北区北城天街 8 号", "layout": [{"fields": [[{"name": "street", "type": "text", "label": "详细地址 "}, {"name": "district", "type": "text", "label": "区"}], [{"name": "city", "type": "text", "label": "城镇/城市"}]]}], "district": "江北区"}}, {"storeId": "R480", "availability": {"availableNowForAllLines": true, "lineItemAvailability": [{"partId": "b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "availabilityQuote": "今天 可取货", "availableNowForLine": true, "partName": "iPhone 16 128GB 黑色"}], "storeAvailability": "今天 可取货", "sameAvailabilityForAllLineItems": true, "pickupMethod": "店内取货"}, "lockerBoxDetail": null, "storeDisabled": false, "pickupMessages": [{"description": "提取你的在线订单商品。你可以获得设置帮助，还能选购配件。", "pickupMethod": "店内"}], "storeName": "Apple 解放碑", "storeHours": [{"storeDays": "周一-周日", "storeTimings": "10:00 - 22:00 "}], "pickupMessageCommonLabel": "参加个人设置辅导，新机上手用起来。我们会发送电子邮件邀请你参加一对一在线辅导课程，在 Specialist 专家的指导下转移数据、探索新功能，还能获得个性化建议。", "imageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R480.png?resize=828:*&output-format=jpg", "specialHoursLabel": "特殊营业时间", "phoneNumber": "4006171224", "specialHours": [], "personalSetupDetail": "", "pupAuthorizedLabel": null, "storeMessages": "Wait times may vary.\nYou will be given an estimated wait time when you check in. This time may vary based on Specialist availability.", "distance": "637.94 km", "retailAddress": {"city": "重庆", "mode": "VIEW", "street": "重庆市渝中区邹容路 108 号", "layout": [{"fields": [[{"name": "street", "type": "text", "label": "详细地址 "}, {"name": "district", "type": "text", "label": "区"}], [{"name": "city", "type": "text", "label": "城镇/城市"}]]}], "district": "渝中区"}}, {"storeId": "R571", "availability": {"availableNowForAllLines": false, "lineItemAvailability": [{"partId": "b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "availabilityQuote": "不可取货", "availableNowForLine": false, "partName": "iPhone 16 128GB 黑色"}], "storeAvailability": "目前不可取货", "sameAvailabilityForAllLineItems": true, "pickupMethod": "店内取货"}, "lockerBoxDetail": null, "storeDisabled": true, "storeName": "Apple 南宁万象城", "storeHours": [{"storeDays": "周五-周六", "storeTimings": "10:00 - 22:30 "}, {"storeDays": "周一-周四, 周日", "storeTimings": "10:00 - 22:00 "}], "imageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R571.png?resize=828:*&output-format=jpg", "specialHoursLabel": "特殊营业时间", "phoneNumber": "4006171265", "specialHours": [{"storeHolidayDays": "Sep 30", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 1", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 2", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 5", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 6", "storeHolidayTiming": "10:00 - 22:30 "}], "personalSetupDetail": "", "pupAuthorizedLabel": null, "storeMessages": "Wait times may vary.\nYou will be given an estimated wait time when you check in. This time may vary based on Specialist availability.", "distance": "633.56 km", "retailAddress": {"city": "南宁", "mode": "VIEW", "street": "南宁市青秀区民族大道 136 号", "layout": [{"fields": [[{"name": "street", "type": "text", "label": "详细地址 "}, {"name": "district", "type": "text", "label": "区"}], [{"name": "city", "type": "text", "label": "城镇/城市"}]]}], "district": "青秀区"}}, {"storeId": "R580", "availability": {"availableNowForAllLines": true, "lineItemAvailability": [{"partId": "b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "availabilityQuote": "今天 可取货", "availableNowForLine": true, "partName": "iPhone 16 128GB 黑色"}], "storeAvailability": "今天 可取货", "sameAvailabilityForAllLineItems": true, "pickupMethod": "店内取货"}, "lockerBoxDetail": null, "storeDisabled": false, "pickupMessages": [{"description": "提取你的在线订单商品。你可以获得设置帮助，还能选购配件。", "pickupMethod": "店内"}], "storeName": "Apple 成都太古里", "storeHours": [{"storeDays": "周五-周日", "storeTimings": "10:00 - 22:30 "}, {"storeDays": "周一-周四", "storeTimings": "10:00 - 22:00 "}], "pickupMessageCommonLabel": "参加个人设置辅导，新机上手用起来。我们会发送电子邮件邀请你参加一对一在线辅导课程，在 Specialist 专家的指导下转移数据、探索新功能，还能获得个性化建议。", "imageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R580.png?resize=828:*&output-format=jpg", "specialHoursLabel": "特殊营业时间", "phoneNumber": "4006171275", "specialHours": [{"storeHolidayDays": "Sep 30", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 1", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 2", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 3", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 4", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 5", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 6", "storeHolidayTiming": "10:00 - 22:30 "}], "personalSetupDetail": "", "pupAuthorizedLabel": null, "storeMessages": "Wait times may vary.\nYou will be given an estimated wait time when you check in. This time may vary based on Specialist availability.", "distance": "643.3 km", "retailAddress": {"city": "成都", "mode": "VIEW", "street": "成都市锦江区中纱帽街 8 号", "layout": [{"fields": [[{"name": "street", "type": "text", "label": "详细地址 "}, {"name": "district", "type": "text", "label": "区"}], [{"name": "city", "type": "text", "label": "城镇/城市"}]]}], "district": "锦江区"}}, {"storeId": "R502", "availability": {"availableNowForAllLines": true, "lineItemAvailability": [{"partId": "b48a31d9-d0e8-441a-8942-0a17cd5dbe22", "availabilityQuote": "今天 可取货", "availableNowForLine": true, "partName": "iPhone 16 128GB 黑色"}], "storeAvailability": "今天 可取货", "sameAvailabilityForAllLineItems": true, "pickupMethod": "店内取货"}, "lockerBoxDetail": null, "storeDisabled": false, "pickupMessages": [{"description": "提取你的在线订单商品。你可以获得设置帮助，还能选购配件。", "pickupMethod": "店内"}], "storeName": "Apple 成都万象城", "storeHours": [{"storeDays": "周五-周六", "storeTimings": "10:00 - 22:30 "}, {"storeDays": "周一-周四, 周日", "storeTimings": "10:00 - 22:00 "}], "pickupMessageCommonLabel": "参加个人设置辅导，新机上手用起来。我们会发送电子邮件邀请你参加一对一在线辅导课程，在 Specialist 专家的指导下转移数据、探索新功能，还能获得个性化建议。", "imageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R502.png?resize=828:*&output-format=jpg", "specialHoursLabel": "特殊营业时间", "phoneNumber": "4006171214", "specialHours": [{"storeHolidayDays": "Sep 30", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 1", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 2", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 5", "storeHolidayTiming": "10:00 - 22:30 "}, {"storeHolidayDays": "Oct 6", "storeHolidayTiming": "10:00 - 22:30 "}], "personalSetupDetail": "", "pupAuthorizedLabel": null, "storeMessages": "Wait times may vary.\nYou will be given an estimated wait time when you check in. This time may vary based on Specialist availability.", "distance": "643.73 km", "retailAddress": {"city": "成都", "mode": "VIEW", "street": "成都市成华区双庆路 8 号万象城", "layout": [{"fields": [[{"name": "street", "type": "text", "label": "详细地址 "}, {"name": "district", "type": "text", "label": "区"}], [{"name": "city", "type": "text", "label": "城镇/城市"}]]}], "district": "成华区"}}]}}, "storeSearch": {"d": {"searchInput": "云南 昆明 西山区", "searchLabel": "Showing stores near: "}}, "address": {"d": {"mode": "EDIT", "layout": [{"fields": [[{"name": "stateCitySelectorForCheckout", "type": "sublayout"}]]}], "showLocationConsent": true}, "c": ["stateCitySelectorForCheckout"], "stateCitySelectorForCheckout": {"d": {"city": "昆明", "showLocationConsent": true, "districtOptions": [{"value": "五华区", "label": "五华区"}, {"value": "盘龙区", "label": "盘龙区"}, {"value": "西山区", "label": "西山区"}, {"value": "官渡区", "label": "官渡区"}, {"value": "东川区", "label": "东川区"}, {"value": "安宁市", "label": "安宁市"}, {"value": "呈贡区", "label": "呈贡区"}, {"value": "晋宁区", "label": "晋宁区"}, {"value": "富民县", "label": "富民县"}, {"value": "宜良县", "label": "宜良县"}, {"value": "嵩明县", "label": "嵩明县"}, {"value": "石林彝族自治县", "label": "石林彝族自治县"}, {"value": "禄劝彝族苗族自治县", "label": "禄劝彝族苗族自治县"}, {"value": "寻甸回族彝族自治县", "label": "寻甸回族彝族自治县"}], "provinceCityDistrict": "云南 昆明 西山区", "tabSelected": "state", "countryCode": "CN", "mode": "EDIT", "layout": [{"fields": [[{"name": "provinceCityDistrict", "type": "tabs", "label": "显示此地附近的零售店："}], [{"name": "state", "type": "tab", "label": "省/市/自治区", "options": "stateOptions"}, {"name": "city", "type": "tab", "label": "城镇/城市", "options": "cityOptions"}, {"name": "district", "type": "tab", "label": "区", "options": "districtOptions"}]]}], "district": "西山区", "provinceCityDistrictTabs": false, "ProvinceSelectorCityUtil": "", "cityOptions": [{"value": "昆明", "label": "昆明"}, {"value": "曲靖", "label": "曲靖"}, {"value": "玉溪", "label": "玉溪"}, {"value": "保山", "label": "保山"}, {"value": "昭通", "label": "昭通"}, {"value": "大理白族自治州", "label": "大理白族自治州"}, {"value": "红河哈尼族彝族自治州", "label": "红河哈尼族彝族自治州"}, {"value": "楚雄彝族自治州", "label": "楚雄彝族自治州"}, {"value": "文山壮族苗族自治州", "label": "文山壮族苗族自治州"}, {"value": "普洱", "label": "普洱"}, {"value": "临沧", "label": "临沧"}, {"value": "丽江", "label": "丽江"}, {"value": "德宏傣族景颇族自治州", "label": "德宏傣族景颇族自治州"}, {"value": "怒江傈僳族自治州", "label": "怒江傈僳族自治州"}, {"value": "西双版纳傣族自治州", "label": "西双版纳傣族自治州"}, {"value": "迪庆藏族自治州", "label": "迪庆藏族自治州"}], "linkText": "选择地区", "state": "云南", "ProvinceSelectorCityOptionsUtil": [], "addressTabHeader": "Select {label}", "stateOptions": [{"value": "北京", "label": "北京"}, {"value": "上海", "label": "上海"}, {"value": "天津", "label": "天津"}, {"value": "重庆", "label": "重庆"}, {"value": "安徽", "label": "安徽"}, {"value": "福建", "label": "福建"}, {"value": "甘肃", "label": "甘肃"}, {"value": "广东", "label": "广东"}, {"value": "广西壮族自治区", "label": "广西壮族自治区"}, {"value": "贵州", "label": "贵州"}, {"value": "海南", "label": "海南"}, {"value": "河北", "label": "河北"}, {"value": "河南", "label": "河南"}, {"value": "黑龙江", "label": "黑龙江"}, {"value": "湖北", "label": "湖北"}, {"value": "湖南", "label": "湖南"}, {"value": "吉林", "label": "吉林"}, {"value": "江苏", "label": "江苏"}, {"value": "江西", "label": "江西"}, {"value": "辽宁", "label": "辽宁"}, {"value": "内蒙古自治区", "label": "内蒙古自治区"}, {"value": "宁夏回族自治区", "label": "宁夏回族自治区"}, {"value": "青海", "label": "青海"}, {"value": "山东", "label": "山东"}, {"value": "山西", "label": "山西"}, {"value": "陕西", "label": "陕西"}, {"value": "四川", "label": "四川"}, {"value": "西藏自治区", "label": "西藏自治区"}, {"value": "新疆维吾尔自治区", "label": "新疆维吾尔自治区"}, {"value": "云南", "label": "云南"}, {"value": "浙江", "label": "浙江"}]}, "b": {"city": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-address-stateCitySelectorForCheckout-city", "key": "d.city", "type": "select", "submit": true, "optional": true, "events": [{"on": "set", "do": [{"set": "b.city.p.valid", "to": ""}, "a.Selectcity", {"set": "d.district", "to": {"get": "d.ProvinceSelectorCityUtil"}}, {"set": "d.districtOptions", "to": {"get": "d.ProvinceSelectorCityOptionsUtil"}}]}], "p": {"active": {"op": "OR", "args": [{"op": "!EQ", "args": [{"op": "LENGTH", "args": [{"get": "d.cityOptions"}]}, 0]}, {"op": "NOT", "args": [{"op": "EMPTY", "args": [{"get": "d.city"}]}]}]}, "visible": {"get": "b.city.p.active"}, "editable": true}}, "state": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-address-stateCitySelectorForCheckout-state", "key": "d.state", "type": "select", "submit": true, "optional": true, "events": [{"on": "set", "do": [{"set": "b.state.p.valid", "to": ""}, "a.<PERSON>", {"set": "d.city", "to": {"get": "d.ProvinceSelectorCityUtil"}}, {"set": "d.district", "to": {"get": "d.ProvinceSelectorCityUtil"}}, {"set": "d.districtOptions", "to": {"get": "d.ProvinceSelectorCityOptionsUtil"}}, {"set": "d.cityOptions", "to": {"get": "d.ProvinceSelectorCityOptionsUtil"}}]}], "p": {"active": {"op": "OR", "args": [{"op": "!EQ", "args": [{"op": "LENGTH", "args": [{"get": "d.stateOptions"}]}, 0]}, {"op": "NOT", "args": [{"op": "EMPTY", "args": [{"get": "d.state"}]}]}]}, "visible": {"get": "b.state.p.active"}, "editable": true}}, "provinceCityDistrict": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-address-stateCitySelectorForCheckout-provinceCityDistrict", "key": "d.provinceCityDistrict", "type": "text", "submit": true, "events": [{"on": "click", "do": [{"set": "d.provinceCityDistrictTabs", "to": {"op": "NOT", "args": [{"get": "d.provinceCityDistrictTabs"}]}}]}], "p": {"editable": true, "valid": {"if": {"op": "EMPTY", "args": [{"get": "d.state"}]}, "then": {"error": "请输入你的所在地，以查询可为此订单取货的地点。"}, "else": {"if": {"op": "EMPTY", "args": [{"get": "d.city"}]}, "then": {"error": "请选择城市。"}, "else": {"if": {"op": "EMPTY", "args": [{"get": "d.district"}]}, "then": {"error": "请选择区。"}, "else": {"error": ""}}}}}}, "countryCode": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-address-stateCitySelectorForCheckout-countryCode", "key": "d.country<PERSON>ode", "submit": true}, "district": {"id": "checkout-fulfillment-pickupTab-pickup-storeLocator-address-stateCitySelectorForCheckout-district", "key": "d.district", "type": "select", "submit": true, "optional": true, "events": [{"on": "set", "do": [{"set": "b.district.p.valid", "to": ""}, {"set": "d.provinceCityDistrict", "to": {"op": "ADD", "args": [{"get": "d.state"}, " ", {"if": {"op": "EQ", "args": [{"get": "d.state"}, {"get": "d.city"}]}, "then": {"if": {"op": "EQ", "args": [{"get": "d.city"}, {"get": "d.district"}]}, "then": "", "else": {"get": "d.district"}}, "else": {"op": "ADD", "args": [{"get": "d.city"}, " ", {"if": {"op": "EQ", "args": [{"get": "d.city"}, {"get": "d.district"}]}, "then": " ", "else": {"get": "d.district"}}]}}]}}, {"set": "d.provinceCityDistrictTabs", "to": {"op": "NOT", "args": [{"get": "d.provinceCityDistrictTabs"}]}}]}], "p": {"active": {"op": "OR", "args": [{"op": "!EQ", "args": [{"op": "LENGTH", "args": [{"get": "d.districtOptions"}]}, 0]}, {"op": "NOT", "args": [{"op": "EMPTY", "args": [{"get": "d.district"}]}]}]}, "visible": {"get": "b.district.p.active"}, "editable": true}}}, "a": {"Selectstate": {"url": "/shop/checkoutx/fulfillment?_a=Selectstate&_m=checkout.fulfillment.pickupTab.pickup.storeLocator.address.stateCitySelectorForCheckout", "submit": true, "module": "^", "validate": false}, "Selectcity": {"url": "/shop/checkoutx/fulfillment?_a=Selectcity&_m=checkout.fulfillment.pickupTab.pickup.storeLocator.address.stateCitySelectorForCheckout", "submit": true, "module": "^", "validate": false}}}}}}}}, "locationConsent": {"d": {"locationConsent": false}, "b": {"locationConsent": {"id": "checkout-locationConsent", "key": "d.locationConsent", "submit": true, "events": [{"on": "set", "do": "a.location-consent"}]}}, "a": {"location-consent": {"url": "/shop/checkoutx?_a=location-consent&_m=checkout.locationConsent", "submit": true, "validate": false}}}, "session": {"d": {"alertMs": "60000", "interactionMs": "300000", "expiredUrl": "https://www.apple.com.cn/shop/sorry/session_expired", "canExtend": true, "ttl": "1190476", "extendSessionUrl": "/shop/checkoutx/session?_a=extendSessionUrl&_m=checkout.session", "extendSession": null}, "b": {"extendSession": {"id": "checkout-session-extendSession", "key": "d.extendSession", "events": [{"on": "click", "do": "a.extendSession"}]}}, "a": {"extendSession": {"url": "/shop/checkoutx/session?_a=extendSession&_m=checkout.session"}}}}}}