/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[5339],{6322:(e,a,n)=>{n.r(a),n.d(a,{default:()=>C});var t=n(8168),l=n(1594),r=n.n(l),i=n(6942),s=n.n(i),o=n(2224),g=n.n(o),d=n(837),m=n(8382),c=n(6637),v=n(7427),f=n(3416),u=n(5911),E=n(1265),y=n(7856),p=n(2684),b=n(7634),M=n(7213),N=n(5356),A=n(9989);const h=e=>{let{bundleKey:a,bundleDisplayValue:n,engravingPageData:t,slotName:l,selection:i,font:o,fontIdx:d,fontDisplayType:m,onAddingMessage:c}=e;const{errors:v,hasError:f=!1}=i,{removeEmojiButton:y,emojiDisclaimer:p,emojiGroupLabel:h,loremLabel:I,errorMsg:D}=l,T=g().get(t,`engravingMessageBox${a}`,{}),{emojis:x={},selectedEmoji:P="",groupKey:V}=T,$=g().get(x,o,[]),[k,j]=r().useState(P),C=m===A.MM.MULTI_SELECT,K=g().get(v,"line1",""),O=v?K:f?D:"",{group:w=0}=m===A.MM.EMOJI&&g().find($,(e=>e.options.filter((e=>e.value===k)).length>0))||{};return r().createElement("div",{className:"form-element"},r().createElement("div",{className:s()(`rf-engraving-${o}`,{"rf-engraving-emoji-multiselect":C}),id:`rf-engraving-emoji-${V}`},r().createElement(N.A,{bundleKey:a,groupKey:V,items:$,defaultIndex:w,fontIdx:d,selected:C?"":k,groupLabel:C?I:h,onHandleChange:e=>{const t=C?k.concat(e):e;j(t),c({bundleKey:a,bundleDisplayValue:n,font:o,fontIdx:d,fields:$,fieldVals:t})}}),r().createElement("div",{className:`rf-engraving-remove ${E.A.BODY}`},r().createElement(M.A,{in:!!k,role:"tabpanel"},r().createElement("button",{type:"button",onClick:()=>(j(""),void c({bundleKey:a,bundleDisplayValue:n,font:o,fontIdx:d,fields:$,fieldVals:"",emojiRemoved:!0})),className:"as-buttonlink","data-autom":"engraving-remove-emoji"},y))),r().createElement("div",{className:s()({"is-error":!!O})},r().createElement(b.A,{error:O,errorAttrs:{"data-autom":"engraveValidationMsg"},"aria-live":"polite"})),p&&r().createElement("div",{className:"rf-engraving-emojidisclaimer"},r().createElement("p",(0,u.OH)(p)))))};var I=n(2148),D=n(7281);const T=e=>{let{bundleKey:a,bundleDisplayValue:n="",engravingPageData:l,slotName:i={},selection:o,selectedTab:d,onTabChange:m,validateEngravingMsg:c,onAddingMessage:v,disabled:f=!1,disclaimerId:b=""}=e;const{fontChoiceLegendA11yText:M,fontChoiceA11yText:N,emojiChoiceA11yText:T,regionalLangMessage:x="",engravingFooterDisclaimer:P=""}=i,V=g().get(l,`fonts${a}`,[]),$=g().get(l,`engravingMessageBox${a}`,{}),{lines:k=[],selectedEmoji:j="",emojis:C={}}=$,K=V.length>1,O=`${M} ${p.LU(n)}`,{font:w=""}=o,{fontIdx:F=0,fontName:L="",fontDisplayType:B,fontFamily:H=""}=V[d]||{},_=!!V.find((e=>e.fontDisplayType===A.MM.EMOJI_TEXT)),R=!(!K&&!_)&&Object.keys(C).some((e=>C[e].length>1)),S={bundleKey:a,bundleDisplayValue:n,engravingPageData:l,slotName:i,selection:o,onAddingMessage:v,font:w,disabled:f,disclaimerId:b};return r().createElement(r().Fragment,null,r().createElement("div",{className:s()("rf-engraving-form",{"rf-engraving-form-withtab":K,"rf-engraving-form-textandemoji":_,"rf-engraving-form-withdots":R})},K&&r().createElement(r().Fragment,null,r().createElement("fieldset",{disabled:f},r().createElement("legend",{className:"visuallyhidden"},O),r().createElement(y.Ay,{className:"rf-engraving-tabs",handleChange:e=>((e,t)=>{let{fontName:l,fontFamily:r}=e,i="";m(t),i=(0,D.Ve)(l)?j:k.reduce(((e,a)=>(e[a.name]=a.value,e)),{}),c({bundleKey:a,bundleDisplayValue:n,fields:k,font:l,fontIdx:F,fontFamily:r,fieldVals:i})})(V[e],e),defaultIndex:F,count:V.length},r().createElement(y.jl,{classes:{root:"rf-engraving-tabnav",item:"rf-engraving-tabnav-item",link:"rf-engraving-tabnav-link"},keys:V.map((e=>e.fontName)),items:V.map((e=>r().createElement(r().Fragment,null,e.fontLabel,r().createElement("span",{className:"visuallyhidden"},(0,D.Ve)(e.fontName)?T:N)))),attrs:V.map((e=>({"data-autom":e?.fontName})))}),r().createElement(y.T2,{className:s()("rf-engraving-tabpanels")},V.map((e=>r().createElement(y.Kp,{index:e.fontIdx,key:e.fontName,className:"rf-engraving-tabpanel"},e.fontDisplayType===A.MM.EMOJI||e.fontDisplayType===A.MM.MULTI_SELECT?r().createElement(h,(0,t.A)({},S,{font:e.fontName,fontIdx:e.fontIdx,fontFamily:e.fontFamily,fontDisplayType:e.fontDisplayType})):r().createElement(I.A,(0,t.A)({},S,{font:e.fontName,fontIdx:e.fontIdx,fontFamily:e.fontFamily,showEmojiPicker:e.fontDisplayType===A.MM.EMOJI_TEXT}))))))))),!K&&r().createElement(I.A,(0,t.A)({},S,{font:L,fontFamily:H,fontIdx:F,showEmojiPicker:B===A.MM.EMOJI_TEXT}))),x&&r().createElement("div",(0,t.A)({id:"engraving-regionallang-disclaimer",className:`rf-engraving-regionallang-disclaimer ${E.A.CAPTION}`},(0,u.OH)(x))),P&&r().createElement("div",(0,t.A)({className:`rf-engraving-footer-disclaimer ${E.A.CAPTION}`},(0,u.OH)(P))))};var x=n(584),P=n(3725);const V=e=>{let{bundleProps:a,videoProps:n={}}=e;const{data:l,showVideo:i}=n,o=g().get(l,"src"),d=g().get(a,"selection.font",""),m=g().get(a,`engravingPageData.fonts${a.bundleKey}`,[]),c=d?g().findIndex(m,{fontName:d}):0;return r().createElement("div",{className:s()({"rf-engraving-media-content":!!o})},r().createElement(x.Dk,(0,t.A)({},a,{selectedTab:c,showVideo:i})),o&&r().createElement(x.ZI,n))},$=e=>{let{bundleProps:a,bundleItem:n=!1,videoProps:l={},validateEngravingMsg:i,onAddingMessage:o,disabled:m=!1,disclaimerId:c=""}=e;const{showVideo:v}=l,f=g().get(a,"selection.font",""),u=g().get(a,`engravingPageData.fonts${a.bundleKey}`,[]),E=f?g().findIndex(u,{fontName:f}):0,[y,p]=r().useState(E),b=g().get(u,`${y}.fontDisplayType`,"");return r().createElement(d.A,{disabled:v,className:s()("rf-engraving-content",{"rf-engraving-content-disabled":v,"rf-engraving-content-emojitext":b===A.MM.EMOJI_TEXT,"rf-engraving-content-hide":n&&m})},r().createElement(T,(0,t.A)({},a,{validateEngravingMsg:i,onAddingMessage:o,selectedTab:y,onTabChange:p,disabled:m||v,disclaimerId:c})))},k=e=>{let{bundleProps:a,videoProps:n={},validateEngravingMsg:t,onAddingMessage:l,disabled:i=!1,disclaimerId:s=""}=e;return r().createElement(r().Fragment,null,r().createElement(V,{bundleProps:a,videoProps:n}),r().createElement($,{bundleProps:a,videoProps:n,validateEngravingMsg:t,onAddingMessage:l,disabled:i,disclaimerId:s}))},j=e=>{let{engravingPageData:a,slotName:n,selection:t,validateEngravingMsg:l,onAddingMessage:i,selectedBundleKey:o,disclaimerId:d=""}=e;const f=g().get(a,"bundles",[]),[u,E]=r().useState(o),{viewport:y}=(0,c.S)(),p="small"===y,[b,M]=(0,v.oM)();return r().createElement(r().Fragment,null,r().createElement("div",{className:s()("with-paddlenav",{"rf-engraving-bundle-oneitem":1===f.length})},r().createElement(v.Ay,{message:b}),r().createElement(m.aT,{items:f,dotnav:!1,defaultIndex:u,onChange:e=>{E(e),M(g().get(f,`${e}.overlayBundleDisplayValue`,""))},paddlenav:!p,paddlenavProps:{compact:!1},swipableProps:{trackTouch:!0},classes:{root:"rf-engraving-bundle-gallery",gallery:"rf-engraving-bundle",scroller:"rf-engraving-bundle-scroller"},renderItem:(e,o)=>{const{key:g=0,overlayBundleDisplayValue:d=""}=e,c=g===u,v={bundleKey:g,bundleDisplayValue:d,selection:t[g],engravingPageData:a,slotName:n};return r().createElement(m.t,{key:o,index:o,className:s()("rf-engraving-bundle-item",{"rf-engraving-bundle-selecteditem":c,"rf-engraving-bundle-nonselecteditem":!c})},r().createElement("div",{className:"rf-engraving-bundle-box"},r().createElement(V,{bundleProps:v,validateEngravingMsg:l,onAddingMessage:i,disabled:!c})))},a11y:{label:n.engravingA11yText,items:f.map((e=>e.bundleDisplayValue))}})),f.map((e=>{const{key:s=0,bundleDisplayValue:o}=e,g=s===u,m={bundleKey:s,bundleDisplayValue:o,selection:t[s],engravingPageData:a,slotName:n};return r().createElement($,{key:s,bundleProps:m,validateEngravingMsg:l,onAddingMessage:i,disabled:!g,disclaimerId:d,bundleItem:!0})})))},C=e=>{const{visible:a,showContent:n,showVideo:l,videoRef:i,saveButtonRef:o,onClose:d,data:m,isFetching:c,error:v,saveEngrave:y,cancelEngrave:p,validateEngravingMsg:b,onAddingMessage:M,onVideoEnded:N,selectedBundleKey:h,additionalParam:I,prefersReducedMotion:D}=e,T=r().useId(),{isSaving:V,slotName:$,selection:C,isBundleEngraving:K}=m,{engravingPageData:O,engravingVideo:w}=g().get(m,"engravingDetails",{}),F=g().get(O,"engravingMessageBox0.groupKey",""),L=g().get(O,"bundles",[]),{engravingHeader:B,engravingDisclaimer:H="",keyboardHintText:_=""}=$||{},{key:R=0,bundleDisplayValue:S=""}=L[0]||{},J=g().get(O,`engravingMessageBox${R}.lines`,{}),X=!!g().get(O,`fonts${R}`,[]).find((e=>e.fontDisplayType===A.MM.EMOJI_TEXT)),U=J.length>1&&X,Y={bundleKey:R,bundleDisplayValue:S,selection:C?C[R]:{},engravingPageData:O,slotName:$,additionalParam:I},G={data:w,videoRef:i,showVideo:l,onVideoEnded:N,slotName:$,prefersReducedMotion:D},W=r().useRef(null);return r().createElement(f.eA,{visible:a,classes:{root:s()("rf-engraving-overlay",{"rf-engraving-overlay-bundle":K}),content:"rf-engraving-overlay-content"},noPadding:!0,fixedWidth:!0,ariaLabel:`rf-engraving-overlay-label-${F}`,"data-autom":"Engrave-overlay",onClose:()=>d(),onEntered:()=>{W.current.scrollTop>0&&(W.current.scrollTop=0)},ref:W,appear:!0,footnoteListClassName:"rf-engraving-footnotes"},g().isEmpty(v)?r().createElement("div",{className:"rf-engraving-overlay-contentsection"},c?r().createElement(P.A,{ariaLabel:"loading"}):r().createElement(r().Fragment,null,r().createElement("h2",(0,t.A)({className:`rf-engraving-overlay-header ${E.A.HEADLINE}`,id:`rf-engraving-overlay-label-${F}`,"data-autom":"engraving-overlay-header"},(0,u.OH)(B))),H&&r().createElement("div",{className:"rf-engraving-textdisclaimer"},r().createElement("p",{id:`${T}-engraving-disclaimer`,className:`rf-engraving-disclaimer ${E.A.BODY}`,"data-autom":"engraving-disclaimer"},r().createElement("span",(0,u.OH)(H)),U?r().createElement("span",(0,t.A)({className:"rf-engraving-hint-text"},(0,u.OH)(_))):null)),n&&r().createElement("div",{className:"rf-engraving-selecteditem"},K?r().createElement(j,{engravingPageData:O,slotName:$,selection:C,validateEngravingMsg:b,onAddingMessage:M,selectedBundleKey:h,disclaimerId:`${T}-engraving-disclaimer`}):r().createElement(k,{bundleProps:Y,videoProps:G,validateEngravingMsg:b,onAddingMessage:M,disclaimerId:`${T}-engraving-disclaimer`})),r().createElement(x.rs,{isSaving:V,showVideo:l,saveButtonRef:o,slotName:$,selection:C,saveEngrave:y,cancelEngrave:p}))):r().createElement("div",{className:"rf-engraving-overlay-contentsection"},r().createElement("div",(0,t.A)({className:`rf-engraving-overlay-error ${E.A.HEADLINE}`},(0,u.OH)(v.message)))))}}}]);