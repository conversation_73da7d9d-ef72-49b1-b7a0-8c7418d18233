/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[9036],{1954:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(1594),l=n(5646),a=n(5911);const o=e=>e?Math.floor(10*e.volume):0,c=e=>{const t=Math.floor(e,10),n=Math.floor(t/60/60);return(n>0?`${n.toString().padStart(2,"0")}:`:"")+`${(Math.floor(t/60)-60*n).toString().padStart(2,"0")}:`+(t%60).toString().padStart(2,"0")},s=function(){let{withTimeUpdate:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(0,r.useRef)(null),[n,s]=(0,r.useState)(!1),[u,d]=(0,r.useState)(!1),[i,m]=(0,r.useState)(!1),[f,h]=(0,r.useState)(!1),[p,v]=(0,r.useState)(!1),[S,g]=(0,r.useState)(!1),[E,L]=(0,r.useState)(!1),[y,R]=(0,r.useState)(0),b=(0,r.useRef)(0),[w,x]=(0,r.useState)(!1),[C,T]=(0,r.useState)(!1),[k,M]=(0,r.useState)([]),[O,X]=(0,r.useState)(!1),[A,F]=(0,r.useState)(0),[P,D]=(0,r.useState)(0),[_,N]=(0,r.useState)(!1),[W,I]=(0,r.useState)(!1),[U,z]=(0,r.useState)(!1),q=(0,r.useRef)(!1);async function V(){const e=(0,l.tG)("use-video/useVideo/play"),n=t.current;if(n&&(n.paused||n.ended))try{await n.play(),v(!0),x(!0),g(!1)}catch(t){v(!1),e.error(t)}}const $=()=>{const e=t.current;e&&(e.pause(),v(!1))},B=e=>{const n=t.current;if(n){const t=(0,a.qE)(e,0,10);0===t?(n.muted=!0,L(!0)):(n.muted=!1,L(!1)),n.volume=t/10,R(t)}},K=()=>{const e=t.current;e&&!E&&(b.current=y,B(0),e.muted=!0,L(!0))},G=()=>{const e=t.current;e&&E&&(B(b.current),e.muted=!1,L(!1))},H=e=>{const n=t.current;n&&e>=0&&e<A&&(n.currentTime=e,D(e))},j=e=>{const n=t.current;if(n){let t=!1;const r=k.map(((r,l)=>{const a=l===e;return n.textTracks[l]&&(n.textTracks[l].mode=a?"showing":"hidden",a&&(t=!0)),r.selected=a,r}));X(t),M(r)}};return(0,r.useEffect)((()=>{const e=t.current;if(e){if(R(o(e)),N(e.webkitSupportsPresentationMode&&"function"==typeof e.webkitSetPresentationMode),e.textTracks.length>0){const t=[];for(let n=0;n<e.textTracks.length;n+=1)t.push({label:e.textTracks[n].label,language:e.textTracks[n].language,selected:!1});e.textTracks.length>1&&t.push({label:"Off",language:null,selected:!0}),M(t)}const t=e=>I(e&&"available"===e.availability);window.WebKitPlaybackTargetAvailabilityEvent&&(e.addEventListener("webkitplaybacktargetavailabilitychanged",t),setTimeout((()=>{e.removeEventListener("webkitplaybacktargetavailabilitychanged",t)}),1e4))}}),[]),(0,r.useEffect)((()=>{const n=t.current;if(n){const t=()=>{d(!1),s(!0),F(n.duration)},r=()=>{d(!1),s(!0)},l=()=>{q.current=!1,setTimeout((()=>{q.current||d(!0)}),400)},a=()=>{q.current=!0,d(!1)},c=()=>m(!0),u=()=>{g(!0),v(!1)},i=()=>D(n.currentTime),f=()=>T(!!document.fullscreenElement),h=()=>T(!!document.webkitIsFullScreen),p=()=>T(!!document.mozFullScreen),S=()=>T(!!document.msFullscreenElement),E=()=>v(!1),y=()=>v(!0),b=()=>{R(o(n)),L(n.muted)},w=()=>z(!0),x=()=>z(!1);return n.addEventListener("loadedmetadata",t),n.addEventListener("loadeddata",r),n.addEventListener("waiting",l),n.addEventListener("playing",a),n.addEventListener("canplay",a),n.addEventListener("canplaythrough",a),n.addEventListener("stalled",c),n.addEventListener("ended",u),e&&n.addEventListener("timeupdate",i),document.addEventListener("fullscreenchange",f),document.addEventListener("webkitfullscreenchange",h),document.addEventListener("mozfullscreenchange",p),document.addEventListener("msfullscreenchange",S),n.addEventListener("pause",E),n.addEventListener("play",y),n.addEventListener("volumechange",b),n.addEventListener("webkitbeginfullscreen",w),n.addEventListener("webkitendfullscreen",x),()=>{n.removeEventListener("loadedmetadata",t),n.removeEventListener("loadeddata",r),n.removeEventListener("waiting",l),n.removeEventListener("playing",a),n.removeEventListener("canplay",a),n.removeEventListener("canplaythrough",a),n.removeEventListener("stalled",c),n.removeEventListener("ended",u),e&&n.removeEventListener("timeupdate",i),document.removeEventListener("fullscreenchange",f),document.removeEventListener("webkitfullscreenchange",h),document.removeEventListener("mozfullscreenchange",p),document.removeEventListener("msfullscreenchange",S),n.removeEventListener("pause",E),n.removeEventListener("play",y),n.removeEventListener("volumechange",b),n.removeEventListener("webkitbeginfullscreen",w),n.removeEventListener("webkitendfullscreen",x)}}}),[e]),(0,r.useEffect)((()=>{!C&&k.length>0&&(()=>{const e=t.current;if(e){let t=-1;[...e.textTracks].forEach(((e,n)=>{"showing"===e.mode&&(t=n)})),j(t)}})()}),[C]),{ref:t,state:{isLoaded:n,isWaiting:u,isStalled:i,isError:f,isPlaying:p,isEnded:S,isMuted:E,volume:y,isTouched:w,isFullscreen:C,duration:A,currentTime:P,durationString:c(A),currentTimeString:c(P),currentTimePercent:(0,a.qE)(P/A*100,0,100)||0,timeRemainingString:c(A-P),captions:k,isCaptionsOn:O,isPIPSupported:_,isAirPlaySupported:W,iosFullScreen:U},controls:{handleError:()=>{h(!0)},play:V,pause:$,togglePlay:()=>{p?$():V()},stop:function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const n=t.current;n&&($(),n.currentTime=0,e&&g(!1))},mute:K,unmute:G,toggleMute:()=>{t.current&&(E?G():K())},volumeUp:()=>{const e=t.current;e&&y<10&&(e.muted=!1,L(!1),B(y+1))},volumeDown:()=>{t.current&&y>0&&B(y-1)},setVolume:B,toggleFullscreen:()=>{const e=t.current;e&&(document.webkitIsFullScreen||document.mozFullScreen||document.msFullscreenElement||document.fullscreenElement?(document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen(),T(!1)):(e.requestFullscreen?e.requestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullScreen?e.webkitRequestFullScreen():e.msRequestFullscreen&&e.msRequestFullscreen(),T(!0)))},setCurrentTime:H,setCurrentTimePercent:e=>{const n=t.current;if(n){const t=e*n.duration/100;H(t)}},setCaption:j,toggleAirPlay:()=>{const e=t.current;e&&W&&window.WebKitPlaybackTargetAvailabilityEvent&&e.webkitShowPlaybackTargetPicker()},togglePIP:()=>{const e=t.current;e&&_&&e.webkitSetPresentationMode&&e.webkitSetPresentationMode("picture-in-picture"===e.webkitPresentationMode?"inline":"picture-in-picture")}}}}},2957:(e,t,n)=>{n.d(t,{A:()=>r.A});var r=n(2688)},6392:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(8168),l=n(1594),a=n.n(l),o=n(6942),c=n.n(o);const s=(0,l.forwardRef)(((e,t)=>{let{sources:n=[],tracks:l=[],fallback:o=a().createElement("p",null,"Video is not supported"),className:s,...u}=e;return a().createElement("video",(0,r.A)({ref:t,className:c()("rc-video",s)},u),n.map((e=>a().createElement("source",{key:e.src,src:e.src,type:e.type}))),l.map((e=>a().createElement("track",{key:e.label,src:e.src,label:e.label,srcLang:e.srclang,default:e.default}))),o)}));s.displayName="Video";const u=s},7924:(e,t,n)=>{n.d(t,{HO:()=>h,Mn:()=>v,m7:()=>g,uf:()=>S,$X:()=>o});var r=n(1594),l=n.n(r);const a=l().createContext({}),o=()=>{const e=l().useContext(a);if(!e)throw new Error("Scroller compound components cannot be rendered outside the Scroller component");return e};var c=n(4792),s=n(791),u=n(1229),d=n(5911);const i=(e,t,n,r)=>{if(!e.current)return{};const l=e.current,a=l.scrollLeft,o=l.offsetWidth,c=l.scrollWidth-o,s=l.children[0],u=Array.from(s.children).map((e=>{const t=((e,t)=>{if(t&&e._cachedComputedStyle)return e._cachedComputedStyle;const n=window.getComputedStyle(e);return e._cachedComputedStyle=n,n})(e,n),l=t.scrollSnapAlign||"",a=t.scrollSnapCoordinate||"",c=e.offsetLeft+e.offsetWidth-o;return 0===l.indexOf("end")||0===a.indexOf("100%")?r?-1*e.offsetLeft:c:0===l.indexOf("center")||0===a.indexOf("50%")?(r?-1:1)*(e.offsetLeft+e.offsetWidth/2-o/2):r?-1*c:e.offsetLeft})).filter((e=>e<c));u.push(c);const d=u.map(((e,t)=>({x:e,i:t,d:Math.abs(e-(r?-1:1)*a)}))),i=d.reduce(((e,t)=>t.d<e.d?t:e),d[0]),m=d.length,f=void 0!==t?t:i.i,h=Math.min(Math.max(f,0),m-1),p=d[h].x>0?d[h].x:0;return{index:h,newX:(r?-1:1)*p,count:m,deltaX:(r?-1:1)*a-p,scrollX:a,maxX:c}},m=(e,t)=>{let n,r;switch(t.type){case"SCROLL_INDEX":return n=i(t.scrollerRef,t.targetIndex,t.cacheComputedStyle,t.isRTL),t.handleChange&&e.index!==n.index&&t.handleChange(n.index),{...e,index:n.index};case"SCROLL_TO":return n=i(t.scrollerRef,t.targetIndex,t.cacheComputedStyle,t.isRTL),t.handleChange&&e.index!==n.index&&t.handleChange(n.index),{...e,...n};case"SCROLL_UPDATE":return n=i(t.scrollerRef,e.index,t.cacheComputedStyle,t.isRTL),r=t.scrollerRef.current,r&&(r.style.overflowX="hidden",r.scrollLeft=n.newX,r.style.overflowX="scroll"),{...e,...n,deltaX:null};case"SCROLL_END":return e.deltaX||e.fixScroll?(r=t.scrollerRef.current,r&&(r.scrollLeft=e.newX),{...e,fixScroll:/transitionEnd$/i.test(t.eventType),deltaX:null}):e;case"MOUSE_DOWN":return{...e,mouseDown:!0};case"MOUSE_UP":return{...e,mouseDown:!1};default:return e}},f=e=>{let{index:t=0,handleChange:n,updateIndexWhileScrolling:l=!0,cacheComputedStyle:a=!0,dir:o}=e;const{touch:i}=c.Ez(),[f,h]=(0,r.useReducer)(m,{mouseDown:!1}),{count:p,fixScroll:v}=f,S=(0,r.useRef)(null),g=(0,r.useRef)(null),E=(0,r.useRef)(!1),L=(0,r.useRef)(0),y=(0,r.useRef)(0),R=(0,r.useRef)(0),b=(0,r.useMemo)((()=>"rtl"===o||"rtl"===(0,d.NL)()),[o]);(0,r.useLayoutEffect)((()=>{p||h({type:"SCROLL_TO",scrollerRef:S,handleChange:n,targetIndex:t,cacheComputedStyle:a,isRTL:b})}),[]),(0,r.useLayoutEffect)((()=>{v&&h({type:"SCROLL_END",scrollerRef:S})}),[v]),(0,r.useEffect)((()=>{t!==f.index&&h({type:"SCROLL_TO",scrollerRef:S,handleChange:null,targetIndex:t,cacheComputedStyle:a,isRTL:b})}),[t]),(0,r.useEffect)((()=>{y.current=f.maxX}),[f.maxX]);const w=()=>{E.current=!0,setTimeout((()=>{h({type:"SCROLL_UPDATE",scrollerRef:S}),E.current=!1}),100)},x=(0,u.A)(w,300);(0,s.A)("orientationchange",w),(0,s.A)("resize",(()=>{i||x()}));const C=()=>{E.current||h({type:"SCROLL_INDEX",scrollerRef:S,handleChange:n,cacheComputedStyle:a,isRTL:b})},T=(0,u.A)(C,100);return{state:f,scrollerRef:S,scrollerPlatterRef:g,handleScroll:l?C:T,handleScrollUpdate:w,handleTransition:e=>{e.target===g.current&&h({type:"SCROLL_END",scrollerRef:S,eventType:e&&e.type})},slideTo:e=>{f.deltaX||h({type:"SCROLL_TO",scrollerRef:S,handleChange:n,targetIndex:e,cacheComputedStyle:a,isRTL:b})},handleMouseDown:e=>{const t=S.current;f.deltaX||E.current||!t||(h({type:"MOUSE_DOWN"}),L.current=e.pageX-t.offsetLeft,R.current=t.scrollLeft)},handleMouseUp:()=>{E.current||(h({type:"SCROLL_TO",scrollerRef:S,handleChange:n,cacheComputedStyle:a,isRTL:b}),setTimeout((()=>{h({type:"MOUSE_UP"})}),500))},handleMouseMove:e=>{const t=S.current;if(!E.current&&!f.deltaX&&f.mouseDown&&t){e.preventDefault();const n=1.25*(e.pageX-t.offsetLeft-L.current);t.scrollLeft=R.current-n}},handleMouseWheel:e=>{const t=S.current;if(!t)return;const n=t.scrollLeft,{deltaX:r}=e,l=(n+r)*(b?-1:1);("hidden"===t.style.overflowX||l<0||l>y.current)&&e.preventDefault()},isRTL:b}},h=e=>{let{index:t,defaultIndex:n=0,dragEnabled:o=!1,preventNavigation:c=!1,customSnap:s=!1,snapDelay:u=150,updateIndexWhileScrolling:d,cacheComputedStyle:i,handleChange:m=()=>{},dir:h,children:p}=e;const{current:v}=(0,r.useRef)(void 0!==t),[S,g]=(0,r.useState)(n),E=f({index:v?t:S,handleChange:e=>v?setTimeout((()=>m(e)),10):g(e),updateIndexWhileScrolling:d,cacheComputedStyle:i,dir:h});return l().createElement(a.Provider,{value:{...E,preventNavigation:c,customSnap:s,snapDelay:u,dragEnabled:o}},p)};var p=n(8168);const v=e=>{let{platterAttrs:t={},scrollerItemAttrs:n,children:a,updateOnWidthChange:s,...d}=e;const{state:i,scrollerRef:m,scrollerPlatterRef:f,handleScroll:h,handleScrollUpdate:v,handleTransition:S,preventNavigation:g,customSnap:E,snapDelay:L,slideTo:y,dragEnabled:R,handleMouseDown:b,handleMouseUp:w,handleMouseMove:x,handleMouseWheel:C,isRTL:T}=o(),k=(0,r.useRef)(0),M=(0,u.A)((()=>y()),L),{touch:O,os:X}=c.Ez(),A={"data-core-scroller":"","data-core-scroller-customsnap":E||i.mouseDown?"":void 0,onScroll:i.deltaX?void 0:()=>{h(),E&&M()},onTransitionEnd:S,style:{overflowX:i.deltaX&&!i.mouseDown?"hidden":"scroll",scrollSnapType:i.deltaX?"unset":void 0},onMouseDown:R?b:void 0,onMouseUp:R?w:void 0,onMouseLeave:R?w:void 0,onMouseMove:R?x:void 0},F=Math.random()/10,P={ref:f,"data-core-scroller-platter":"","data-core-scroller-transition":i.deltaX?"":void 0,style:i.deltaX?{transform:`translate(${(T?-1*i.deltaX:i.deltaX)+F}px)`}:{},...t};return(0,r.useEffect)((()=>{const e=e=>C(e),{current:t}=m,n=g&&"macosx"===X&&!O;return t&&n&&t.addEventListener("wheel",e,{passive:!1}),()=>{t&&n&&t.removeEventListener("wheel",e)}}),[]),(0,r.useEffect)((()=>{if(s&&m.current){const{scrollWidth:e}=m.current;k.current&&k.current!==e&&v(),k.current=e}}),[a]),l().createElement("div",(0,p.A)({ref:m},A,d),l().createElement("div",P,l().Children.map(a,(e=>l().createElement("div",(0,p.A)({"data-core-scroller-item":""},n),e)))))},S=l().forwardRef(((e,t)=>{let{onClick:n=()=>{},children:r,hideFromVOWhenDisabled:a=!0,...c}=e;const{state:s,slideTo:u}=o(),d=!s.count||0===s.index,i=s.index-1;return l().createElement("button",(0,p.A)({type:"button",ref:t,disabled:d,"aria-hidden":a&&d,onClick:e=>{u(i),n(e,i,s.count)}},c),r)}));S.displayName="ScrollerPrevButton";const g=l().forwardRef(((e,t)=>{let{onClick:n=()=>{},children:r,hideFromVOWhenDisabled:a=!0,...c}=e;const{state:s,slideTo:u}=o(),d=!s.count||s.index>=s.count-1,i=s.index+1;return l().createElement("button",(0,p.A)({type:"button",disabled:d,ref:t,"aria-hidden":a&&d,onClick:e=>{u(i),n(e,i,s.count)}},c),r)}));g.displayName="ScrollerNextButton";l().forwardRef(((e,t)=>{let{page:n,onClick:r=()=>{},children:a,...c}=e;const{state:s,slideTo:u}=o(),d={"data-core-scroller-current":s.index===n?"":void 0};return l().createElement("button",(0,p.A)({type:"button",ref:t,onClick:e=>{u(n),r(e,n)}},d,c),a)})).displayName="ScrollerNextButton"},8843:(e,t,n)=>{n.d(t,{A:()=>r.A,R:()=>l.A});var r=n(6392),l=n(1954)}}]);