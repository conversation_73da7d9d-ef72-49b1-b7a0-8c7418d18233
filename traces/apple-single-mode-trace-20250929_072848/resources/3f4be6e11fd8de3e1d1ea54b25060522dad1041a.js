/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[4418],{584:(e,a,n)=>{n.d(a,{Dk:()=>f,ZI:()=>p,rs:()=>y});var t=n(8168),r=n(1594),s=n.n(r),i=n(2224),l=n.n(i),g=n(6942),o=n.n(g),d=n(6777),c=n(8843),m=n(7213),v=n(5911),u=n(2684),E=n(7281);const f=s().forwardRef(((e,a)=>{let{bundleKey:n,bundleDisplayValue:r,engravingPageData:i,slotName:g,selection:c={},selectedTab:m,showVideo:f,additionalParam:p,prefersReducedMotion:y}=e;const b={},h=l().get(i,"dynamicImage",{}),A=l().get(i,`fonts${n}`,[]),T=A.length?A[m]:"",N=l().cloneDeep(h),O=l().get(N,"srcSet.src")||l().get(N,"sources[0].srcSet"),D={},$=l().get(N,"alt"),S=l().get(N,"defaultAXMessagePartial","");N.alt=u.GP($,{message:S});let C=c.messages&&c.messages[0]||"",V=c.messages&&c.messages[1]||"";const _=c.messagesA11y&&c.messagesA11y[0]||"",k=c.messagesA11y&&c.messagesA11y[1]||"";b.th=C,b.tl=V;const w=l().get(g,"defaultValueArray.items"),P=l().get(g,"defaultValue",""),M=(0,E.Ve)(T?.fontName),H=l().get(g,"transparentBg",!1);""===C&&""===V?(C=l().get(w,"0.value")||P,V=l().get(w,"1.value")||"",M||(b.th=C,b.tl=V)):N.alt=u.GP($,{message:`${_} ${k}`});const x=c.font?c.font:T.fontName,I=x?{f:x,...p}:{...p};if(O&&N){const e=u.k0(O,{...b,...I});D.src=e}const{scaleParams1x:R,scaleParams2x:L}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;const{scaleParams1:t,scaleParams2:r}=e;return{scaleParams1x:t?u.fX({...u.JO(t),...a,...n}):null,scaleParams2x:r?u.fX({...u.JO(r),...a,...n}):null}}(N.srcSet,b,I);R&&(D.scaleParams1=R),L&&(D.scaleParams2=L);const F=l().get(D,"src",""),B=s().useMemo((()=>(e=>{if(!e||!e.src)return{};const[a,n]=e.src.split("?"),t=n?u.JO(n):{};if(!e.scaleParams1&&!e.scaleParams2)return u.k0(a,t);const r=e.scaleParams2?u.k0(a,t):u.k0(a,u.JO(e.scaleParams1||""));return(e.scaleParams1?u.k0(a,t):u.k0(a,u.JO(e.scaleParams2||"")))||r})(D)),[F]);return s().createElement("div",{className:o()("rf-engraving-image-wrapper",{"rf-engraving-transparent-wrapper":H}),ref:a},s().createElement("div",(0,t.A)({className:"rf-engraving-displayvalue"},(0,v.OH)(r))),N&&B&&s().createElement(d.Ay,{width:N.width,height:N.height,alt:N.alt||"",src:B,className:o()("rf-engraving-image",{"rf-engraving-image-hidden":f&&!y}),"data-autom":"preview-image"}))}));f.displayName="ImageView";const p=e=>{let{data:a,onVideoEnded:n,showVideo:r,videoRef:i,slotName:g,prefersReducedMotion:o}=e;const{alt:d,...v}=a,u={autoPlay:!0,muted:!0,role:"img",playsInline:!0,"aria-label":d,title:d,...l().omit(v,["poster","format"])},E=l().get(g,"transitionDuration",700);return o?null:s().createElement(m.A,{in:r,timeout:E,className:"rf-engraving-media-wrapper"},s().createElement(c.A,(0,t.A)({ref:i,className:"rf-engraving-media"},u,{onEnded:n,onStalled:n,onError:n,onContextMenu:e=>e.preventDefault()})))},y=e=>{let{isSaving:a,showVideo:n,saveButtonRef:r,slotName:i,saveEngrave:g,cancelEngrave:d,selection:c}=e;const m=l().get(i,"saveButton","Save"),u=l().get(i,"cancelButton","Cancel"),E=(c?c.filter((e=>!!e.errors||e.hasError)):[]).length>0;return s().createElement("div",{className:"rf-engraving-actions"},s().createElement("div",{className:"rf-engraving-save"},s().createElement("button",(0,t.A)({type:"button",className:o()("button","button-super","button-block",{"as-button-isloading":a}),ref:r,disabled:E||a||n,"data-autom":"engraving-save","data-trigger-id":"engraving-save",onClick:()=>g(!0)},(0,v.OH)(m)))),s().createElement("div",{className:"rf-engraving-cancel"},s().createElement("button",(0,t.A)({type:"button",className:"as-buttonlink",disabled:a,"data-autom":"engraving-cancel","data-trigger-id":"engraving-cancel",onClick:()=>d()},(0,v.OH)(u)))))}},2148:(e,a,n)=>{n.d(a,{A:()=>p});var t=n(1594),r=n.n(t),s=n(6942),i=n.n(s),l=n(2224),g=n.n(l),o=n(2787),d=n(8595),c=n(7634),m=n(6637),v=n(7427),u=n(5356),E=n(7281);const f=document.documentElement.lang||"en-US",p=e=>{let{bundleKey:a,bundleDisplayValue:n,engravingPageData:t,slotName:s,selection:l,font:p,fontIdx:y=0,fontFamily:b,showEmojiPicker:h=!1,onAddingMessage:A,disabled:T=!1,disclaimerId:N="",clearField:O}=e;const{errors:D,hasError:$=!1}=l,{assets:S}=(0,m.S)(),C=g().get(l,"messagesA11y",[]),{errorMsg:V,textEmojiGroupLabel:_,regionalLangMessage:k="",addedA11yText:w="",skipUpperCaseGEOS:P="th-TH,zh-CN,ko-KR,ja-JP,en-IN,zh-HK,zh-TW"}=s,M=P.split(","),H=g().get(s,"labelArray.items"),x=g().get(t,"bundles",[]),I=g().get(t,`engravingMessageBox${a}.lines`,[]),R=g().get(I,"0.name",""),L=g().get(t,`engravingMessageBox${a}.groupKey`),F=g().get(t,`engravingMessageBox${a}.emojis.${p}`,[]),B=g().get(t,`fonts${a}.${y}.defaultValue`,[]),K=k?"engraving-regionallang-disclaimer":"",{initialValues:U}=r().useMemo((()=>(e=>({initialValues:g().reduce(e,((e,a,n)=>(e[`line${n+1}`]=a,e)),{})}))((0,E.Ve)(l.font)?[]:l.messages)),[l.font,l.messages]),{values:j,setFieldValue:z}=(0,o.A)({initialValues:U}),Y=r().useRef({}),[G,J]=(0,v.oM)({resetDelay:4e3}),[X,Q]=r().useState(R),W=I.length>1,Z=g().get(t,"engravingMessageBox.engravingInUpperCaseOnly"),q=W&&h,[ee,ae]=r().useState(!1),ne=e=>{e.persist();const t=e.target,{name:r,value:s}=t;let i=s;Z?M.indexOf(f)<0?i=((e,a)=>{const n=e.selectionStart,t=e.selectionEnd,r=e.value.toLocaleUpperCase(f);if(a(e.name,r),r){const a=r.length;window.setTimeout((()=>{e.setSelectionRange(n+a,t)}),0)}return r})(t,z):(z(r,s),i=s.toLocaleUpperCase(f)):z(r,s);const l={...j,[r]:i};A({bundleKey:a,bundleDisplayValue:n,font:p,fontIdx:y,fontFamily:b,fields:I,fieldVals:l})},te=e=>{const{name:a,value:n}=e.target,t=g().get(Y,`current.${a}`);if(13===e.keyCode&&(e.preventDefault(),Q(a),ae(!0),n)){const e=n.length;t.setSelectionRange(e,e)}};return r().useEffect((()=>{O&&I.forEach((e=>z(e.name,"")))}),[O]),r().createElement("div",{className:"rf-engraving-messagebox"},r().createElement("div",{className:i()({"rf-engraving-singleline":!W&&!h,"rf-engraving-multilines":W,"rf-engraving-mixedtext":h,"rf-engraving-form-error":D,[`rf-engraving-${b}`]:!!b})},I.map(((e,t)=>{const{name:s,label:i}=e,l=x.length>1?g().get(H,`${a}.value`,i):i,o=j[s]?"":`${N} ${K}`;return r().createElement("div",{className:"field-wrapper",key:`${s}_${y}`,"data-autom":`Engrave-labelLine${t+1}`},r().createElement(d.A,{name:s,label:l,autoComplete:"off",error:D?D[s]:"",value:j[s]||"",onChange:ne,onFocus:()=>{q&&ee?ae(!1):Q(s)},onKeyDown:q?te:void 0,onReset:()=>(e=>{z(e,"");const t={...j,[e]:""},r=g().get(Y,`current.${e}`);A({bundleKey:a,bundleDisplayValue:n,font:p,fontIdx:y,fontFamily:b,fields:I,fieldVals:t}),r&&r.focus()})(s),resetAttrs:{"aria-label":W?`${S.reset} ${i}`:S.reset,"data-autom":W?`removeEngrave${t+1}`:"removeEngrave","data-trigger-id":W?`remove-engrave${t+1}`:"remove-engrave"},errorAttrs:{"data-autom":W?`engraveValidationMsg${t+1}`:"engraveValidationMsg"},"aria-describedby":`${o} engraving-message-${s}`,"data-autom":`Engrave${s}`,disabled:T,ref:e=>{Y.current[s]=e}}),X===s&&G?r().createElement(v.Ay,{message:`${l} ${G} ${w}`}):null,h&&r().createElement("div",{id:`engraving-message-${s}`,className:"visuallyhidden","aria-hidden":"true"},C[t]?r().createElement("span",null,l):null,r().createElement("span",null,C[t]||"")))})),r().createElement("div",{className:i()({"is-error":$})},r().createElement(c.A,{error:$?V:"",errorAttrs:{"data-autom":"engraveValidationMsg"},"aria-live":"polite"})),h&&r().createElement("div",{className:`rf-engraving-${p}`},r().createElement(u.A,{bundleKey:a,groupKey:L,items:F,groupLabel:_,fontIdx:y,dotNavFocus:ee,onHandleChange:e=>{const t=X,r=g().get(j,t,""),s=g().get(Y,`current.${t}`),i=s.selectionStart;let l=r?r.substring(0,i)+e+r.substring(i):e;if(e&&!ee){const a=e.length;window.setTimeout((()=>{s.setSelectionRange(i+a,i+a),s.focus()}),0)}z(t,l),Z&&M.indexOf(f)>=0&&(l=l.toLocaleUpperCase(f));const o={...j,[t]:l};A({bundleKey:a,bundleDisplayValue:n,font:p,fontIdx:y,fontFamily:b,fields:I,fieldVals:o}),J((0,E.Q5)(e,B))}}))))}},3725:(e,a,n)=>{n.d(a,{A:()=>s});var t=n(1594),r=n.n(t);const s=e=>{let{ariaLabel:a}=e;const n=r().useRef(null),t=r().useRef(null);return r().useEffect((()=>(t.current=document.activeElement,n.current&&n.current.focus(),()=>{t.current&&t.current.focus()})),[]),r().createElement("div",{className:"column large-12 small-12 rf-engraving-overlay-spinner"},r().createElement("div",{ref:n,role:"img",tabIndex:-1,className:"waitindicator waitindicator40-blue","aria-label":a}))}},4418:(e,a,n)=>{n.r(a),n.d(a,{default:()=>z});var t=n(1594),r=n.n(t),s=n(6942),i=n.n(s),l=n(2224),g=n.n(l),o=n(840),d=n(5646),c=n(2684),m=n(3151),v=n(8843),u=n(4792),E=n(266),f=n(9989),p=n(7281);const y=e=>e.every((e=>e?.engravedLines?.every((e=>!e.value&&!e.font)))),b=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",n=arguments.length>2?arguments[2]:void 0,t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];const r={...e},{engravingDetails:s,slotName:i}=r,l=g().get(i,"engravingData.fonts.items",[]),{engravingPageData:o,engravingVideo:d,baseProduct:c}=s,m=g().get(d,"src");if(!o||!o.engravingMessageBox)return{};const{bundleQuantity:v=0,engravingMessageBox:E}=o;r.isBundleEngraving=v>0,r.isMultiFontEngraving=l.length>0;const f=(0,p.Vp)(o),b=[];t.forEach((e=>{const{key:a,messages:n=[],font:t=""}=e;v>0?n.forEach(((e,n)=>{g().set(b,`${n}.engravedLines.${a}`,{value:e||"",font:t})})):n.forEach(((e,a)=>{g().set(b,`0.engravedLines.${a}`,{value:e||"",font:t})}))})),b.length>0&&(o.engravedItems=b),r.savedSelection=[],o.bundles=f.map((e=>{const{key:n,bundleDisplayValue:t}=e,s=g().cloneDeep(E),i=(0,p.jM)(o.engravedItems,n);let d=[...i];const c=(0,p.AW)(o.engravedItems,n),m=(0,p.af)(l,c),v=g().get(s,"inputTypes.sections[0].fields",[]);r.savedSelection[n]={key:n,bundleDisplayValue:t,messages:i,font:c,fontFamily:m,messageLines:v.length,errors:null},s.lines=v.map(((e,a)=>(g().set(e,"0.value",(0,p.Ve)(c)?"":i[a]),{...e[0]}))),s.selectedEmoji=(0,p.Ve)(c)?i[0]:"";const f=(0,p.US)(l,c),y=u.Ez().isHandheldPhone?35:40;return s.emojis={},f.forEach((e=>{const{fontDisplayType:n,fontLabel:t="Emoji",defaultValue:r}=e,l=t,g="text"!==n?[...r]:[],o=Math.ceil(g.length/y),c=Array(o).fill({}).map(((e,n)=>({legend:o>1?`${l} ${n+1}`:l,key:`${a}_selectedEmoji`,group:n,options:g.slice(n*y,n*y+y)})));s.emojis[e.fontName]=c,"text"!==n&&(d=(0,p.Uy)(i,r))})),r.savedSelection[n].messagesA11y=d,s.groupKey=a,o[`engravingMessageBox${n}`]={...s},o[`fonts${n}`]=f,e})),r.selection=Object.assign([],r.savedSelection);const h=g().get(i,"engravingData.additionalData.namedAssets",g().get(i,"data.map.namedAssets",{}));return r.slotName={...h},{data:r,showVideo:!!m&&(g().isEmpty(o.engravedItems)||y(o.engravedItems)),metricsConfig:{...n,baseProduct:c}}},h=(e,a)=>{switch(a.type){case f.En.SET_PART:return{...e,overlayPart:a.payload};case f.En.SET_DATA:{const{engravingData:n,id:t,metricsConfig:r}=a.payload;return{...e,...b(n,t,r)}}case f.En.OPEN_OVERLAY:return{...e,overlayVisible:!0,showContent:!0};case f.En.CLOSE_OVERLAY:return{...e,overlayVisible:!1};case f.En.FETCH_META_START:return{...e,isFetching:!0,isFetched:!1,error:null};case f.En.FETCH_META_ERROR:return{...e,loadOverlay:!0,isFetching:!1,isFetched:!0,error:a.error,data:{}};case f.En.FETCH_META_SUCCESS:return{...e,loadOverlay:!0,isFetching:!1,isFetched:!0,error:null,...a.payload};case f.En.UPDATE_SELECTION_DATA:{const{key:n,...t}=a.payload,r=((e,a)=>{let{key:n,isEmojiFont:t,fieldVals:r}=a;const s=g().cloneDeep(e);return t?g().set(s,`engravingPageData.engravingMessageBox${n}.selectedEmoji`,r):Object.keys(r).forEach(((e,a)=>{g().set(s,`engravingPageData.engravingMessageBox${n}.lines[${a}].value`,r[e])})),s})(e.data.engravingDetails,a.payload),s=g().get(e,"data.selection",[]).map((e=>e.key===n?{...e,...g().omit(t,["fieldVals","isEmojiFont"])}:{...e}));return{...e,data:{...e.data,selection:s,engravingDetails:r}}}case f.En.UPDATE_SELECTION_INBUNDLE:{const a=(e=>{let{engravingDetails:a,savedSelection:n=[]}=e;if(g().isEmpty(a))return{};const t=g().cloneDeep(a);return t.engravingPageData.bundles.forEach((e=>{let{key:a}=e;const r=g().get(t,`engravingPageData.engravingMessageBox${a}.lines`,[]);if(g().isEmpty(n))g().set(t,`engravingPageData.engravingMessageBox${a}.selectedEmoji`,""),r.forEach(((e,n)=>{g().set(t,`engravingPageData.engravingMessageBox${a}.lines[${n}].value`,"")}));else{const e=g().get(n,`${a}.font`,""),s=g().get(n,`${a}.messages`,[]);(0,p.Ve)(e)?(g().set(t,`engravingPageData.engravingMessageBox${a}.selectedEmoji`,s[0]),r.forEach(((e,n)=>{g().set(t,`engravingPageData.engravingMessageBox${a}.lines[${n}].value`,"")}))):(r.forEach(((e,n)=>{g().set(t,`engravingPageData.engravingMessageBox${a}.lines[${n}].value`,s[n])})),g().set(t,`engravingPageData.engravingMessageBox${a}.selectedEmoji`,""))}})),t})(e.data);return{...e,data:{...e.data,engravingDetails:a}}}case f.En.SAVE_SELECTION:return{...e,data:{...e.data,savedSelection:a.payload}};case f.En.CLEAR_SELECTION:{const a=g().cloneDeep(g().get(e,"data.savedSelection",[]));return{...e,data:{...e.data,selection:a}}}case f.En.REMOVE_SELECTION:return{...e,data:{...e.data,selection:a.payload,savedSelection:a.payload}};case f.En.HIDE_CONTENT:return{...e,showContent:!1};case f.En.HIDE_VIDEO:return{...e,showVideo:!1};case f.En.UPDATE_VIDEO_STATE:{const n=g().get(e,"data.engravingDetails.engravingVideo.src");return{...e,showVideo:!!n&&!a.payload}}default:return e}};var A=n(8108);const T=e=>{const a=e||A.Ay.getEntriesForStep({excludePath:!0}),{engravedPart:n,engravingOption:t}=a;t&&(A.Ay.remove("engravingOption"),A.Ay.remove("productToEngrave"),n&&(A.Ay.remove("engravedPart"),Object.keys(a).filter((e=>0===e.indexOf("msg.")||0===e.indexOf("f."))).forEach((e=>{A.Ay.remove(e)}))))},N=function(){let e=arguments.length>1?arguments[1]:void 0;(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((a=>{let{key:n=0,font:t="",messages:r=[],messageLines:s=0}=a;if(!e&&r.length)t&&A.Ay.set(`f.${n}`,t),r.forEach(((e,a)=>{A.Ay.set(`msg.${n+a}`,e)}));else{A.Ay.remove(`f.${n}`);for(let e=0;e<s;e+=1)A.Ay.remove(`msg.${n+e}`)}}))},O=e=>{let{engravedData:a,engravingOption:n}=e;const{isEngraved:t,part:r,engravedPart:s,selection:i}=a;t?(A.Ay.set("engravedPart",s),A.Ay.set("productToEngrave",r),N(i)):(A.Ay.remove("engravedPart"),A.Ay.remove("productToEngrave"),N(i,!0)),A.Ay.set("engravingOption",n)},D=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,t=arguments.length>3?arguments[3]:void 0;if(window.asMetrics){const{baseProduct:r,pageName:s,disableFireMetrics:i=!1}=e,l=g().get(window,"s.pageName",""),o=" engraving ",d=` ${s} - ${r}`;let c=null;const m={eVar5:[l," ",d,o," finish engraving"].join("|")};if(a)c="typeface | bundle";else{const e=g().get(n,"0.font");if(e)if("mixed"===e){const{hasEmoji:e,hasText:a}=((e,a)=>{let n=!1,t=!1;const r=g().get(e,"fonts0",[]).find((e=>"mixed"===e.fontName)),s=g().get(r,"defaultValue",[]).reduce(((e,a)=>e+a.value),""),i=g().get(a,"0.messages",[]).join("");return g().some(i,(e=>(s.includes(e)?n=!0:t=!0,n&&t))),{hasEmoji:n,hasText:t}})(t,n);c=e&&a?"typeface | mixed":e?"typeface | emoji":a?"typeface | text":""}else c=(0,p.Ve)(e)?"typeface | emoji":"typeface | text";else c="text"}if(!i){const e=g().get(window.asMetrics,"sendUserInteraction",(()=>""));c&&(m.eVar6=[l," ",d,o,` ${c}`].join("|")),e({name:"metrics",data:m})}return{engravedType:c}}return{}},$=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0;const n=g().get(e,"disableFireMetrics",!1);if(window.asMetrics&&a&&("no engraving"!==a||!n)){const n=g().get(window,"s.pageName",""),{baseProduct:t,pageName:r}=e;window.asMetrics.fireMicroEvent({eVar:"eVar5",page:n,part:"engraving",feature:`${r} - ${t}`,action:a})}},S=e=>{let{bootstrap:a,part:n,id:t,defaultEngravingOption:s,disableStorage:i,engravingData:l,storageData:u,removeEngraving:y,resetEngraving:N,onCancel:S=()=>{},onChange:C=()=>{},onSave:V=()=>{}}=e;if(!a)return{};const{metaUrl:_,assets:k,additionalParam:w,engravingSelectorKey:P,metricsConfig:M={},collapseAddEngraving:H=!0}=a,[x,I]=r().useReducer(h,{overlayVisible:!1,loadOverlay:!1,showContent:!1,showVideo:!1,overlayPart:"",data:{},assets:k}),[R,L]=r().useState(!!s),[F,B]=r().useState(s),[K,U]=r().useState(0),j=r().useRef(null),z=r().useRef(null),Y=r().useRef(!1),G=r().useRef(!!s),J=r().useRef(!1),{ref:X}=(0,v.R)(),Q=(0,m.A)(n),W=r().useMemo((()=>window.matchMedia("(prefers-reduced-motion)")?.matches),[]);r().useEffect((()=>{if(n){const e=u||(i?{}:(()=>{const e=A.Ay.getEntriesForStep({excludePath:!0}),a=[],{productToEngrave:n,engravedPart:t,engravingOption:r}=e;if(t){const n=[],t={key:0};Object.keys(e).filter((e=>0===e.indexOf("msg.")||0===e.indexOf("f."))).forEach((a=>{const[r,s]=a.split("."),i=e[a],l=Number(s);"msg"===r?n[l]=i:t.font=i})),a.push({...t,messages:n,messageLines:n.length})}return{selection:a,engravedPart:t,engravingOption:r,productToEngrave:n}})());I({type:f.En.SET_PART,payload:n}),(e=>{let{metaUrl:a,part:n,additionalParam:t,id:r,storageValue:s,assets:i,metricsConfig:l}=e;return e=>{const o=(0,d.tG)("reducer/loadEngravingData"),m=c.k0(a,{product:n,...t}),v=g().get(i,"errorMessage","");e({type:f.En.FETCH_META_START});const u=(0,E.q)(m);return u.then((a=>{const{engravingDetails:t,errorMessage:i}=g().get(a,"body",{});if(t&&!i){const i=t.variantEngravable||s.productToEngrave===n?s.selection:[];e({type:f.En.FETCH_META_SUCCESS,payload:b(a.body,r,l,i)})}else e({type:f.En.FETCH_META_ERROR,error:{isResponseError:!0,message:i||v}})})).catch((a=>{o.error(a),e({type:f.En.FETCH_META_ERROR,error:{isNetworkError:!0,errorInfo:a,message:v}})})),u}})({metaUrl:_,part:n,additionalParam:w,id:t,storageValue:e,assets:k,metricsConfig:M})(I).then((a=>{i&&g().isEmpty(e)?(L(!1),B(null),G.current=!1):((e,a)=>{const t=g().get(e,"body.engravingDetails",{}),r=g().get(t,"engravingPageData.bundleQuantity",0),s=(0,p.Vr)(a.selection),l=t.variantEngravable&&s;if(a.productToEngrave===n||l||"noEngraving"===F&&P){L(!!a.engravingOption),B(a.engravingOption),G.current=s;const e={engravedData:{selection:a.selection,isEngraved:s,isBundleEngraving:r>0,part:n,engravedPart:s?t.productEngrave:null},engravingOption:a.engravingOption,param:P?{[P]:!0}:{}};if(t.variantEngravable){const a={...e,partChange:!0,engravedData:{...e.engravedData,variantEngravable:t.variantEngravable}};i||O(a),C(a)}else"noEngraving"===F&&P&&C(e)}else Q&&Q!==n?(T(),L(!1),B(null),G.current=!1):a.engravingOption&&B(a.engravingOption)})(a,e)}))}}),[n]),r().useEffect((()=>{l&&I({type:f.En.SET_DATA,payload:{engravingData:l,id:t,metricsConfig:M}})}),[l]),r().useEffect((()=>{(0,p.Vr)(x.data.savedSelection)&&(L(!0),B("addEngraving"))}),[x.data.savedSelection]),r().useEffect((()=>{if(y||N){const e=N?null:s,a=g().get(window,"asMetrics.isBFEMetricsEnabled",!1);L(!!e),B(e);const n=g().cloneDeep(g().get(x,"data.selection",[])),t=g().get(x,"data.engravingDetails.product"),r=g().get(x,"data.isBundleEngraving",!1),l=n.map((e=>g().omit(e,["messages","messagesA11y","font","fontFamily"])));G.current&&!a&&$(x.metricsConfig,"remove engraving"),G.current=!!e,I({type:f.En.REMOVE_SELECTION,payload:l}),I({type:f.En.UPDATE_VIDEO_STATE,payload:!!e});const o={engravedData:{selection:l,isEngraved:!1,isBundleEngraving:r,part:t,engravedPart:null},engravingOption:e,param:{}};i||T(),N||C(o)}}),[y,N,s]);const Z=()=>{I({type:f.En.HIDE_CONTENT}),I({type:f.En.UPDATE_VIDEO_STATE,payload:G.current}),Y.current&&I({type:f.En.CLEAR_SELECTION})},q=e=>{const a=(0,d.tG)("useEngraving/validateEngravingMsg"),{bundleKey:n,bundleDisplayValue:t="",fields:r=[],fieldVals:s,font:i="",fontIdx:l=0,fontFamily:o="",emojiRemoved:m}=e,{engravingValidationUrl:v,product:u}=g().get(x,"data.engravingDetails",{}),y={};let b={};const h=i?{[f.D$.fontKey]:i}:{},A=(0,p.Ve)(i),T={key:n,bundleDisplayValue:t,font:i,fontFamily:o,fieldVals:s,isEmojiFont:A};if(A){const e=g().get(f.D$,"message1Key","");b[e]=s}else b=r.reduce(((e,a,n)=>{const t=g().get(f.D$,`message${n+1}Key`,""),r=g().trim(s[a.name]);return e[t]=r,e}),{});const N=Object.keys(b).map((e=>b[e]));if(N.filter((e=>e&&""!==e)).length>0){const e=c.k0(v,{product:u,...b,...h});(0,E.q)(e).then((e=>{const{content:a}=e.body;if(a.hasErrors)Object.keys(a.errors).forEach((e=>{y[f.D$[`line${parseInt(e,10)+1}Key`]]=a.errors[e]})),I({type:f.En.UPDATE_SELECTION_DATA,payload:{...T,errors:y}});else{const e=g().get(x,`data.engravingDetails.engravingPageData.fonts${n}.${l}.defaultValue`,[]),a=(0,p.Uy)(N,e);I({type:f.En.UPDATE_SELECTION_DATA,payload:{...T,messages:N,messagesA11y:a,errors:null}})}})).catch((e=>{a.error(e),I({type:f.En.UPDATE_SELECTION_DATA,payload:{...T,errors:null,hasError:!0}})}))}else I({type:f.En.UPDATE_SELECTION_DATA,payload:{...T,font:i,fontFamily:o,messages:[],messagesA11y:[],errors:null}});m&&z.current&&setTimeout((()=>z.current.focus()),200)},ee=g().debounce((e=>{q(e)}),200);return{...x,prefersReducedMotion:W,focusRef:j,videoRef:X,saveButtonRef:z,selectedOpt:F,completed:R,handleAddEngravingKeyDown:()=>{J.current=!0},handleEngravingChange:e=>{const a=g().cloneDeep(g().get(x,"data.selection",[])),{product:n,productEngrave:t,engravingPageData:r,variantEngravable:s}=g().get(x,"data.engravingDetails",{}),l=g().get(x,"data.isBundleEngraving",!1);let o={},d=!1;if(e)d=(0,p.Vr)(a),d?o=D(x.metricsConfig,l,a,r):$(x.metricsConfig,"no engraving"),I({type:f.En.SAVE_SELECTION,payload:a}),I({type:f.En.CLOSE_OVERLAY,payload:!1}),setTimeout((()=>Z()),400);else{if(!H){const e=a.map((e=>g().omit(e,["messages","font","fontFamily"])));I({type:f.En.REMOVE_SELECTION,payload:e})}$(x.metricsConfig,"no engraving")}const c=d?"addEngraving":"noEngraving";L(!0),B(c),G.current=d;const m={engravedData:{selection:a,isEngraved:d,isBundleEngraving:l,part:n,engravedPart:d?t:null,variantEngravable:s,metricsData:o},engravingOption:c,param:P?{[P]:!0}:{}};i||O(m),C(m),V(m),j.current&&H&&setTimeout((()=>j.current.focus()),200)},handleOpenOverlay:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2?arguments[2]:void 0;(!n||J.current||n.keyCode!==o.HP.Return&&n.keyCode!==o.HP.Space)&&(J.current=!1,$(x.metricsConfig,e),Y.current=!1,U(a),I({type:f.En.UPDATE_SELECTION_INBUNDLE}),W&&I({type:f.En.HIDE_VIDEO}),I({type:f.En.OPEN_OVERLAY}))},handleCloseOverlay:()=>{$(x.metricsConfig,"close engraving"),Y.current=!0,I({type:f.En.CLOSE_OVERLAY}),j.current&&setTimeout((()=>j.current.focus()),200),setTimeout((()=>Z()),400)},removeEngrave:e=>{const a=g().cloneDeep(g().get(x,"data.selection",[])),{product:n,productEngrave:t}=g().get(x,"data.engravingDetails",{}),r=g().get(x,"data.isBundleEngraving",!1),s=a.map((a=>a.key===e?g().omit(a,["messages","font","fontFamily"]):a)),l=(0,p.Vr)(s),o=l?"addEngraving":"noEngraving";r&&(L(!0),B(o)),G.current=l;g().get(window,"asMetrics.isBFEMetricsEnabled",!1)||$(x.metricsConfig,"remove engraving"),I({type:f.En.REMOVE_SELECTION,payload:s});const d={engravedData:{selection:s,isEngraved:l,isBundleEngraving:r,part:n,engravedPart:l?t:null},engravingOption:o,param:P?{[P]:!0}:{}};i||O(d),C(d),V(d),j.current&&setTimeout((()=>j.current.focus()),200)},cancelEngrave:()=>{g().get(window,"asMetrics.isBFEMetricsEnabled",!1)||$(x.metricsConfig,"cancel engraving"),Y.current=!0,I({type:f.En.CLOSE_OVERLAY}),setTimeout((()=>Z()),400),j.current&&setTimeout((()=>j.current.focus()),200),S()},onOverlayHidden:Z,validateEngravingMsg:q,onAddingMessage:ee,selectedBundleKey:K,onVideoEnded:()=>{I({type:f.En.HIDE_VIDEO})}}};var C=n(8168);const V='<svg viewBox="0 0 35 35" class="as-svgicon as-svgicon-engraving as-svgicon-base as-svgicon-engravingbase" role="img" aria-hidden="true" width="35px" height="35px"><path fill="none" d="M0 0h35v35H0z"/><path d="M17.5 6.1A11.4 11.4 0 1 1 6.1 17.5 11.413 11.413 0 0 1 17.5 6.1m0-1.1A12.5 12.5 0 1 0 30 17.5 12.5 12.5 0 0 0 17.5 5Z"/><path d="M23 20.5H12a.5.5 0 0 1 0-1h11a.5.5 0 0 1 0 1ZM25 15.5H10a.5.5 0 0 1 0-1h15a.5.5 0 0 1 0 1Z"/></svg>';var _=n(5911);const k=e=>{let{part:a,bootstrap:n,engravingState:s,disabled:l}=e;const{addEngraving:o,addEngravingMultipack:d,addEngravingText:c,yourEngraving:m,editEngraving:v,removeEngraving:u,engravingFreeText:E,engravingFootnoteMessage:f}=g().get(n,"assets",{}),y=g().get(n,"personalizeAlly",""),{data:b,handleOpenOverlay:h,removeEngrave:A,focusRef:T}=s,N=g().get(b,"isBundleEngraving",!1),O=g().get(b,"engravingDetails.engravingPageData.bundleQuantity",0),D=g().get(b,"engravingDetails.engravingPageData.engravingMessageBox0.groupKey",""),{savedSelection:$=[]}=b,S=(0,p.Vr)($),k=g().get($,"0.messages",[]),w=g().get($,"0.messagesA11y",[]),P=g().get($,"0.fontFamily",""),M=N&&O>1,H=g().get($,"0.messageLines",1)>1,[x,I]=(0,t.useState)({"aria-hidden":!0});return r().createElement(r().Fragment,null,r().createElement("div",{className:i()({"rf-engraving-nonbundle row row-logical":!N,"rf-engraving-bundleview":N,"rf-engraving-disabled":!a})},S?M?r().createElement(r().Fragment,null,r().createElement("div",{className:"rf-engraving-headline"},r().createElement("span",(0,C.A)({className:"rf-engraving-iconcontainer"},(0,_.OH)(V))),r().createElement("h4",(0,_.OH)(m)),r().createElement("div",(0,C.A)({className:"as-engraving-addbutton"},(0,_.OH)(E)))),r().createElement("div",{className:`rf-engraving-bundleview-message rf-engraving-bundle-${O}`},$&&$.map((e=>{const a=g().get(e,"messages",[]),n=g().get(e,"messagesA11y",[]),t=g().get(e,"key",[]);return r().createElement("div",{className:"rf-engraving-messagesection row row-logical",key:e.key},r().createElement("div",{className:"column large-6 small-7 rf-engraving-messagesection-item"},r().createElement("span",null,e.bundleDisplayValue),a.length>0?a.map(((a,s)=>r().createElement(r().Fragment,{key:`${e.key}_${e.font}`},r().createElement("span",{className:`rf-engraving-${e.font} rf-engraving-messagevalue`,role:"img","aria-label":n[s],"data-autom":`engraving-message${s+1}-${t+1}`},a)))):null),r().createElement("div",{className:"column large-6 small-5 rf-engraving-addbutton"},a.length>0?r().createElement(r().Fragment,null,r().createElement("button",{className:"as-buttonlink rf-engraving-buttonlink rf-engraving-editbutton icon icon-after icon-pluscircle",type:"button",onClick:()=>h("edit engraving",e.key),disabled:l,ref:T,"data-trigger-id":"engraving-edit","data-autom":`engraving-edit${t+1}`},r().createElement("span",(0,_.OH)(v)),r().createElement("span",(0,C.A)({className:"visuallyhidden"},(0,_.OH)(`${m} - ${e.bundleDisplayValue}`)))),r().createElement("span",{className:"rf-engraving-lineseparator","aria-hidden":"true"}),r().createElement("button",{className:"as-buttonlink rf-engraving-buttonlink icon icon-after icon-minuscircle",type:"button",disabled:l,onClick:()=>A(e.key),"data-trigger-id":"engraving-remove","data-autom":`engraving-remove${t+1}`},r().createElement("span",(0,_.OH)(u)),r().createElement("span",(0,C.A)({className:"visuallyhidden"},(0,_.OH)(`${m} - ${e.bundleDisplayValue}`))))):r().createElement("button",{className:"as-buttonlink rf-engraving-buttonlink icon icon-after icon-pluscircle",type:"button",disabled:l,onClick:()=>h("add free engraving",e.key),ref:T,"data-trigger-id":"engraving-add","data-autom":`engraving-add${t+1}`},r().createElement("span",(0,_.OH)(c)),r().createElement("span",(0,C.A)({className:"visuallyhidden"},(0,_.OH)(`${m} - ${e.bundleDisplayValue}`))))))})))):r().createElement(r().Fragment,null,r().createElement("div",{className:"column large-12 small-12 rf-engraving-headline"},r().createElement("span",(0,C.A)({className:"rf-engraving-iconcontainer"},(0,_.OH)(V))),r().createElement("h4",(0,C.A)({className:"rf-engraving-spacer","data-autom":"engraving-summary-header"},(0,_.OH)(m))),r().createElement("span",{className:"rf-engraving-lineseparator","aria-hidden":"true"}),r().createElement("button",{type:"button",className:"as-buttonlink rf-engraving-buttonlink icon icon-after icon-pluscircle",ref:T,disabled:l,onClick:()=>h("edit engraving"),"data-analytics-title":"edit modal | personalize it","data-autom":"engraving-edit","data-trigger-id":"engraving-edit"},v,y&&r().createElement("span",(0,_.OH)(y)))),r().createElement("div",{className:"column large-12 small-12 rf-engraving-item"},k.map(((e,a)=>r().createElement("div",{className:"rf-engraving-message",key:e},r().createElement("p",{className:i()({[`rf-engraving-${P}`]:!!P}),role:"img","aria-label":w[a],"data-autom":H?`engraving-message${a+1}`:"engraving-message"},e)))))):r().createElement(r().Fragment,null,r().createElement("div",{className:"column large-12 small-12 rf-engraving-headline"},r().createElement("button",{type:"button",className:"as-buttonlink icon icon-after icon-pluscircle",onClick:()=>h("add free engraving"),ref:T,disabled:!a||l,"aria-describedby":`engraving-footer-${D}`,"data-autom":`addEngraving-${D}`,"data-analytics-title":"open modal | personalize it","data-trigger-id":"engraving-add"},M&&d?r().createElement("span",(0,_.OH)(d)):r().createElement("span",(0,_.OH)(o)),y&&r().createElement("span",(0,_.OH)(y)))),r().createElement("div",{className:"column large-12 small-12"},r().createElement("div",(0,C.A)({id:`engraving-footer-${D}`,className:"rf-engraving-footer",onMouseEnter:()=>I({}),onMouseLeave:()=>I({"aria-hidden":!0})},(0,_.OH)(f),x))))))};var w=n(7213),P=n(632),M=n(6637),H=n(2957);const x=e=>{let{engravingState:a,bootstrap:n,disabled:t,summaryHeaderTag:s}=e;const{data:l,handleOpenOverlay:o,focusRef:d,removeEngrave:c,showContent:m}=a,{assets:v={},collapseAddEngraving:u=!0}=n,{addEngraving:E,noEngravingOptionTitle:f,yourEngraving:y,editEngraving:b,removeEngraving:h,changeEngraving:A,addEngravingText:T,selectedA11yText:N,addEngravingOptionFooter:O}=v,D=g().get(l,"isBundleEngraving",!1),$=g().get(l,"engravingDetails.engravingPageData.bundleQuantity",0),{savedSelection:S=[]}=l,V=(0,p.Vr)(S),k=g().get(S,"0.messages",[]),P=g().get(S,"0.messagesA11y",[]),M=g().get(S,"0.fontFamily",""),H=g().get(S,"0.messageLines",1)>1,x=l?.engravingDetails?.deviceEngravingText||y;return r().createElement(r().Fragment,null,V?D?r().createElement("div",{className:"rf-engraving-summary"},r().createElement(s,(0,C.A)({className:"rf-flagship-collapsable-header-text","data-autom":"engraving-summary-header"},(0,_.OH)(y))),r().createElement("div",{className:`rf-engraving-bundleview-message rf-engraving-bundle-${$}`},S&&S.map((e=>{const a=g().get(e,"messages",[]),n=g().get(e,"messagesA11y",[]),s=g().get(e,"key",[]);return r().createElement("div",{className:"rf-engraving-messagesection row row-logical",key:e.key},r().createElement("div",{className:"column large-6 small-7 rf-engraving-messagesection-item"},$>1&&r().createElement("span",null,e.bundleDisplayValue),a.length>0?a.map(((a,t)=>r().createElement(r().Fragment,{key:`${e.key}_${e.font}`},r().createElement("span",{className:`rf-engraving-${e.font} rf-engraving-messagevalue`,role:"img","aria-label":n[t],"data-autom":`engraving-message${t+1}-${s+1}`},a)))):null),r().createElement("div",{className:"column large-6 small-5 rf-engraving-addbutton"},a.length>0?r().createElement(r().Fragment,null,r().createElement("button",{className:"as-buttonlink rf-engraving-buttonlink rf-engraving-editbutton icon icon-after icon-pluscircle",type:"button",onClick:()=>o("edit engraving",e.key),disabled:t,ref:d,"data-trigger-id":"engraving-edit","data-autom":`engraving-edit${s+1}`},r().createElement("span",(0,_.OH)(b)),r().createElement("span",(0,C.A)({className:"visuallyhidden"},(0,_.OH)(`${y} - ${e.bundleDisplayValue}`)))),r().createElement("span",{className:"rf-engraving-lineseparator","aria-hidden":"true"}),r().createElement("button",{className:"as-buttonlink rf-engraving-buttonlink icon icon-after icon-minuscircle",type:"button",disabled:t,onClick:()=>c(e.key),"data-trigger-id":"engraving-remove","data-autom":`engraving-remove${s+1}`},r().createElement("span",(0,_.OH)(h)),r().createElement("span",(0,C.A)({className:"visuallyhidden"},(0,_.OH)(`${y} - ${e.bundleDisplayValue}`))))):r().createElement("button",{className:"as-buttonlink rf-engraving-buttonlink icon icon-after icon-pluscircle",type:"button",disabled:t,onClick:()=>o("add free engraving",e.key),ref:d,"data-trigger-id":"engraving-add","data-autom":`engraving-add${s+1}`},r().createElement("span",(0,_.OH)(T)),r().createElement("span",(0,C.A)({className:"visuallyhidden"},(0,_.OH)(`${y} - ${e.bundleDisplayValue}`))))))})))):r().createElement("div",{className:"rf-engraving-summary"},r().createElement(s,(0,C.A)({className:"rf-flagship-collapsable-header-text","data-autom":"engraving-summary-header"},(0,_.OH)(y))),r().createElement("div",{className:"row row-logical"},r().createElement("div",{className:"column large-9 small-8"},k.map(((e,a)=>r().createElement("div",{className:"rf-engraving-message",key:a},e&&r().createElement("p",{className:i()({[`rf-engraving-${M}`]:!!M}),role:"img","aria-label":P[a],"data-autom":H?`engraving-message${a+1}`:"engraving-message"},e))))),r().createElement("div",{className:"column large-3 small-4 rf-engraving-editbutton"},r().createElement("button",{className:"as-buttonlink icon icon-after icon-pluscircle",type:"button",onClick:()=>o("edit engraving"),ref:d,disabled:t,"data-autom":"engraving-edit","data-trigger-id":"engraving-edit"},b,r().createElement("span",(0,C.A)({className:"visuallyhidden"},(0,_.OH)(x))))),O&&r().createElement("div",{className:"rf-engraving-option-footer"},r().createElement("span",(0,_.OH)(O))))):u&&r().createElement("div",{className:"rf-engraving-collapsable"},r().createElement(w.A,{in:!0,className:"rf-flagship-collapsable-header"},r().createElement(s,(0,C.A)({className:"rf-flagship-collapsable-header-text rf-flagship-collapsable-link",onClick:()=>o("change engraving"),onKeyDown:e=>(e=>{13!==e.keyCode&&32!==e.keyCode||(e.preventDefault(),m?setTimeout((()=>o("change engraving")),400):o("change engraving"))})(e)},(0,_.OH)(f),{role:"button",tabIndex:"-1"})),r().createElement("button",(0,C.A)({className:"as-buttonlink rf-flagship-collapsable-header-button",type:"button",onClick:()=>o("change engraving"),ref:d,disabled:t,"data-autom":"changeEngraving","data-trigger-id":"engraving-change"},(0,_.OH)(`${A}\n                                <span class="visuallyhidden">\n                                    (${E} - ${f} ${N})\n                                </span>`))))))},I=e=>{let{part:a,engravingState:n,bootstrap:t,disabled:s,gallery:l,headerTag:o,summaryHeaderTag:d}=e;const{viewport:c}=(0,M.S)(),m="small"===c,{data:v,handleOpenOverlay:u,selectedOpt:E,handleAddEngravingKeyDown:f,handleEngravingChange:y,showContent:b}=n,{engravingFreeText:h,assets:A={},collapseAddEngraving:T=!0,decisionSupportTile:N}=t,{key:O="select",sectionHeader:D,addEngravingOptionTitle:$,noEngravingOptionTitle:S,addEngravingOptiondesc:V,engravingFootnoteMessage:k}=A,{savedSelection:w=[]}=v,I=(0,p.Vr)(w),R=r().useId(),L=g().get(N,"items.0.value"),F=g().get(v,"engravingDetails.engravingPageData.engravingMessageBox0.groupKey","");return r().createElement(P.c6,{legend:D&&r().createElement(o,(0,C.A)({className:"rf-engraving-section-header",tabIndex:"-1"},(0,_.OH)(D))),className:"rf-engraving-tileoptions",tag:"div"},T&&L?r().createElement(H.A,{content:L,dimensionKey:`engraving-option-${O}-${F}`,disabled:!a||s}):null,m&&!T&&l,r().createElement(P.gW,{key:"addEngraving",name:`engraving-option-${O}-${F}`,value:"addEngraving",checked:"addEngraving"===E,onKeyDown:!I&&f?f:void 0,handleChange:(e,a)=>{I||(b?setTimeout((()=>u("add free engraving",0,a)),400):u("add free engraving",0,a))},"aria-haspopup":"dialog","aria-describedby":R,"data-autom":`addEngraving-${F}`,"data-trigger-id":"engraving-add",classes:{input:"rf-engraving-tileoption-input"},disabled:!a||s,skipChangeSelection:!0,withAriaLabeledBy:!0,"aria-label":$,render:e=>{let{SelectorLabel:a}=e;return r().createElement(a,null,r().createElement("span",{className:"row row-logical"},r().createElement("span",{className:"rf-engraving-tileoption-left-col form-selector-left-col column large-9 small-8"},r().createElement(P._V,{text:$}),r().createElement("span",(0,C.A)({id:R,className:"row row-logical form-label-small rf-engraving-tileoption-desc"},(0,_.OH)(V)))),r().createElement("span",{className:"rf-engraving-tileoption-right-col form-selector-right-col form-label-small column large-3 small-4"},r().createElement("span",(0,C.A)({className:"rf-engraving-tileoption-labelright"},(0,_.OH)(h))))))}}),!T&&r().createElement(x,{engravingState:n,bootstrap:t,disabled:s,summaryHeaderTag:d}),r().createElement(P.gW,{key:"noEngraving",name:`engraving-option-${O}-${F}`,value:"noEngraving",checked:"noEngraving"===E,handleChange:()=>y(),disabled:!a||s,"data-autom":`noEngraving-${F}`,skipChangeSelection:!0,render:e=>{let{SelectorLabel:a}=e;return r().createElement(a,null,r().createElement("span",{className:"row row-logical"},r().createElement("span",{className:i()("form-selector-left-col","column","large-12")},r().createElement(P._V,{text:S}))))}}),!T&&L?r().createElement(H.A,{content:L,dimensionKey:`engraving-option-${O}-${F}`,disabled:!a||s}):null,!T&&k?r().createElement("div",(0,C.A)({id:`engraving-footer-${F}`,className:"rf-engraving-footer"},(0,_.OH)(k))):null)},R=e=>{let{part:a,engravingState:n,bootstrap:t,disabled:s,gallery:l,headerTag:o,summaryHeaderTag:d}=e;const{data:c,completed:m}=n,{collapse:v=!0,collapseAddEngraving:u=!0}=t,E=g().get(c,"isBundleEngraving",!1),{savedSelection:f=[]}=c,y=(0,p.Vr)(f),b=v?m:y;return r().createElement("div",{className:i()({"rf-engraving-nonbundle":!E,"rf-engraving-bundleview":E,"rf-engraving-nocollapsable":!u})},r().createElement("div",{className:"rf-engraving-container rf-engraving-tile"},u&&r().createElement(r().Fragment,null,r().createElement(w.x,{in:!b},r().createElement(I,{part:a,engravingState:n,bootstrap:t,disabled:s,headerTag:o,summaryHeaderTag:d})),r().createElement(w.x,{in:b},r().createElement(x,{engravingState:n,bootstrap:t,disabled:s,summaryHeaderTag:d}))),!u&&r().createElement(I,{part:a,engravingState:n,bootstrap:t,disabled:s,gallery:l,headerTag:o,summaryHeaderTag:d})))};var L=n(837),F=n(2148),B=n(3725),K=n(584);const U=e=>{let{bootstrap:a,engravingState:n,disabled:s,hideView:l,editMode:o}=e;const[d,c]=(0,t.useState)(!1),{sectionHeader:m,sectionDescription:v,saveButton:u,cancelButton:E}=g().get(a,"assets",{}),{data:y,handleEngravingChange:b,removeEngrave:h,cancelEngrave:A,onAddingMessage:T,error:N,isFetched:O,isSaving:D,saveButtonRef:$,videoRef:S,showVideo:V,onVideoEnded:k,prefersReducedMotion:w}=n,{slotName:P,selection:M=[],savedSelection:H=[]}=y,{engravingPageData:x,engravingVideo:I}=g().get(y,"engravingDetails",{}),R=(0,p.Vr)(H),U=g().get(x,"bundles",[]),{key:j=0,bundleDisplayValue:z=""}=U[0]||{},Y=g().get(M,"font")||"",G=(O&&M?M.filter((e=>!!e.errors||e.hasError)):[]).length>0,J=g().get(x,`fonts${j}`,[]),X=Y?g().findIndex(J,{fontName:Y}):0,{fontIdx:Q=0,fontName:W="",fontDisplayType:Z,fontFamily:q=""}=J[X]||{},ee=V&&!w,ae={bundleKey:j,bundleDisplayValue:z,engravingPageData:x,slotName:P,selection:M?M[j]:{},onAddingMessage:T,font:Y,disabled:s,clearField:d},ne={data:I,videoRef:S,showVideo:V,onVideoEnded:k,slotName:P,prefersReducedMotion:w};return r().createElement(r().Suspense,{fallback:null},r().createElement("div",{className:"rf-engraving-showonpage"},g().isEmpty(N)&&O?r().createElement("div",{className:"row row-logical"},r().createElement("div",{className:i()("column large-6 small-12 rf-engraving-showonpage-left",{"rf-engraving-media-content":!!I})},r().createElement(K.Dk,(0,C.A)({},ae,{selectedTab:X,showVideo:V,prefersReducedMotion:w})),I&&r().createElement(K.ZI,ne)),r().createElement("div",{className:"column large-6 small-12 rf-engraving-showonpage-right"},(!R||!l&&R)&&r().createElement("div",{className:"row row-logical"},r().createElement("h2",(0,C.A)({className:"rf-engraving-section-header",tabIndex:"-1"},(0,_.OH)(m))),v&&r().createElement("div",(0,C.A)({className:"rf-engraving-section-description"},(0,_.OH)(v))),r().createElement("div",{className:"column large-12 small-12"},r().createElement(L.A,{disabled:ee,className:i()("rf-engraving-content",{"rf-engraving-content-disabled":ee,"rf-engraving-content-hide":s})},r().createElement(F.A,(0,C.A)({},ae,{font:W,fontFamily:q,fontIdx:Q,showEmojiPicker:Z===f.MM.EMOJI_TEXT})))),r().createElement("div",{className:"column large-12 small-12 rf-engraving-actions-section"},r().createElement("button",(0,C.A)({type:"button",className:i()("button","button-block","button-super","rf-engraving-save-button",{"as-button-isloading":D}),ref:$,disabled:G||D||ee,"data-autom":"engraving-save","data-trigger-id":"engraving-save",onClick:()=>{b(!0),c(!1)}},(0,_.OH)(u))),r().createElement("button",(0,C.A)({type:"button",className:"as-buttonlink rf-engraving-skip-button","data-autom":"engraving-skip","data-trigger-id":"engraving-skip",onClick:()=>{o?A():(h(j),c(!0))}},(0,_.OH)(E))))))):r().createElement(B.A,{ariaLabel:"loading"})))},j=r().lazy((()=>n.e(5339).then(n.bind(n,6322)))),z=e=>{let{part:a,bootstrap:n,id:t,defaultEngravingOption:s,disableStorage:l=!1,disabled:g=!1,removeEngraving:o=!1,resetEngraving:d=!1,storageData:c,onCancel:m,onChange:v,onSave:u,gallery:E,hideView:y=!1,editMode:b=!1,headerTag:h="h2",summaryHeaderTag:A="div"}=e;const T=S({part:a,bootstrap:n,id:t,defaultEngravingOption:s,disableStorage:l,storageData:c,removeEngraving:o,resetEngraving:d,onCancel:m,onChange:v,onSave:u});if(!n)return null;const{view:N,additionalParam:O}=n,{overlayVisible:D,loadOverlay:$,showContent:C,showVideo:V,saveButtonRef:_,videoRef:w,data:P,isFetching:M,error:H,handleCloseOverlay:x,validateEngravingMsg:I,onAddingMessage:L,handleEngravingChange:F,cancelEngrave:B,onOverlayHidden:K,selectedBundleKey:z,onVideoEnded:Y}=T,G=(0,p.Vr)(P.savedSelection);return r().createElement(r().Fragment,null,r().createElement("div",{className:i()("rf-engraving",{"rf-engraving-isengraved":G})},(e=>{let{view:a,part:n,disabled:t,engravingState:s,bootstrap:i,gallery:l,hideView:g,editMode:o,headerTag:d,summaryHeaderTag:c}=e;switch(a){case f.Pp.TILE:return r().createElement(R,{part:n,engravingState:s,bootstrap:i,disabled:t,gallery:l,headerTag:d,summaryHeaderTag:c});case f.Pp.SHOW_ONPAGE:return r().createElement(U,{part:n,engravingState:s,bootstrap:i,disabled:t,gallery:l,hideView:g,editMode:o});case f.Pp.INLINE:default:return r().createElement(k,{part:n,engravingState:s,bootstrap:i,disabled:t})}})({view:N,disabled:g,engravingState:T,bootstrap:n,part:a,gallery:E,hideView:y,editMode:b,headerTag:h,summaryHeaderTag:A})),r().createElement(r().Suspense,{fallback:null},$&&r().createElement(j,{visible:D,showContent:C,data:P,isFetching:M,error:H,videoRef:w,showVideo:V,saveButtonRef:_,onClose:x,validateEngravingMsg:I,onAddingMessage:L,onVideoEnded:Y,saveEngrave:F,cancelEngrave:B,onOverlayHidden:K,selectedBundleKey:z,additionalParam:O})))}},5356:(e,a,n)=>{n.d(a,{A:()=>v});var t=n(8168),r=n(1594),s=n.n(r),i=n(6942),l=n.n(i),g=n(840),o=n(7924),d=n(3471),c=n(1345);const m=e=>{let{items:a,groupKey:n,groupLabel:t,id:r,dotNavFocus:i}=e;const{state:d,slideTo:c}=(0,o.$X)(),m=s().useRef([]),v=e=>a=>{m.current&&(m.current[e]=a)},u=e=>{const a=m.current?m.current[e]:null;a&&a.focus()};return s().useEffect((()=>{if(i){const e=m.current?m.current[d.index]:null;e&&e.focus()}}),[i]),a.length>1?s().createElement("div",{className:"rf-engraving-dotnav dotnav"},s().createElement("ul",{role:"tablist",className:"dotnav-items"},a.map(((e,a)=>{const i=d.index===a;return s().createElement("li",{className:"dotnav-item",role:"presentation",key:`dotnav_${n}_${e.group}`},s().createElement("button",{ref:v(a),type:"button",role:"tab","data-core-scroller-current":i?"":void 0,className:l()("dotnav-link",{current:i}),"aria-controls":`${r}-tab-item-${a}`,"aria-label":`${t} ${a+1}`,"aria-selected":i,tabIndex:i?0:-1,"data-autom":`scrollerButton${a}`,"data-trigger-id":`engraving-emoji-scroller-${a}`,onClick:()=>{c(a)},onKeyDown:e=>{switch(e.keyCode){case g.HP.ArrowUp:case g.HP.ArrowLeft:e.preventDefault(),d.deltaX||(c(a-1),u(a-1));break;case g.HP.ArrowDown:case g.HP.ArrowRight:e.preventDefault(),d.deltaX||(c(a+1),u(a+1))}}}))})))):""},v=e=>{let{bundleKey:a,groupKey:n,items:r,defaultIndex:i=0,fontIdx:l=0,selected:v="",groupLabel:u,dotNavFocus:E=!1,onHandleChange:f=()=>{}}=e;const p=s().useId(),[y,b]=s().useState(i),h=`${n}_${a}_${l}`;return s().createElement("div",{className:"rf-engraving-scroller"},s().createElement(o.HO,{index:y,"aria-label":u,handleChange:b},s().createElement(m,{items:r,groupKey:n,groupLabel:u,id:p,dotNavFocus:E}),s().createElement("div",{className:"rf-engraving-scroller-crop"},s().createElement(o.Mn,{className:"rf-engraving-scroller-item",platterAttrs:{className:"rf-engraving-scroller-platter"}},r.map(((e,a)=>{const{group:r,options:i=[]}=e,l=a===y,o=i.length-1;return s().createElement("div",{key:`${n}_${r}`,id:`${p}-tab-item-${a}`,className:"rf-engraving-scroller-itemcontent",role:"tabpanel","data-autom":`engravingEmojis${r}`,"aria-label":l?`${u} ${a+1}`:void 0,"aria-hidden":!l||void 0},s().createElement(d.N5,{withGutters:!0,className:"row row-logical",role:"list"},i.map(((e,a)=>{let{value:n,a11yText:i}=e;return s().createElement(d.Ay,(0,t.A)({name:`${h}_${r}`,value:n,key:`${h}_${r}_${n}`,checked:v===n,tagAttrs:{role:"listitem","data-option-index":`${h}_${r}_${a}`},className:"form-selector-singlecolumn column",role:"button"},(0,c.b)((e=>f(e)),!0),{onKeyDown:o>0&&(0===a||a===o)?e=>(e=>{let{evt:a,idx:n,group:t,length:r}=e;const s=a.keyCode;if(s===g.HP.ArrowUp||s===g.HP.ArrowLeft||s===g.HP.ArrowDown||s===g.HP.ArrowRight){const e=0===n?r:0,a=document.querySelector(`[data-option-index="${h}_${t}_${e}"] input`);a&&a.focus()}})({evt:e,idx:a,group:r,length:o}):void 0,tabIndex:l?0:-1,"aria-hidden":!l||void 0,"aria-label":i,"data-trigger-id":`engraving-emoji-${n}`}),s().createElement("span",{"aria-hidden":"true",translate:"no"},n))}))))}))))))}},7281:(e,a,n)=>{n.d(a,{AW:()=>l,Q5:()=>v,US:()=>o,Uy:()=>m,Ve:()=>s,Vp:()=>d,Vr:()=>c,af:()=>g,jM:()=>i});var t=n(2224),r=n.n(t);const s=e=>"emoji"===e||"char1"===e,i=(e,a)=>r().get(e,`${a}.engravedLines`,[]).map((e=>{let{value:a}=e;return a})),l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],a=arguments.length>1?arguments[1]:void 0;const n=r().uniqBy(r().get(e,`${a}.engravedLines`,[]),"font"),{font:t=""}=n[0]||{};return t},g=(e,a)=>{const n=r().find(e,(e=>e.value.fontName===a));return r().get(n,"value.fontFamily")||""},o=(e,a)=>e.map(((e,n)=>({fontIdx:n,open:a?e.value.fontName===a:0===n,...e.value}))),d=e=>{let{bundleQuantity:a=1,bundles:n=[]}=e;return Array(a).fill({}).map(((e,a)=>({...e,...n[a]?n[a]:{},key:a})))},c=e=>(e?e.filter((e=>!r().isEmpty(e)&&e.messages&&e.messages.length>0)):[]).length>0,m=(e,a)=>r().isEmpty(a)?e:e.map((e=>e?[...e].map((e=>{const n=a.find((a=>a.value===e));return n?` ${n.a11yText} `:e})).join(""):e)),v=(e,a)=>{if(!r().isEmpty(a)){const n=a.find((a=>a.value===e));return n?n.a11yText:e}return e}},7634:(e,a,n)=>{n.d(a,{A:()=>t.A});var t=n(2370)},9989:(e,a,n)=>{n.d(a,{D$:()=>s,En:()=>t,MM:()=>i,Pp:()=>r});const t=Object.freeze({OPEN_OVERLAY:"OPEN_OVERLAY",CLOSE_OVERLAY:"CLOSE_OVERLAY",SET_PART:"SET_PART",SET_DATA:"SET_DATA",FETCH_META_START:"FETCH_META_START",FETCH_META_SUCCESS:"FETCH_META_SUCCESS",FETCH_META_ERROR:"FETCH_META_ERROR",CLEAR_META_DATA:"CLEAR_META_DATA",UPDATE_SELECTION_DATA:"UPDATE_SELECTION_DATA",SAVE_SELECTION:"SAVE_SELECTION",CLEAR_SELECTION:"CLEAR_SELECTION",UPDATE_SELECTION_INBUNDLE:"UPDATE_SELECTION_INBUNDLE",HIDE_CONTENT:"HIDE_CONTENT",HIDE_VIDEO:"HIDE_VIDEO",UPDATE_VIDEO_STATE:"UPDATE_VIDEO_STATE"}),r=Object.freeze({INLINE:"inline",TILE:"tile",SHOW_ONPAGE:"showonpage"}),s=Object.freeze({line1Key:"line1",line2Key:"line2",message1Key:"msg.0",message2Key:"msg.1",message3Key:"msg.2",message4Key:"msg.3",fontKey:"f"}),i=Object.freeze({TEXT:"text",EMOJI:"select",EMOJI_TEXT:"text+select",MULTI_SELECT:"multiselect"})}}]);