/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[7560],{2385:(e,a,t)=>{t.r(a),t.d(a,{default:()=>A});var r=t(1594),l=t.n(r),o=t(6942),n=t.n(o),s=t(2224),i=t.n(s),d=t(8203),c=t(6637),p=t(2323),u=t(3416),m=t(3757),g=t(982),f=t(8168),y=t(5911),h=t(9704),b=t(6638);const v=e=>{let{typeAheadUrl:a,isTypeAheadEnabled:t,typeAheadMinChars:o,visible:s,part:d,onSubmit:c,localityLookup:p,deliveryMessage:u,a11yLabels:m={},isWarm:v,classes:E={}}=e;const{form:C,formFields:A,geoLocatedAttr:L,consent:k,isFetched:w,getConsentCookie:M,locationUpdate:N,clearGeoLookupError:S,handleInputChange:x,handleSelectChange:D,handleFormSubmit:O,handleGeoLocationClick:F,handleGeoLocationClear:G,handleGeoLocationKeyDown:T}=(0,h.P)({bootstrap:p},c),W=i().get(p,"localityLookupData.localityLookupFields.length",0),H=i().get(u,`[${d}].expanded.deliveryOptions`,[]),I=((e,a,t,r)=>e?a?r.loading:`${t} ${1===t?r.result:r.results}`:"")(v,u.fetching,H.length,m),$=i().get(k,"cookieCompliance.showConsent",!1),[P,R]=(0,r.useState)(!1),Z=i().get(u,"errorMessage",""),U={buttonText:i().get(p,"localityLookupData.buttonText","")},V=(0,g.zP)({form:"rf-dude-form",multipleGeo:"rf-dude-form-multiple-geo",singleGeo:"rf-dude-form-single-geo",numFields:"rf-dude-form-num-fields",fieldWrapper:"rf-dude-form-field-wrapper",formText:"rf-dude-form-text",formTextGeoLocated:"rf-dude-form-text-geo-located",formErrorMessage:"rf-dude-form-error-message",formButton:"rf-dude-form-button",geoSpan:"rf-dude-form-geo-span",geoIcon:"rf-dude-form-geo-icon",geoErrorMessage:"rf-dude-form-geo-error-message",locateGeo:"rf-dude-form-locate-geo",clearGeo:"rf-dude-form-clear-geo",errorGeo:"rf-dude-form-error-geo",deliveryErrorMessage:"rf-dude-form-delivery-error-message typography-caption",consentCheckbox:"rf-dude-form-consent",consentCheckboxMOW:"rf-dude-form-consent-mow"},E);l().useEffect((()=>{s&&($&&M(),R(!0),N(),S())}),[s]);return l().createElement("form",{className:n()(V.form,`${V.numFields}-${W}`,{[V.multipleGeo]:W>1,[V.singleGeo]:1===W}),onSubmit:e=>O(e),noValidate:!0},l().createElement(h.A,{typeAheadUrl:a,isTypeAheadEnabled:t,typeAheadMinChars:o,form:C,formFields:A,geoLocatedAttr:L,consent:k,a11yAssets:{a11ytext:I},isFetched:w,showButton:P,buttonTextAttr:U,clearGeoLookupError:S,fireIconMicroEvent:b.kj,handleInputChange:x,handleSelectChange:D,handleGeoLocationClick:F,handleGeoLocationClear:G,handleGeoLocationKeyDown:T,handleDudeFormSubmit:(e,a)=>{O(e,a)}}),Z&&l().createElement("div",(0,f.A)({className:V.deliveryErrorMessage,"data-autom":"deliveryErrorMessage"},(0,y.OH)(Z))))},E=e=>{let{options:a=[],vertical:t,classes:r={},ariaLabel:o=""}=e;const s=(0,g.zP)({table:"rf-dude-overlay-table typography-body-reduced",tableVertical:"rf-dude-overlay-table-vertical",loading:"rf-dude-overlay-loading",tableRow:"rf-dude-overlay-table-row",tableCell:"rf-dude-overlay-table-cell",tableCellDescription:"rf-dude-overlay-table-cell-description",tableCellDate:"rf-dude-overlay-table-cell-date",tableCellDateInfo:"rf-dude-overlay-table-cell-dateinfo typography-caption",tableCellShipping:"rf-dude-overlay-table-cell-shipping"},r);return l().createElement("ul",{role:"list","aria-label":o,className:n()(s.table,{[s.tableVertical]:t})},a.map((e=>l().createElement("li",{role:"listitem",key:`${e.date}_${e.displayName}`,className:s.tableRow},l().createElement("span",{className:n()(s.tableCell,s.tableCellDescription)},e.displayName),l().createElement("span",{className:n()(s.tableCell,s.tableCellDate)},e.date,e.additionalContent&&l().createElement("span",(0,f.A)({className:s.tableCellDateInfo},(0,y.OH)(e.additionalContent)))),l().createElement("span",{className:n()(s.tableCell,s.tableCellShipping)},e.shippingCost)))))},C=e=>{let{visible:a,part:t,deliveryMessage:r,userState:o,onClose:s,onFormSubmit:f,a11yLabels:y={},classes:h={}}=e;const b=i().get(window,"LOCATION_BOOTSTRAP",{}),C=i().get(window,"fulfillmentBootstrap",{}),A=i().get(r,"availableOptionsText",""),L=i().get(r,`[${t}].expanded.deliveryOptions`,[]),k=i().get(r,`[${t}].expanded.subHeader`,"")||i().get(r,`[${t}].regular.subHeader`,""),w=(0,g.zP)({overlay:"rf-dude-overlay",overlayContent:"rf-dude-overlay-content",overlayClose:"rf-dude-overlay-close",headingWrapper:"rf-dude-overlay-heading-wrapper",heading:"rf-dude-overlay-heading typography-headline-reduced",subHeading:"rf-dude-overlay-sub-heading typography-body",formTableWrapper:"rf-dude-overlay-form-table-wrapper",tableWrapper:"rf-dude-overlay-table-wrapper",tableWrapperCold:"rf-dude-overlay-table-wrapper-cold",loading:"rf-dude-overlay-loading",loadingIndicator:"rf-dude-overlay-loading-indicator",locationLabel:"rf-dude-overlay-location-label typography-sosumi",geoNotice:"rf-dude-overlay-geo-notice typography-caption"},h),{typeAheadUrl:M,isTypeAHeadEnabledForDude:N,typeAheadMinChars:S}=C,x=l().useRef(null),D=i().get(b,"localityLookupData.overleyHeader",""),O=i().get(b,"localityLookupData.geoNotice",""),[F,G]=l().useState(!1),[T,W]=l().useState(!1);l().useEffect((()=>{if(F&&D){const e=x.current?x.current.querySelector("[data-core-overlay-content]"):null;e&&e.focus()}}),[F,D]);const{viewport:H,assets:I}=(0,c.S)(),$=i().get(b,"localityLookupData.localityLookupFields.length",1)>1,P=o===m.Y.WARM||o===m.Y.GEO_WARM,R=!T&&O?l().createElement("div",{className:w.geoNotice},O):null;return l().createElement(u.eA,{visible:a,onClose:s,classes:{root:w.overlay,content:w.overlayContent},fixedWidth:!0,ariaLabel:"rf-dude-overlay-label",disableFocusIn:!0,onEntered:()=>G(!0),onExited:()=>{G(!1),W(!0)},onEnter:()=>{T&&W(!1)},appear:!0,"data-autom":"dudeOverlayContainer",ref:x,elementAfterFootnotes:R},!T&&!i().isEmpty(b)&&l().createElement(l().Fragment,null,l().createElement("div",{className:w.headingWrapper},l().createElement("h2",{id:"rf-dude-overlay-label",className:w.heading},D)),l().createElement("p",{className:w.subHeading},k),l().createElement("div",{className:n()(w.formTableWrapper,"row")},l().createElement("div",{className:n()({"large-5 small-12":$,"large-12 small-12":!$})},l().createElement(v,{typeAheadUrl:M,isTypeAheadEnabled:N,typeAheadMinChars:S,visible:a,part:t,localityLookup:b,deliveryMessage:r,isWarm:P,onSubmit:f,a11yLabels:y,classes:h})),l().createElement("div",{className:n()(w.tableWrapper,{[w.tableWrapperCold]:!P,[w.loading]:r.fetching,"large-6 large-offset-1 small-12 small-offset-0":$,"large-12 small-12":!$})},l().createElement(d.W,{in:r.fetching,className:w.loadingIndicator}),!r.fetching&&!P&&l().createElement("span",{className:w.locationLabel},b.localityLookupData.locationLabel),!r.fetching&&P&&l().createElement(E,{options:L,vertical:"small"===H||$,classes:h,ariaLabel:A})))),l().createElement(p.A,{messages:{pending:i().get(I,"loading"),finished:i().get(I,"finished")},inline:!0,isPending:r?.fetching}))},A=e=>{let{fulfillment:a}=e;return l().createElement(C,{visible:a.overlayDudeVisible,part:a.overlayPart,userState:a.userState,deliveryMessage:a.deliveryMessage,localityLookup:a.localityLookup,geoLookupUrl:a.geoLookupUrl,onClose:a.handleCloseDudeOverlay,onFormSubmit:a.handleDudeFormSubmit,a11yLabels:a.overlayDudeA11yLabels})}},8203:(e,a,t)=>{t.d(a,{A:()=>u,W:()=>p});var r=t(8168),l=t(1594),o=t.n(l),n=t(6942),s=t.n(n),i=t(5067),d=t(5911);const c=o().forwardRef(((e,a)=>{let{visible:t=!0,tiny:l,elevated:n,ariaLabel:i,className:c,...p}=e;return o().createElement("div",(0,r.A)({ref:a,className:s()("progress-indicator-container",c),role:"progressbar"},p),o().createElement("div",(0,r.A)({className:s()("progress-indicator","progress-indicator-indeterminate",{"progress-indicator-indeterminate-size-tiny":l,"progress-indicator-indeterminate-size-elevated":n,"progress-indicator-visible":t}),"aria-label":i},(0,d.OH)('\n<svg class="progress-indicator-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 56 56" aria-hidden="true">\n    <g class="progress-indicator-spokes">\n        <path class="progress-indicator-spoke" d="M28,8.5A2.5,2.5,0,0,1,30.5,11v7a2.5,2.5,0,0,1-5,0V11A2.5,2.5,0,0,1,28,8.5Z"></path>\n        <path class="progress-indicator-spoke" d="M41.79,14.21a2.52,2.52,0,0,1,0,3.54L36.84,22.7a2.5,2.5,0,0,1-3.54-3.54l5-4.95A2.52,2.52,0,0,1,41.79,14.21Z"></path>\n        <path class="progress-indicator-spoke" d="M47.5,28A2.5,2.5,0,0,1,45,30.5H38a2.5,2.5,0,0,1,0-5h7A2.5,2.5,0,0,1,47.5,28Z"></path>\n        <path class="progress-indicator-spoke" d="M41.79,41.79a2.52,2.52,0,0,1-3.54,0l-5-4.95a2.5,2.5,0,0,1,3.54-3.54l4.95,5A2.52,2.52,0,0,1,41.79,41.79Z"></path>\n        <path class="progress-indicator-spoke" d="M28,47.5A2.5,2.5,0,0,1,25.5,45V38a2.5,2.5,0,0,1,5,0v7A2.5,2.5,0,0,1,28,47.5Z"></path>\n        <path class="progress-indicator-spoke" d="M14.21,41.79a2.52,2.52,0,0,1,0-3.54l4.95-5a2.5,2.5,0,0,1,3.54,3.54l-4.95,4.95A2.52,2.52,0,0,1,14.21,41.79Z"></path>\n        <path class="progress-indicator-spoke" d="M8.5,28A2.5,2.5,0,0,1,11,25.5h7a2.5,2.5,0,0,1,0,5H11A2.5,2.5,0,0,1,8.5,28Z"></path>\n        <path class="progress-indicator-spoke" d="M14.21,14.21a2.52,2.52,0,0,1,3.54,0l4.95,4.95a2.5,2.5,0,0,1-3.54,3.54l-4.95-4.95A2.52,2.52,0,0,1,14.21,14.21Z"></path>\n    </g>\n</svg>'))))}));c.displayName="ProgressIndicator";const p=e=>{let{in:a,transitionProps:t={},...l}=e;const n=o().useRef(),{mountOnEnter:s=!0,unmountOnExit:d=!0,...p}=t;return o().createElement(i.Ay,(0,r.A)({in:a,timeout:400,nodeRef:n,mountOnEnter:s,unmountOnExit:d},p),(e=>o().createElement(c,(0,r.A)({ref:n,visible:"entering"===e||"entered"===e},l))))},u=c}}]);