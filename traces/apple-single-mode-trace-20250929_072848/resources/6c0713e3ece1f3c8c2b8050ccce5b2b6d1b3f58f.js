/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[892],{6271:(e,t,r)=>{r.r(t),r.d(t,{default:()=>ae});var a=r(8168),o=r(1594),s=r.n(o),l=r(2224),n=r.n(l),c=r(6942),i=r.n(c),m=r(7427),u=r(3510),p=r(6884),d=r(1265),E=r(6637),h=r(5911),f=r(3416),g=r(2905),b=r(5646),v=r(266),y=r(3658);const N=(e,t)=>r=>{const a=(0,b.tG)("storelocator/fetchPickupData");r({type:g.E.SL_FETCH_PICKUP_START});const o=(0,v.q)((0,y.bj)(e,t));return o.then((e=>({body:n().get(e,"body.content.pickupMessage")}))).then((e=>{if(e.body){const t=n().get(e.body,"errorMessage");t?(a.debug(`ResponseError: ${t}`),r({type:g.E.SL_FETCH_PICKUP_ERROR,error:{isResponseError:!0,message:t}})):(e.body.stores=e.body.stores.slice().map((e=>{const t=n().get(e,"partsAvailability",{}),r=n().keys(t)[0];return e.partsAvailabilityNorm=n().get(t,r,{}),e})),r({type:g.E.SL_FETCH_PICKUP_SUCCESS,data:e.body}))}})).catch((e=>{a.error(e),r({type:g.E.SL_FETCH_PICKUP_ERROR,error:{isNetworkError:!0,message:e}})})),o},S={isFetching:!1,isFetched:!1,data:{},error:null,isShown:!0,isToggleBtnNeeded:!1},A=(e,t)=>{switch(t.type){case g.E.SL_FETCH_PICKUP_START:return{...e,isFetching:!0,isFetched:!1,error:null};case g.E.SL_FETCH_PICKUP_ERROR:return{...e,isFetching:!1,isFetched:!0,error:t.error,data:{}};case g.E.SL_FETCH_PICKUP_SUCCESS:return{...e,isFetching:!1,isFetched:!0,error:null,data:t.data};case g.E.SL_CLEAR_PICKUP_DATA:return S;default:return e}};var C=r(8994);let k={autoCompleteDebounceInterval:200,pickupRequest:{searchNearby:!0}};var T=r(1644),D=r(2662),w=r(9283),O=r(2787),x=r(8595),L=r(2096);const F="search",H=e=>{let{fieldData:t,form:r,inputProps:o,setOptions:l,inputRef:n,resetA11yText:c,onReset:i=()=>{},errorMessageId:m=""}=e;return s().createElement(x.A,(0,a.A)({type:"text","data-autom":"zipCode",name:F,search:!0,label:t.searchPlaceholder,autoComplete:"off",maxLength:t.maxlength,value:r.values[F],onChange:r.handleChange,error:r.touched[F]&&r.errors[F],onReset:()=>{r.setFieldValue(F,""),l([]),i()},resetA11y:c,ref:n},o,{onBlur:e=>{o?.onBlur&&o.onBlur?.(),r.handleBlur(e)},"aria-describedby":m}))},R=e=>{let{form:t,format:r={},inputRef:a,inputProps:o,...l}=e;const n=(0,D.Ay)({value:t.values[F]||"",format:r}),c=(0,h.E2)(a,n.ref),i={form:t,...l,inputRef:c,inputProps:{...o,onChange:e=>{const{value:r}=n.onChange(e);t.setFieldValue(F,r)},onPaste:n.handlePaste}};return s().createElement(H,i)},_=e=>{const t=n().get(e,"fieldData.validation.zip.format");return t?s().createElement(R,(0,a.A)({},e,{format:t})):s().createElement(H,e)},I=e=>{let{onSubmit:t,fieldData:r,consent:a,classes:o={},inputValue:l,errorMessageId:c,searchInputRef:u}=e;const{initialValues:p,validationSchema:d}=s().useMemo((()=>((e,t)=>{const r=n().get(e,"validation.zip",{}),a={[F]:t||""},o={};return o[F]=[],o[F].push({type:"required",message:r.requiredError}),e.isAutoCompleteEnabled||o[F].push({type:"regEx",regEx:r.required?[r.pattern]:[r.pattern,"^$"],message:r.invalidFormatError}),{initialValues:a,validationSchema:o}})(r,l)),[r,l]),{isTypeAHeadEnabledForAPU:h}=r,[f,g]=s().useState([]),N=(0,O.A)({initialValues:p,validationSchema:d,onSubmit:t}),{consent:S,handleChange:A,showConsent:C}=a.cookieCompliance||{},[k,T]=(0,m.oM)({resetDelay:400}),D=n().get(p,F),x=n().get(r,"resultVoText"),H=n().get(r,"resultsVoText");s().useEffect((()=>{N.resetForm(),N.setValues(p),u.current&&N.registerFieldRef(u.current)}),[D]);const{assets:R}=(0,E.S)(),I=n().get(R,"reset");return s().createElement("div",{className:i()("rf-storelocator-search",o.search)},s().createElement("form",{className:"rf-storelocator-form",onSubmit:N.handleSubmit},s().createElement("div",{className:"field-wrapper row",key:F},s().createElement("div",{className:"large-8 small-12"},h?s().createElement(s().Fragment,null,s().createElement(w.A,{value:N.values[F],onChange:e=>{const t=(0,b.tG)("storelocator/SearchForm/onChange");N.setFieldValue(F,e.target.value),e.target?.value?.length>=r.typeAheadMinChars&&(0,v.q)((0,y.bj)(r.typeAheadUrl,{value:e.target.value})).then((e=>{const t=n().get(e,"body.suggestions",[]).map((e=>e.displayValue));g(t);const r=x&&H?`${t.length} ${1===t.length?x:H}`:"";T(r)})).catch((e=>t.error(e)))},onSelection:e=>{N.setFieldValue(F,e),t({[F]:e})},options:f,getPosition:e=>e?{width:e.width,left:0}:{},renderInput:e=>s().createElement(_,{fieldData:r,form:N,inputProps:{...e,ref:u},onReset:()=>{u.current&&u.current.focus()},setOptions:g,resetA11yText:I,errorMessageId:c}),classes:{root:"rf-storelocator-autocomplete"}}),s().createElement(m.Ay,{inline:!0,message:k})):s().createElement(_,{fieldData:r,form:N,setOptions:g,inputRef:u,onReset:()=>{u.current&&u.current.focus()},resetA11yText:I,errorMessageId:c})),s().createElement("div",{className:"large-4 small-12"},s().createElement("button",{type:"submit",className:"button button-super button-block rf-storelocator-searchbutton","data-autom":"storeSearchButton"},r.searchButton))),C?s().createElement("div",{className:"row"},s().createElement(L.f,{consent:S,handleChange:A,label:a.label})):null))};I.displayName="SearchForm";const V=I;var U=r(7213),P=r(9904);const B=e=>{let{onSubmit:t,fieldData:r,consent:a,inputValue:o,classes:l={}}=e;const[c,m]=s().useState(!1),[u,p]=s().useState(),d=s().useRef(!1),E=s().useRef(),{selectLocationLabel:h,searchForStoresNearLabel:f,showingStoresNearLabel:g,provinceSelectorTabs:b,addressLookupUrl:v}=r,y=s().useMemo((()=>{const e=b.map((e=>e.name));return b.map(((t,r)=>({...t,update:e.slice(r+1)})))}),[b]),N=(0,P.vH)({addressLookupUrl:v,provinceSelectorTabs:y,onChange:e=>{E.current&&E.current.focus(),p(e),m(!1),t({search:e})}}),{consent:S,handleChange:A,showConsent:C}=a.cookieCompliance||{},k=u&&n().get(N.selection,"city")||o;return s().createElement("div",{className:i()("rf-storelocator-search-province-selector",l.search)},s().createElement("form",{className:"rf-storelocator-province-selector-form"},s().createElement("span",null,k?g:f),s().createElement("button",{type:"button",className:i()(l.button,"rf-storelocator-province-selector-button","icon","icon-after",{"icon-chevrondown":!c,"icon-chevronup":c}),onClick:()=>{m(!c),c||(d.current=!0)},ref:E,"aria-expanded":c},k||h),s().createElement("div",{className:"column large-12 small-12"},s().createElement(U.x,{in:c},C?s().createElement(L.f,{consent:S,handleChange:A,label:a.label}):null,d.current&&s().createElement(P.Ay,{activeTab:N.activeTab,onTabChange:N.setActiveTab,selection:N.selection,onSelection:N.handleSelection,tabs:N.tabs,desktopColumns:4,mobileColumns:2})))))};var M=r(4067),$=r(4175);const q=e=>{let{localityLookupUrl:t,onSubmit:r,consent:a,inputValue:o,inputRef:l}=e;const[c,i]=s().useState({}),[m,u]=s().useState({}),[p,d]=s().useState(!1),{consent:E,handleChange:h,showConsent:f}=a.cookieCompliance||{};s().useEffect((()=>{d(!0),(0,v.q)(t).then((e=>{const t=n().get(e,"body");i(t);const r=t?.localityLookupFields.reduce(((e,r)=>({...e,...t[r],name:r})),{});u(r)})).finally((()=>{d(!1)}))}),[t]);const{initialValues:g,validationSchema:b}=s().useMemo((()=>((e,t)=>{const r={},a={},o=n().get(e,"localityLookupFields",[]);return e&&o.forEach((o=>{const s=e[o];"select"===s.type&&s.data&&(r[o]=t||s.data.reduce(((e,t)=>"true"===t.selected?t.value:e),"")),a[o]=[],s.required&&a[o].push({type:"required",message:s.requiredErrorMsg})})),{initialValues:r,validationSchema:a}})(c,o)),[c,o]);s().useEffect((()=>{y.resetForm(),y.setValues(g)}),[g]);const y=(0,O.A)({initialValues:g,validationSchema:b,onSubmit:r}),{name:N,data:S={},type:A,required:C}=m,k=S[0]?.text;return s().createElement(s().Fragment,null,!p&&m&&"select"===A&&s().createElement("div",{className:"rf-storelocator-form row"},s().createElement("div",{className:"large-8 small-12"},s().createElement(M.A,{key:N,name:N,value:y.values[N],classes:{error:"rc-location-form-errormsg"},onBlur:y.handleBlur,onChange:e=>{y.handleChange(e)},label:k,disabled:S.length<=1,required:C,error:S.length>1&&y.touched[N]&&y.errors[N],ref:l},S.map((e=>s().createElement(M.n,{value:e.value,key:e.value,disabled:e.isDisabled},e.text))))),s().createElement("div",{className:"large-4 small-12"},s().createElement("button",{type:"submit",className:"button button-super button-block rf-storelocator-viewoptionsbutton","data-autom":"storeViewOptionshButton",onClick:()=>r({search:y.values[N]})},n().get(c,"buttonText")))),p&&s().createElement($.A,{className:"rf-cityselector-spinner"}),s().createElement("div",{className:"row"},f?s().createElement(L.f,{consent:E,handleChange:h,label:a.label}):null))};var Y=r(3471),K=r(632),j=r(1345),W=r(2547);const z=e=>{let{stores:t,storeNumber:r,assets:o,handleStoreChange:l,handleShowStoreDetails:c,legend:m=""}=e;return s().createElement("div",{className:"rf-storelocator-stores"},s().createElement(K.c6,{name:"store",legend:m},t&&t.map((e=>{const t=e.storeNumber===r;return s().createElement(Y.Ay,(0,a.A)({key:e.storeNumber,name:`store-${e.partsAvailabilityNorm.partNumber}`,value:e.storeNumber,checked:t,withAriaLabeledBy:!0},(0,j.b)((e=>l(e)),!0),{className:i()("rf-storelocator-storeoption","form-selector-twocol-threeline",{selected:t},{disabled:!n().get(e,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1)}),render:t=>{let{SelectorLabel:r}=t;return s().createElement(s().Fragment,null,s().createElement(r,{classes:{root:"rf-storelocator-storeoption-label"}},s().createElement("span",{className:"row"},s().createElement("span",{className:i()("form-selector-left-col","column","large-6","rf-storelocator-storeitem-storeinfo")},e.recommendationLabel&&s().createElement(W.A,{text:e.recommendationLabel}),s().createElement(K._V,{text:e.address.address}),s().createElement(K.ED,{text:e.city}),s().createElement(K.ED,{text:e.storeDistanceWithUnit})),s().createElement("span",{className:i()("form-selector-right-col","column","large-6","rf-storelocator-storeitem-availabilityinfo")},s().createElement("span",(0,h.OH)(e.partsAvailabilityNorm.pickupSearchQuote)),e.partsAvailabilityNorm.pickupType&&s().createElement(K.ED,{text:e.partsAvailabilityNorm.pickupType})))),s().createElement("button",{className:"rf-storelocator-storeitem-showdetails",type:"button",onClick:()=>c(e.storeNumber,!0),"aria-label":`${o.viewDetailsText} - ${e.storeName}`},s().createElement("span",null,o.viewDetailsText)))}}))}))))};z.displayName="StoreLocatorStoresList";const G=z;var Q=r(4768);const J=e=>{let{storeItem:t,assets:r,handleShowStoreDetails:o,handleSaveSelection:l,coldStartMessage:c}=e;const{viewport:m}=(0,E.S)(),u="small"===m,{selectButton:p,closebuttonVOText:d,moreInformation:g,moreInformationVoText:b,viewMoreHoursLinkText:v,viewMoreHoursVoText:y}=r,N=n().get(t,"specialHours.viewAllSpecialHours"),S=n().get(t,"address.address",""),A=n().get(t,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1);return s().createElement(s().Fragment,null,s().createElement("div",{className:"rf-storelocator-storedetails"},s().createElement(f.An.Consumer,null,(e=>s().createElement(s().Fragment,null,s().createElement("div",{className:"rf-storelocator-storeimgcontainer large-centered small-centered"},s().createElement("img",{src:t.storeImageUrl,alt:""})),s().createElement("button",{className:i()("rf-storelocator-profileclose",Q.A.HEADLINE),type:"button",onClick:()=>o()},s().createElement("span",{className:"visuallyhidden"},d)),s().createElement("div",{className:"rf-storelocator-storedetailscontainer"},s().createElement("h3",{className:Q.A.EYEBROW},t.address.address),s().createElement("div",{className:Q.A.BODY_REDUCED},t.address&&s().createElement(s().Fragment,null,s().createElement("span",(0,a.A)({className:"row"},(0,h.OH)(t.address.address2))),t.address.address3&&s().createElement("span",(0,a.A)({className:"row"},(0,h.OH)(t.address.address3)))),s().createElement("span",{className:"row"},t.state?`${t.city}, ${t.state}`:`${t.city}`),s().createElement("span",{className:"row"},t.address.postalCode),s().createElement("span",{className:"row"},t.phoneNumber),t.pickupTypeAvailabilityText&&s().createElement("span",{className:"row rf-storelocator-pickuptypeinfo"},t.pickupTypeAvailabilityText),t.reservationUrl&&s().createElement("a",{className:"more rf-storelocator-reservationurl",href:t.reservationUrl,target:"_blank",rel:"noopener noreferrer"},g,s().createElement("span",{className:"visuallyhidden"},b)),N&&s().createElement("a",{className:"more rf-storelocator-specialhoursurl",href:N.directionsUrl,target:"_blank",rel:"noopener noreferrer"},"View all upcoming special hours",s().createElement("span",{className:"visuallyhidden"},b))),s().createElement("div",{className:i()("rf-storelocator-storehoursinfo",Q.A.BODY_REDUCED)},t.storeHours&&s().createElement("div",{className:"rf-storelocator-storehours"},s().createElement("span",{className:"visuallyhidden"},t.storeHours.storeHoursText),t.storeHours.hours&&t.storeHours.hours.map((e=>s().createElement("div",{key:`${t.storeNumber}-${e.storeDays}`,className:"rf-storelocator-storehours-item row"},s().createElement("span",(0,a.A)({className:"rf-storelocator-storehoursdays large-4 small-4"},(0,h.OH)(e.storeDays))),s().createElement("span",(0,a.A)({className:"rf-storelocator-storehourstiming large-8 small-8"},(0,h.OH)(e.storeTimings))))))),t.hoursUrl&&s().createElement("a",{className:"more rf-storelocator-hoursurl",href:t.hoursUrl,target:"_blank",rel:"noopener noreferrer"},v,s().createElement("span",{className:"a11y"},y)),t.specialHours&&t.specialHours.specialHoursData&&s().createElement(s().Fragment,null,s().createElement("div",{className:"rf-storelocator-specialhours"},s().createElement("p",(0,h.OH)(t.specialHours.specialHoursText)),t.specialHours.specialHoursData.map((e=>s().createElement("div",{key:`${t.storeNumber}-${e.specialDays}`,className:"rf-storelocator-storehours-item"},s().createElement("span",(0,a.A)({className:"rf-storelocator-storehoursdays"},(0,h.OH)(e.specialDays))),s().createElement("span",(0,a.A)({className:"rf-storelocator-storehourstiming"},(0,h.OH)(e.specialTimings))))))),N&&s().createElement("a",{className:"more rf-storelocator-specialhoursurl",href:N.directionsUrl,target:"_blank",rel:"noopener noreferrer"},"View all upcoming special hours",s().createElement("span",{className:"a11y"},b))),c&&s().createElement("div",(0,a.A)({className:"rf-storelocator-personalsetup"},(0,h.OH)(c)))),u&&t.pickupOptionsDetails&&s().createElement("div",{className:i()("rf-storelocator-pickupdetails",Q.A.BODY_REDUCED)},s().createElement("div",(0,h.OH)(t.pickupOptionsDetails.whatToExpectAtPickup)),t.pickupOptionsDetails.pickupOptions&&t.pickupOptionsDetails.pickupOptions.map((e=>s().createElement("ul",{key:e.index,className:"form-selector-group"},s().createElement("li",{className:"rf-storelocator-pickupoption"},s().createElement("div",null,e.pickupOptionTitle),s().createElement("div",null,e.pickupOptionDescription))))),s().createElement("div",(0,h.OH)(t.pickupOptionsDetails.comparePickupOptionsLink)))),!n().isEmpty(e)&&s().createElement(f.gn,{className:"rc-overlay-footnotes rf-storelocator-footer",footnotes:e}))))),s().createElement("div",{className:"rf-storelocator-selectaction"},s().createElement("button",{className:"button button-block","data-autom":"storeSelectButton",type:"button",onClick:()=>l(),disabled:!A},s().createElement("span",{className:"select-store","aria-hidden":"true"},p),s().createElement("span",{className:"visuallyhidden"},`${p} ${S}`))))};J.displayName="StoreDetails";const X=J,Z=()=>s().createElement("ul",{className:"form-selector-group"},s().createElement("li",{className:"rf-storelocator-storeitem rf-storelocator-storeitem-emptycontent"}),s().createElement("li",{className:"rf-storelocator-storeitem rf-storelocator-storeitem-emptycontent"}),s().createElement("li",{className:"rf-storelocator-storeitem rf-storelocator-storeitem-emptycontent"}),s().createElement("li",{className:"rf-storelocator-storeitem rf-storelocator-storeitem-emptycontent"}),s().createElement("li",{className:"rf-storelocator-storeitem rf-storelocator-storeitem-emptycontent"}),s().createElement("li",{className:"rf-storelocator-storeitem rf-storelocator-storeitem-emptycontent"})),ee=e=>{let{bootstrap:t,part:r,options:o,productTitle:l,storeId:c,consent:b,visible:v,onClose:y,onStoreChange:D,stickyTrigger:w}=e;const O=(e=>{let{bootstrap:t,part:r,options:a,storeId:o,visible:l,onStoreChange:c,stickyTrigger:i}=e;k={...k,...t};const[m,u]=s().useState(null),[p,d]=s().useState(null),[E,h]=s().useState(!1),[f,b]=s().useState(!1),[v,y]=s().useReducer(A,S),T=s().useRef(null),D=a?Object.values(a).join(","):null;s().useEffect((()=>{if(l&&r&&o){u(o),h(!0);const e=D?{"option.0":D}:{};N(k.url,{...k.pickupRequest,"parts.0":r,...e,store:o})(y)}l&&r&&((0,C.Y$)(r,i),(0,C.gE)(o,r))}),[l,r,D,o]);const w=()=>{y({type:g.E.SL_CLEAR_PICKUP_DATA}),d(null),u(null),h(!1)};return{state:v,isWarm:E,showStoreDetails:f,location:p,store:m,handleFormSubmit:e=>{if(e.search){h(!0),d(e.search);const t=D?{"option.0":D}:{};N(k.url,{...k.pickupRequest,"parts.0":r,...t,location:e.search})(y).then((e=>{const t=n().get(e,"body.content.pickupMessage.stores[0].storeNumber");t&&u(t);const r=n().get(e,"body.content.pickupMessage.errorMessage");T.current&&r&&setTimeout((()=>{T.current.focus()}),100)}))}},handleStoreChange:e=>u(e),handleShowStoreDetails:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e&&u(e),b(t)},handleSaveSelection:()=>{c&&c({store:m}),(0,C.tj)(),w()},clearOverlayData:w,searchInputRef:T}})({bootstrap:t,visible:v,part:r,options:o,storeId:c,onStoreChange:D,stickyTrigger:w}),{viewport:x}=(0,E.S)(),[L,F]=s().useState(!1),[H,R]=s().useState(!!t?.personalPickupTextFragmentkey),[_,I]=(0,m.oM)({resetDelay:4e3}),U=s().useId(),{searchHeader:P,personalPickup:M,personalSetupTextFragmentKey:$,personalPickupTextFragmentkey:Y,validation:K,searchPlaceholder:j,searchButton:W,searchInputClass:z,maxlength:Q,autocompleteOn:J,autosaveValue:ee,availabilityFor:te,selectButton:re,mowSelectButton:ae,moreInformation:oe,moreInformationVoText:se,closebuttonVOText:le,resultVoText:ne,resultsVoText:ce,provinceSelector:ie,showingOptionsLabel:me,selectLocationLabel:ue,searchForStoresNearLabel:pe,showingStoresNearLabel:de,provinceSelectorTabs:Ee,addressLookupUrl:he,typeAheadUrl:fe,typeAheadMinChars:ge,isTypeAHeadEnabledForAPU:be,enableCitySelector:ve,localityLookupUrl:ye}=t,{isWarm:Ne,store:Se,location:Ae,handleStoreChange:Ce,state:ke,clearOverlayData:Te,showStoreDetails:De,handleShowStoreDetails:we,handleFormSubmit:Oe,handleSaveSelection:xe,searchInputRef:Le}=O,Fe="small"===x,{isFetching:He,isFetched:Re,error:_e,data:Ie={}}=ke,{stores:Ve=[],viewMoreHoursLinkText:Ue,viewMoreHoursVoText:Pe,viewDetailsText:Be}=Ie,Me=n().get(ke,"data.stores",[]).length,$e=1===Me?ne:ce;s().useEffect((()=>{I(`${Me} ${$e}`)}),[Re]);const qe={viewMoreHoursLinkText:Ue,viewMoreHoursVoText:Pe,viewDetailsText:Be,selectButton:re,mowSelectButton:ae,moreInformation:oe,moreInformationVoText:se,closebuttonVOText:le},Ye=Ae||n().get(ke,"data.locationLabel")||n().get(ke,"data.location"),Ke=Ve.find((e=>e.storeNumber===Se)),je=n().get(Ke,"address.address",""),We=n().get(Ke,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1),ze={validation:K,searchPlaceholder:j,searchButton:W,searchInputClass:z,maxlength:Q,autocompleteOn:J,autosaveValue:ee,typeAheadUrl:fe,typeAheadMinChars:ge,isTypeAHeadEnabledForAPU:be,resultVoText:ne,resultsVoText:ce},Ge=n().get(ke,"data.legendLabelText",""),Qe=s().createElement("span",(0,a.A)({className:"visuallyhidden"},(0,h.OH)(Ge)));return s().createElement(p.A,{visible:v,classes:{root:i()("rf-storelocator-overlay",{"rf-storelocator-storedetails-shown":Fe&&De}),content:"rf-storelocator-overlay-content"},fixedWidth:!0,noPadding:!0,appear:!0,ariaLabel:"rf-storelocator-overlay-label",onClose:()=>{y(),(0,C.kE)(),Te(),R(!!Y)},onEntered:()=>F(!0),onExited:()=>F(!1)},He&&s().createElement(T.A,{ariaLabel:"loading",entered:L}),Re&&Me>0&&s().createElement(m.Ay,{message:_}),s().createElement(f.Og,null,s().createElement("div",{className:i()("rf-storelocator-headersection",{"rf-storelocator-loading":He})},s().createElement("h2",(0,a.A)({id:"rf-storelocator-overlay-label",className:i()("rf-storelocator-header",d.A.HEADLINE_REDUCED)},(0,h.OH)(P))),l&&s().createElement("div",{className:"rf-storelocator-productinfo"},s().createElement("span",{className:i()("rf-storelocator-labeltext",d.A.BODY)},te),s().createElement("span",{className:d.A.BODY},l)),ie&&s().createElement(B,{fieldData:{showingOptionsLabel:me,selectLocationLabel:ue,searchForStoresNearLabel:pe,showingStoresNearLabel:de,provinceSelectorTabs:Ee,addressLookupUrl:he},onSubmit:Oe,inputValue:Ye,consent:b}),!ie&&!ve&&s().createElement(V,{fieldData:ze,onSubmit:Oe,inputValue:Ye,consent:b,error:_e,errorMessageId:U,searchInputRef:Le}),ve&&s().createElement(q,{localityLookupUrl:ye,onSubmit:Oe,consent:b,inputValue:Ae})),s().createElement("div",{className:i()("rf-storelocator-contentsection",{"rf-storelocator-loading":He,"rf-storelocator-showfootnote":H})},s().createElement("div",{className:"rf-storelocator-searchresult row"},s().createElement("div",{className:i()("rf-storelocator-searchresult-list",{"rf-storelocator-searchresult-empty":!Ne||_e},"column","large-7","small-12")},!Ne||_e?s().createElement(Z,null):s().createElement(s().Fragment,null,s().createElement(G,{stores:ke.data.stores,storeNumber:Se,handleStoreChange:Ce,handleShowStoreDetails:we,assets:qe,legend:Qe}),H&&s().createElement("div",{className:i()("rf-storelocator-footnote",d.A.BODY_REDUCED)},s().createElement("span",(0,a.A)({className:"rf-storelocator-footnote-more more"},(0,h.OH)(Y)))))),s().createElement("div",{className:"rf-storelocator-storeinfo column large-5 small-12"},!Ne&&s().createElement("div",(0,a.A)({className:i()("rf-storelocator-personalpickup",d.A.BODY_REDUCED)},(0,h.OH)(M))),_e&&s().createElement("div",{id:U,className:"rf-storelocator-errorplaceholder"},_e.message),Ke&&(Fe?s().createElement(u.A,{in:De,timeout:1e3,classNames:"rf-storelocator-storeinfoslide",mountOnEnter:!0},s().createElement("div",{className:"rf-storelocator-storeinfoslide"},s().createElement(X,{storeItem:Ke,handleShowStoreDetails:we,handleSaveSelection:xe,assets:qe}))):s().createElement(X,{storeItem:Ke,handleSaveSelection:xe,coldStartMessage:$,assets:qe})))),Fe&&Ke&&!_e&&s().createElement(s().Fragment,null,H&&s().createElement("div",{className:i()("rf-storelocator-footnote",d.A.BODY_REDUCED)},s().createElement("span",(0,a.A)({className:"rf-storelocator-footnote-more more"},(0,h.OH)(Y))),s().createElement("button",{className:i()("rf-storelocator-footnoteclose",d.A.BODY_REDUCED),type:"button",onClick:()=>R(!1)})),s().createElement("div",{className:"rf-storelocator-selectaction rf-storelocator-selectstore"},s().createElement("button",{type:"button",className:"button  button-block","data-autom":"selectStoreButton",onClick:()=>xe(),disabled:!We},s().createElement("span",{className:"select-store"},re),s().createElement("span",{className:"visuallyhidden"},`${re} ${je}`)))))))},te=e=>{let{bootstrap:t,...r}=e;return t&&t.url?s().createElement(ee,(0,a.A)({bootstrap:t},r)):null};te.displayName="StoreLocatorOverlay";const re=te,ae=e=>{let{bootstrap:t,fulfillment:r,...o}=e;return s().createElement(re,(0,a.A)({bootstrap:t,visible:r.overlayApuVisible,part:r.overlayPart,productTitle:r.productTitle,storeId:r.storeId||r.geoStoreId,consent:r.consent,onClose:r.handleCloseApuOverlay,onStoreChange:r.handleStoreSelection,stickyTrigger:r.stickyTrigger},o))}}}]);