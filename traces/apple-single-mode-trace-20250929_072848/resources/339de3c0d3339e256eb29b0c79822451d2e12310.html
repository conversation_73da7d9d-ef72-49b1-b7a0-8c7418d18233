





































 






<!DOCTYPE html>

    
    
        <html dir="ltr" data-rtl="false" lang="zh">
    

<head>
    <title></title>
    <meta name="robots" content="noindex">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<script type="application/json" id="app_config">
    {
        "i18n": 
    
    
    {"web.auth.oauth2.error.photo.more.detail.apple.support.link.text":"Apple 支持。","web.auth.oauth2.open.in.new.window.sr.only":"在新窗口中打开。","web.auth.external.profile.error.required.2.factor.title":"需要双重认证","web.auth.external.profile.error.u13.title":"不支持通过 Apple 登录","web.auth.oauth2.error.no.apple.card.title":"未找到 Apple Card","web.auth.oauth2.error.photo.no.access.title":"开启 iCloud 照片","web.auth.oauth2.error.photo.no.access.description":"若要导入照片和视频，请先开启 iCloud 照片再继续。","web.auth.oauth2.error.photo.age.limit.title":"数据导入不可用","web.auth.oauth2.error.not.allow.country.title":"不可用","web.auth.oauth2.error.not.allow.country.description":"你所在的国家\/地区不支持此服务。请尝试使用其他 Apple 账户。","web.auth.oauth2.error.photo.invalid.country.for.legacy.users.title":"不可用","web.auth.oauth2.error.photo.can.not.imported.title":"目前无法导入你的数据","web.auth.oauth2.error.photo.more.detail.email.to.support.link.text":"<EMAIL>。","web.auth.oauth2.error.photo.more.detail.email.to.support.descrpition":"有关更多详细信息，请联系 {0:emailSupportLink}","web.auth.oauth2.error.photo.more.detail.descrpition":"有关更多详细信息，请联系 {0:appleSupportLink}","web.auth.oauth2.error.request.cannot.be.completed.title":"无法完成请求","web.auth.oauth2.error.generic.error.description":"你的 Apple 账户无法登录。","web.auth.oauth2.error.turn.off.access.description":"因为你关闭了“在网页上访问 iCloud 数据”，所以无法完成你的请求。","web.auth.oauth2.error.data.protection.turn.on.description":"因为你开启了高级数据保护，所以无法完成你的请求。","web.auth.oauth2.error.concurrent.requests":"该 Apple 账户已有正在处理的请求。","web.tryAgainLater.text":"请稍后再试。","web.auth.oauth2.error.require.two.factor":"若要将数据传输到 Apple，你需要为 Apple 账户设置双重认证。","web.auth.oauth2.error.music.max.playlists.reached.description":"播放列表数量已达到上限。若要导入更多，你需要先从 Apple Music 中移除部分播放列表。","web.auth.oauth2.error.music.max.no.subscription.description":"若要导入播放列表，请订阅 Apple Music。如果你已订阅，请登录 Apple Music 账户。","web.auth.oauth2.error.photo.request.to.tranfers.data.title":"请求转移数据","web.auth.oauth2.error.no.icloud.mail.title":"无 iCloud 邮件地址","web.auth.oauth2.error.no.icloud.mail":"没有找到与此账户相关联的 iCloud 邮件地址。你可以在 Mac、iPhone 或 icloud.com\/mail 网页上创建新的 iCloud 邮件地址。","web.footer.verifyCode.popover.label.sr.only":"其他选项","web.trustMyBrowser.sa.info":"如果你选择信任此浏览器，当你下次登录时，系统将不会要求你提供安全提示问题。","web.trustMyBrowser.2fa.info":"如果你选择信任此浏览器，当你下次登录时，系统将不会要求你提供验证码。","web.trustMyBrowser.notNow":"以后再说","web.error.fallback":"无法验证你的身份。重试。","web.placeholder.appleid":"Apple 账户","web.placeholder.account":"账户","web.error.native.takeover.error.title":"尝试验证凭据时出错","web.error.unknown.title":"发生意外错误","web.error.account.locked.webAuthnNotSupported.title":"无法验证身份","web.error.account.locked.noDevicesRKNotAllowed.title":"暂时无法登录","web.error.account.locked.selfServiceReactivation":"此账户已锁定","web.error.account.locked.requestAccessButton":"请求访问","web.aria.repair.iframeTitle":"Apple 账户修复","web.aria.repair.loading":"正在载入…","web.auth.oauth2.error.no.apple.card.description.learnmore.url":"https:\/\/www.apple.com\/apple-card\/","web.auth.oauth2.error.no.apple.card.description.learnmore.link.text":"了解如何申请","web.auth.oauth2.error.no.apple.card.description.support.url":"https:\/\/support.apple.com\/apple-card","web.auth.oauth2.error.no.apple.card.description.support.link.text":"Apple Card 支持","web.auth.oauth2.error.no.apple.card.description2":"{0:learnMoreLink} {1:supportLink}","web.signin.desription.security.key.login.successful.enter.credentials":"安全密钥登录成功。请输入凭证以继续。","webAuthn.browser.doesnot.support.security.keys.enter.password.error.message":"此浏览器不支持 FIDO2 安全密钥。请输入密码以继续。","webAuthn.authentication.method.not.supported.browser.error.message":"浏览器不支持此验证方法。","web.signInLabelWithAppName":"请登录{0}","web.footer.siginin.link.createAppleID":"立即创建你的 \u003Cspan class=\"no-wrap sk-icon sk-icon-after sk-icon-external\"\u003EApple 账户\u003C\/span\u003E","widget.signin.v2.footer.link.createAppleAccount":"创建你的 Apple 账户","web.text.forgotPassword":"忘记密码？","web.federated.intermediaryIntroPage.title":"{0} 的 Apple 账户","web.federated.intermediaryIntroPage.text":"继续前往 {0} 以登录你的 {1} 账户","web.auth.external.profile.pageTitle.appleaccount":"登录 Apple 账户","web.text.sendingCode":"正在发送验证码…","web.suppressIforgot.forgotPassword.text":"请联系你所在组织的管理员或经理，为你的管理式 Apple 账户重设密码。","web.challenge.too.many.verification.attempts.title":"验证错误的次数过多","web.challenge.too.many.verification.attempts.body":"你尝试回答安全提示问题的次数过多。你可以稍后再试。","web.text.cancel":"取消","web.challenge.reset.button":"重设安全提示问题","web.challenge.canNotGenerateResetQuestionURL.text":"发生错误，无法完成你的请求。","widget.signin.not.completed.try.again":"无法完成登录。请重试。","webAuthn.browser.doesnot.support.security.keys.error.message":"此浏览器不支持 FIDO2 安全密钥。","widget.signin.with.privacy.text":"您的 Apple 账户信息用于让您安全登录和访问您的数据。Apple 会出于安全、支持和报告目的记录某些数据。如果您同意，Apple 还可能使用您的 Apple 账户信息向您发送营销电子邮件和通信，包括基于您对 Apple 服务的使用情况。","widget.signin.with.privacy.text.link":"了解数据的管理方式…","web.button.text.continue":"继续","web.text.forgotPassword.link":"忘记了\u003Cspan class=\"no-wrap sk-icon sk-icon-after sk-icon-external\"\u003E密码？\u003C\/span\u003E","web.text.verifying":"正在验证…","widget.signin.v2.signin.button.text":"登录","widget.swp.continue.with.phone.button":"通过通行密钥登录","widget.swp.continue.with.phone.button.ios.description":"需要 iOS 17、macOS Sonoma 或更高版本。","widget.swp.continue.with.phone.button.macos.description":"需要 macOS Sonoma、iOS 17 或更高版本。","widget.swp.continue.with.phone.button.description":"需要安装 iOS 17 或更高版本的设备。","web.default.signInLabel":"登录你的 Apple 账户","web.text.useAppleConnect":"使用 AppleConnect \u003Ca target=\"_parent\" id=\"appleConnectUrl\" class=\"si-link ax-outline thin \" href=\"{0}\"\u003E继续登录\u003C\/a\u003E。","web.aria.signIn":"登录","web.aria.close":"关闭","web.sign.in.with.security.key":"使用安全密钥登录…","web.footer.copyright":"Copyright &#169; {0} Apple Inc. 保留所有权利。","web.footer.termsOfUse":"使用条款","web.footer.privacyPolicy":"隐私政策","web.footer.appleConnectAvailable":"使用你的 AppleConnect 账户登录","web.footer.siginin.dontHaveAppleID":"没有 Apple 账户？","web.footer.siginin.link.forgotAccount":"忘记了账户或\u003Cspan class=\"no-wrap sk-icon sk-icon-after sk-icon-external\"\u003E密码？\u003C\/span\u003E","web.footer.siginin.link.forgotPassword":"忘记了\u003Cspan class=\"no-wrap sk-icon sk-icon-after sk-icon-external\"\u003E密码？\u003C\/span\u003E","web.footer.siginin.link.forgotAppleID":"忘记\u003Cspan class=\"no-wrap sk-icon sk-icon-after sk-icon-external\"\u003E密码？\u003C\/span\u003E","web.button.text.back":"返回","web.text.ok":"好","web.auth.external.profile.error.required.2.factor.learn.more.button":"了解更多","web.auth.oauth2.error.photo.request.to.tranfers.data.continue.button.text":"继续…","web.button.text.cancel":"取消","web.auth.external.profile.button.done":"完成","web.auth.oauth2.error.no.apple.card.description":"你输入的 Apple 账户未与 Apple Card 相关联。如果你想登录现有的 Apple Card 账户，请尝试使用其他 Apple 账户。要申请 Apple Card，请在 iPhone 上使用“钱包” App。","web.auth.external.profile.error.required.2.factor.description":"“通过 Apple 登录”需要双重认证。你可以在 iOS 设备或 Mac 上为你的 Apple 账户开启双重认证。","web.auth.external.profile.error.required.2.factor.description.ios.title":"请参阅 iOS 设备说明","web.auth.external.profile.error.required.2.factor.description.ios.instruction.v10.3.title":"如果你使用的是 iOS 10.3 或更高版本：","web.auth.external.profile.error.required.2.factor.description.ios.instruction.v10.3.text1":"前往“设置”\u003E [你的姓名] \u003E“密码与安全性”。","web.auth.external.profile.error.required.2.factor.description.ios.instruction.v10.3.text2":"轻点“开启双重认证”。","web.auth.external.profile.error.required.2.factor.description.ios.instruction.v10.3.text3":"轻点“继续”。","web.auth.external.profile.error.required.2.factor.description.mac.title":"请参阅 Mac 说明","web.auth.external.profile.error.required.2.factor.description.mac.instruction.capitan.title":"在运行 macOS Catalina 或更高版本的 Mac 上按照以下步骤操作：","web.auth.external.profile.error.required.2.factor.description.mac.instruction.capitan.text1":"前往 Apple ( \u003Ci aria-hidden=\"true\" style=\"font-size: 12px;\" class=\"icon icon_apple\"\u003E\u003C\/i\u003E ) 菜单\u003E“系统偏好设置”或“系统设置”\u003E Apple 账户\u003E“概览”。","web.auth.external.profile.error.required.2.factor.description.mac.instruction.capitan.text3":"点按“开启双重认证”。","signing.in.with.appleconnect.in.progress":"正在使用 AppleConnect 登录…","signing.in.with.appleconnect":"使用 AppleConnect 登录","signing.in.with.appleconnect.attempt.canceled":"你的登录尝试已取消。","signing.in.with.appleconnect.signin.button":"登录","signing.in.with.appleconnect.approved.device.not.found":"未找到获批准设备。","signing.in.with.appleconnect.two.factor.requirements":"你的账户必须使用双重认证。请确认你已安装 AppleConnect，已启用 AppleConnect Safari\/Chrome 扩展，并且正在使用的是获批准设备。","signing.in.with.appleconnect.retry":"重试","signing.in.with.appleconnect.acmobile.instructions.not.configured.heading":"尚未配置 AppleConnect","signing.in.with.appleconnect.acmobile.instructions.not.configured.subheading":"请确保你已在 Self Service 中注册，并已安装 Connect App。如果未找到 Connect，请打开 Self Service App 并手动安装 Connect。","signing.in.with.appleconnect.acmobile.instructions.enrollInSelfService.button":"在 Self Service 中注册…","web.error.unknown.description":"发生错误，无法完成你的请求。请稍后再试。","web.error.native.takeover.error.default.description":"若要解决此问题，请使用密码登录。","web.error.account.locked.resetInfo":"你必须解锁账户，才能登录。","web.error.account.locked.selfServiceReactivationInfo":"确认你的信息以请求访问此账户。","web.error.accountLocked":"你尝试验证身份的次数过多。你必须解锁账户才能继续。","web.error.account.locked.noDevicesNoRK.text":"因为你的账户没有受信任设备，所以你无法完成登录。请访问 Apple 账户网站，并使用你的恢复密钥添加受信任设备。","webAuthn.authentication.notSupported":"该浏览器不支持安全密钥，因此你无法完成登录流程。","web.error.account.locked.noDevicesRKNotAllowed.text":"尝试登录账户的失败次数过多。为保护账户安全，8 小时内无法登录。"}
  
    }
</script>
    
        
            



    
    
    
    
    
    
    
        
    

<link rel="stylesheet"  href="https://www.apple.com/wss/fonts?families=SF+Pro,v3|SF+Pro+Icons,v3|SF+Pro+SC,v1" type="text/css"/>


        
    
    
  
  
    
      
      
        
          
          
          
            
              
<link rel="stylesheet" href="https://appleid.cdn-apple.com/appleauth/static/module-assets/home-4f1095960e3cb44d84d9.css"/>
            
            <link rel="stylesheet" type="text/css" media="screen" href="https://appleid.cdn-apple.com/appleauth/static/cssj/********/widget/auth/app-sk7.css" />

          
        
      
    
  

    
        
        
            <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsj/**********/common-header.js" ></script>

        
    

    
</head>

<body class="tk-body ">
<div aria-hidden="true" style='font-family:"SF Pro Icons"; width: 0px; height: 0px; color: transparent;'>.</div>
<div aria-hidden="true" style='font-family:"SF Pro Display"; width: 0px; height: 0px; color: transparent;'>.</div>
<div class="si-body si-container container-fluid" id="content" role="main" aria-labelledby="contentheader">
</div>


    
    
    
    
    

    
  <script type="text/stache" id="jstache_570314101">
    
          
          
          
          
          
          <div class="graphite-icon {{symbolName}} {{symbolType}}{{#if wrapperClasses}} {{wrapperClasses}}{{/if}}" aria-live="assertive">
    {{#is symbolName "arrow-left-circle"}}
      <svg id="arrowLeftCircle" data-name="arrow-left-circle" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 110 110"><g id="Semibold-M"><path d="M55,106.05A51.33,51.33,0,0,0,106,55,51.32,51.32,0,0,0,55,4a51.05,51.05,0,0,0,0,102.1Zm0-11.47A39.58,39.58,0,1,1,94.6,55,39.45,39.45,0,0,1,55,94.58ZM29.71,55a5.3,5.3,0,0,0,1.8,4L48.16,75.14a4.32,4.32,0,0,0,3.28,1.32,4.19,4.19,0,0,0,4.24-4.39,4.36,4.36,0,0,0-1.41-3.28l-5.76-5.37-5.18-4,9.77.49H75.7a4.94,4.94,0,0,0,0-9.87H53.1l-9.77.49,5.22-4,5.72-5.37a4.53,4.53,0,0,0,1.41-3.27,4.15,4.15,0,0,0-4.24-4.35,4.32,4.32,0,0,0-3.28,1.32L31.51,51A5.25,5.25,0,0,0,29.71,55Z"/></g></svg>
    {{/is}}

    {{#is symbolName "exclamationmark-triangle-fill"}}
      <svg id="exclamationMark" data-name="exclamationmark-triangle-fill" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 110 110"><g id="Semibold-M"><path d="M16.74,102.19H93.26A12.55,12.55,0,0,0,106.15,89.3,13.1,13.1,0,0,0,104.44,83L66.11,14.4A12.46,12.46,0,0,0,55,7.81,12.61,12.61,0,0,0,43.89,14.4L5.56,83a12.89,12.89,0,0,0-1.71,6.3A12.55,12.55,0,0,0,16.74,102.19ZM55.07,67.91c-3.17,0-5-1.75-5.12-4.93l-.74-22.7c-.14-3.37,2.25-5.67,5.81-5.67s6,2.35,5.81,5.72l-.78,22.6C60,66.21,58.15,67.91,55.07,67.91Zm0,17.24c-3.61,0-6.49-2.39-6.49-5.86s2.88-5.91,6.49-5.91,6.45,2.45,6.45,5.91S58.64,85.15,55.07,85.15Z"/></g></svg>
    {{/is}}

    {{#is symbolName "checkmark"}}
      <svg id="checkmark" data-name="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 110 110"><g id="Bold-M"><path d="M45.09,98.63a9.05,9.05,0,0,0,8-4L98.51,25.29a10.28,10.28,0,0,0,1.85-5.67,8.15,8.15,0,0,0-8.49-8.25c-3.33,0-5.42,1.22-7.48,4.4l-39.5,62.3-20-24.56a8,8,0,0,0-6.74-3.22,8.13,8.13,0,0,0-8.49,8.3,8.6,8.6,0,0,0,2.49,6.1L37.32,95A9.52,9.52,0,0,0,45.09,98.63Z"/></g></svg>
    {{/is}}

    {{#is symbolName "exclamationmark-circle-fill"}}
      <svg id="exclamationmark-circle-fill" data-name="exclamationmark-circle-fill" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 110 110"><g id="Semibold-M"><path d="M55,106.05A51.33,51.33,0,0,0,106,55,51.32,51.32,0,0,0,55,4a51.05,51.05,0,0,0,0,102.1Zm0-43.26c-3.17,0-5-1.76-5.13-4.93l-.73-22.71C49,31.83,51.39,29.49,55,29.49s6,2.34,5.86,5.71L60,57.81C59.88,61.08,58.08,62.79,55,62.79ZM55,80c-3.61,0-6.49-2.39-6.49-5.86s2.88-5.9,6.49-5.9,6.45,2.44,6.45,5.9S58.56,80,55,80Z"/></g></svg>
    {{/is}}

    {{#is symbolName "arrow-left-circle-fill"}}
      <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 112 112"><g id="Regular-M" transform="matrix(1 0 0 1 1391.3 1126)"><path d="M-1335.30029-1020.19531c27.24609,0,49.80518-22.55859,49.80518-49.80469
        c0-27.19727-22.60779-49.80469-49.854-49.80469c-27.19727,0-49.75586,22.60742-49.75586,49.80469
        C-1385.10498-1042.75391-1362.49744-1020.19531-1335.30029-1020.19531z M-1363.76709-1070.04883
        c0-1.17188,0.39075-2.14844,1.5625-3.27148l18.70117-18.70117c0.73242-0.73242,1.70911-1.12305,2.88098-1.12305
        c2.29492,0,4.10144,1.75781,4.10144,4.05273c0,1.17188-0.58582,2.24609-1.26941,2.92969l-6.88489,6.83594l-6.5918,5.56641
        l11.71887-0.48828h28.61328c2.44141,0,4.15027,1.75781,4.15027,4.19922s-1.66016,4.19922-4.15027,4.19922h-28.61328
        l-11.7677-0.43945l6.64062,5.61523l6.88489,6.73828c0.7323,0.73242,1.26941,1.75781,1.26941,2.97852
        c0,2.29492-1.80652,4.05273-4.10144,4.05273c-1.17188,0-2.14856-0.48828-2.88098-1.17188l-18.70117-18.65234
        C-1363.27881-1067.80273-1363.76709-1068.7793-1363.76709-1070.04883z"/></g></svg>
    {{/is}}

    {{#is symbolName "badge-circle-fill"}}
      <svg id="badge_circle_filled" data-name="badge_circle_filled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
        <path d="M29.929896,59.9213121 C46.2189361,59.9213121 59.8517361,46.2884801 59.8517361,29.99944 C59.8517361,13.67128 46.2189361,0.0776000001 29.890856,0.0776000001 C13.562696,0.0776000001 0.00800000001,13.67128 0.00800000001,29.99944 C0.00800000001,46.2884801 13.601736,59.9213121 29.929896,59.9213121 Z" transform="translate(2 2)"/>
      </svg>
    {{/is}}
    {{#is symbolName "plus"}}
      <svg id="plus" data-name="plus" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
        <path d="M0.0955555557,29.9994075 C0.0955555557,31.7717038 1.57844445,33.2184445 3.3145926,33.2184445 L26.715926,33.2184445 L26.715926,56.6197734 C26.715926,58.3558816 28.1626667,59.8388149 29.934963,59.8388149 C31.7072593,59.8388149 33.1901482,58.3558816 33.1901482,56.6197734 L33.1901482,33.2184445 L56.5553334,33.2184445 C58.2914075,33.2184445 59.7743705,31.7717038 59.7743705,29.9994075 C59.7743705,28.2271112 58.2914075,26.7442223 56.5553334,26.7442223 L33.1901482,26.7442223 L33.1901482,3.37903704 C33.1901482,1.64288889 31.7072593,0.16 29.934963,0.16 C28.1626667,0.16 26.715926,1.64288889 26.715926,3.37903704 L26.715926,26.7442223 L3.3145926,26.7442223 C1.57844445,26.7442223 0.0955555557,28.2271112 0.0955555557,29.9994075 Z" transform="translate(2 2)"/>
      </svg>
    {{/is}}
    {{#is symbolName "minus"}}
      <svg id="minus" data-name="minus" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 64 64">
        <path d="M3.3145926,6.62392594 L56.5553334,6.62392594 C58.2914075,6.62392594 59.7743705,5.14096297 59.7743705,3.36866667 C59.7743705,1.59637037 58.2914075,0.14962963 56.5553334,0.14962963 L3.3145926,0.14962963 C1.57844445,0.14962963 0.0955555557,1.59637037 0.0955555557,3.36866667 C0.0955555557,5.14096297 1.57844445,6.62392594 3.3145926,6.62392594 Z" transform="translate(2 28.667)"/>
      </svg>
    {{/is}}
    {{#is symbolName "help"}}
      <svg id="help" data-name="help" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 64 64">
        <path d="M15.9854681,42.4905958 C18.1671702,42.4905958 19.1645107,40.9945533 19.1645107,38.9998724 L19.1645107,37.9401703 C19.2268085,33.826149 20.6916383,32.1120001 25.6783405,28.6836383 C31.0390852,25.0682554 34.4362341,20.8919362 34.4362341,14.8766809 C34.4362341,5.52663831 26.8315532,0.165957447 17.3567873,0.165957447 C10.3131064,0.165957447 4.14204256,3.50080852 1.49285107,9.51600002 C0.838340427,10.9808298 0.557872341,12.4145107 0.557872341,13.5988724 C0.557872341,15.375383 1.5863617,16.6220426 3.48753192,16.6220426 C5.07702129,16.6220426 6.13672341,15.6870639 6.60421278,14.1598724 C8.19376597,8.23812767 12.120766,5.99412767 17.138617,5.99412767 C23.2161703,5.99412767 27.9847022,9.42248938 27.9847022,14.8455319 C27.9847022,19.302383 25.2108511,21.795766 21.2214894,24.600766 C16.3282979,27.9979788 12.7441277,31.6445107 12.7441277,37.1298511 L12.7441277,39.093383 C12.7441277,41.0880639 13.8349787,42.4905958 15.9854681,42.4905958 Z M15.9854681,59.5388299 C18.4787873,59.5388299 20.4423192,57.5441746 20.4423192,55.1131533 C20.4423192,52.6509716 18.4787873,50.6874575 15.9854681,50.6874575 C13.5544468,50.6874575 11.559766,52.6509716 11.559766,55.1131533 C11.559766,57.5441746 13.5544468,59.5388299 15.9854681,59.5388299 Z" transform="translate(14.766 2)"/>
      </svg>
    {{/is}}
    {{#is symbolName "lock-fill"}}
      <svg id="lock_fill" data-name="lock_fill" class="icon" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g transform="matrix(1 0 0 1 80.70993408203117 114.2724609375)">
          <path d="M30.8971 11.3277L75.5987 11.3277C82.7683 11.3277 86.5689 7.44371 86.5689-0.285611L86.5689-33.8799C86.5689-41.568 82.7683-45.452 75.5987-45.452L30.8971-45.452C23.7381-45.452 19.9375-41.568 19.9375-33.8799L19.9375-0.285611C19.9375 7.44371 23.7381 11.3277 30.8971 11.3277ZM28.5164-41.7016L36.2358-41.7016L36.2358-58.1657C36.2358-70.506 44.1294-77.0264 53.2273-77.0264C62.3559-77.0264 70.3118-70.506 70.3118-58.1657L70.3118-41.7016L78.0311-41.7016L78.0311-57.1622C78.0311-75.51 66.0249-84.3623 53.2273-84.3623C40.4709-84.3623 28.5164-75.51 28.5164-57.1622Z" />
        </g>
      </svg>
    {{/is}}
</div>

        
  </script>
  
  <script type="text/stache" id="jstache_1381419984">
    
          
          
          
          
          
          


{{#if showSwpOption}}
  {{#if swpOptionAsDefault}}
    {{> swpContainerTemplateID}}
 {{else}}
    <div class="form-row swp-account-name">
         <label class="sr-only form-cell form-label" for="account_name_text_field">{{locMessage 'web.default.signInLabel'}}</label>
        <div class="form-cell">
          <div class='swp-form-cell-wrapper'>
               {{> swpContainerTemplateID}}
          </div>
        </div>
      </div>
 {{/if}}
{{/if}}

        
  </script>
  
  <script type="text/stache" id="jstache_649468495">
    
          
          
          
          
          
          
<div style='{{#if swpOptionAsDefault}}{{#if useSasskit7}}height:114px;{{else}}height:90px;{{/if}}{{/if}}' class="{{#if errorMessage}}is-error{{/if}} fade-in fade-out"> 
  <div class='swp-option' style=''>
    <div style='position:relative;flex-grow: 1;'>
      <div class="loading-wrapper" style="position: absolute;top: 50%;left: -20px;">
        {{#if showSwpLoadingSpinner}}
          <idms-spinner anchor-element=".spin-anchor" show="{showSwpLoadingSpinner}"></idms-spinner>
          <div class="spin-anchor"></div>
        {{/if}}
      </div>
      <button {{#if showSwpLoadingSpinner}} disabled {{/if}} id="continue-password" style="display:inline; width:100%;box-sizing: border-box;"  ($click)="usePassword()" class="{{#if useSasskit7}}button-secondary-alpha{{else}}button-secondary{{/if}} button-block tk-subbody button button-rounded-rectangle">
        继续使用密码登录
      </button>  
    </div> 
    <div style='text-align:center;flex-grow: 1;'> 
      <button  {{#if showSwpLoadingSpinner}} disabled {{/if}} style="display: flex;align-items: center;column-gap: 5px;width: 100%;box-sizing: border-box;justify-content: center;" id="swp" ($click)="signInWithKey()" class="{{#if useSasskit7}}button-secondary-alpha{{else}}button-secondary{{/if}} button-block button-rounded-rectangle tk-subbody button">
        <i aria-hidden="true" class="shared-icon no-flip iphone-icon"></i> 通过通行密钥登录
      </button> 
      <div class="tk-caption" style='margin-top: 5px;'>
     {{#switch osPlatform}}
      {{#case "ios"}}
        需要 iOS 17、macOS Sonoma 或更高版本。
      {{/case}}
      {{#case "mac os"}}
        {{#if isSafari }}
          需要 macOS Sonoma、iOS 17 或更高版本。
        {{else}}
          需要安装 iOS 17 或更高版本的设备。
        {{/if}}
      {{/case}}
      {{#default}}
        需要安装 iOS 17 或更高版本的设备。
      {{/default}}
     {{/switch}}
     </div>
      
      
    </div>
  </div>
  
    <div style="height:22px; text-align:center;position:relative;">
    
    {{#if errorMessage}}
    <div style="position:absolute;top:3px;text-align: center;width: 100%;">
      <idms-error
        {error-message}="errorMessage"
        error-type="standards"
                    
      ></idms-error>  
      {{/if}}
    </div>
    <div>

  
</div>

        
  </script>
  
  
  








    
    
        <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsj/1913796087/boot/initBootData.js" ></script>

    




<script type="application/json" class="boot_args">
    {"direct":{"destinationDomain":"https://secure8.www.apple.com.cn","appleOAuth":{"requestor":{"redirectURI":"https://secure8.www.apple.com.cn","responseMode":"web_message","responseType":"code","frameId":"auth-ysan8zob-gmkx-394y-krkc-q8bdzd9k","id":"a797929d224abb1cc663bb187bbcd02f7172ca3a84df470380522a7c6092118b","state":"auth-ysan8zob-gmkx-394y-krkc-q8bdzd9k","type":"firstPartyAuth"}},"urlContext":"/appleauth","enableAuthPopup":false},"additional":{"skVersion":"7"}}
</script>



    
    
        <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsj/**********/boot/initLocalizationStrings.js" ></script>

    



<script type="application/json" class="localization_strings">
    [{"key":"signInFallbackError","value":"请检查输入的账户信息并重试。","shouldEscape":true},{"key":"fallback","value":"无法验证你的身份。重试。","shouldEscape":true},{"key":"exampleEmail","value":"<EMAIL>","shouldEscape":true},{"key":"appleId","value":"电子邮件或电话号码","shouldEscape":true},{"key":"account","value":"账户","shouldEscape":true},{"key":"password","value":"密码","shouldEscape":true},{"key":"rememberMe","value":"记住我的账户","shouldEscape":true},{"key":"keepMeSignedIn","value":"保持我的登录状态","shouldEscape":true},{"key":"defaultSignInLabel","value":"登录你的 Apple 账户","shouldEscape":true},{"key":"cancelText","value":"取消","shouldEscape":true},{"key":"resetAccText","value":"解锁账户","shouldEscape":true},{"key":"gotoAppleIdText","value":"前往 Apple 账户","shouldEscape":true},{"key":"addTrustedDeviceText","value":"添加受信任设备","shouldEscape":true},{"key":"iframeTitle","value":"登录你的 Apple 账户","shouldEscape":true},{"key":"acccountLockedTitle","value":"出于安全原因，此 Apple 账户已锁定。","shouldEscape":true},{"key":"noDevicesNoRKDialogTitle","value":"无法验证身份","shouldEscape":true},{"key":"suppressIforgotTitle","value":"需要重设密码？","shouldEscape":true},{"key":"resetCRTokenTitle","value":"此链接已失效。","shouldEscape":true},{"key":"logoAltText","value":"应用程序徽标","shouldEscape":true},{"key":"saChallenge.button.resetSecurityQuestions.text","value":"重设安全提示问题","shouldEscape":true},{"key":"titleCouldNotGenerateResetQuestionURL","value":"无法重设安全提示问题","shouldEscape":true},{"key":"okText","value":"好","shouldEscape":true},{"key":"accountUnlockButton","value":"解锁账户","shouldEscape":true},{"key":"btnTextGoBack","value":"返回","shouldEscape":true},{"key":"btnTextContinue","value":"继续","shouldEscape":true},{"key":"notNowTrustMyBrowser","value":"以后再说","shouldEscape":true},{"key":"intermediaryIntroPageTitle","value":"{0} 的 Apple 账户","shouldEscape":true},{"key":"intermediaryIntroPageDefaultTitle","value":"你所在组织的 Apple 账户","shouldEscape":true},{"key":"profileIntroTitle2","value":"获取正确权限","shouldEscape":true},{"key":"profileIntroDescription2","value":"让 App 轻松提供访问控制，使整个组织更安全。","shouldEscape":true},{"key":"profilePrivacyText","value":"系统将共享此屏幕中的信息，以便“{0:appName}”识别你在组织中的身份，并为你分配正确的访问级别。","shouldEscape":true}]
</script>















    
    
        
    



        
    
    


    <script type="application/json" id="app_config_sa_sk7">
        {
            "i18n": 
        
        
        {"web.error.rk.rkLocked.body":"你输入错误恢复密钥的次数过多，请稍后再试。","web.error.tooManyCodesSentToDevice":"发送验证码的次数过多。请输入你最后收到的验证码、使用你的设备，或稍后再试。","web.error.unknown.description":"发生错误，无法完成你的请求。请稍后再试。","web.error.securityCodeLocked":"你输入错误验证码的次数过多。请稍后再试。","web.error.fallback":"无法验证你的身份。重试。","error_21669":"验证码不正确","web.error.unknown.title":"发生意外错误","web.challenge.canNotGenerateResetQuestionURL.title":"无法重设安全提示问题","web.challenge.canNotGenerateResetQuestionURL.text":"发生错误，无法完成你的请求。","web.tryAgainLater.text":"请稍后再试。","web.text.ok":"好","web.title.answerSecurityQuestion":"回答你的安全提示问题以继续","web.challenge.answer.placeholder.answer.label.with.number":"答案 {0:digit}","web.challenge.supportPin.button":"获取支持 PIN","web.text.verifying":"正在验证…","web.button.text.goBack":"返回","web.button.text.continue":"继续","web.challenge.reset.button":"重设安全提示问题","web.challenge.reset.text":"忘记答案？","web.challenge.too.many.verification.attempts.title":"验证错误的次数过多","web.challenge.too.many.verification.attempts.resetQuestions.body":"你尝试回答安全提示问题的次数过多。你可以稍后再试或重设你的安全提示问题。","web.text.cancel":"取消","web.text.sendingCode":"正在发送验证码…","web.title.email.verifyCode":"输入验证码","web.aria.digit":"位数","web.verifyCode.first.digit.sr":"请输入验证码。在输入字段中输入代码后，焦点会自动移动到下一个输入字段上。输入验证码后，页面会自动更新。","web.aria.description.security.code":"在每一个输入字段中输入验证码后，光标会自动移动到下一个输入字段","web.footer.restricted.verifyEmail.codeHasBeenSent":"一封包含验证码的电子邮件已发送至 \u003Cspan class=\"displayName\"\u003E{0}\u003C\/span\u003E。输入验证码以继续。","web.hsa2.verify.tryAgain.sms":"重新发送验证码","web.restricted.verifyEmail.tryAgain.subtext":"查看你的垃圾邮件文件夹或等待几分钟，然后请求发送新验证码。","web.restricted.verifyEmail.help.other":"更多信息","web.restricted.verifyEmail.needHelp.subtext":"无法使用此电子邮件地址？","web.restricted.verifyEmail.more.help.other":"无法使用 \u003Cspan class=\"displayName\"\u003E{0}\u003C\/span\u003E?","web.restricted.verifyEmail.more.needHelp.subtext":"出于安全考虑，你必须能够访问此电子邮件地址才可登录。","web.footer.verifyCode.link.didntReceiveCode":"没有收到验证码？","web.footer.verifyCode.popover.label.sr.only":"其他选项","web.challenge.canNotGeneratePin.title":"发生错误，无法完成你的请求。","web.challenge.canNotGenerateSupportPIN.text":"发生错误，无法完成你的请求。","web.challenge.generatePin.title":"临时支持 PIN","web.challenge.generatePin.bodyText":"当你通过电话或在线联系 Apple 支持时，我们可能会要求你提供一个四位数的 PIN 用于验证你的身份。","web.auth.signout.loading.text":"正在载入…","web.challenge.generatePin.button":"生成 PIN","web.challenge.supportPinGenerated.text":"你的临时支持 PIN：{0}","web.challenge.supportPinValid.text":"此 PIN 的有效期至：{0}","web.challenge.generateNewPin.button":"生成新 PIN","web.challenge.done.button":"完成","web.title.trustMyBrowser":"信任此浏览器吗？","web.trustMyBrowser.2fa.info":"如果你选择信任此浏览器，当你下次登录时，系统将不会要求你提供验证码。","web.trustMyBrowser.notNow":"以后再说","web.trustMyBrowser.dontTrust":"不信任","web.trustMyBrowser.trust":"信任","web.hsa1.collectionDevice":"集合设备","web.hsa1.fsa2key":"安全密钥","web.hsa1.individualKey":"个人密钥","web.hsa1.fsa1key":"安全密钥","web.hsa1.signInWithApprovedDevice":"使用已获批准的设备登录","web.hsa1.pushNotification":"推送通知","web.hsa1.sendCode.phoneNumberEndingIn":"电话号码尾号 {0}","web.text.sms":"SMS","web.text.totp":"TOTP","web.aria.deviceInfo":"尾号为 {0} 的号码","web.title.chooseDevice.sendVerificationCode":"验证你的身份","web.footer.chooseDevice.chooseTrustedDeviceForAccount":"你的账户配置了多个多重认证选项。","web.footer.chooseDevice.chooseTrustedDevice":"你的 Apple 账户已受两步验证保护。请选择一部受信任设备接收验证码。","web.footer.chooseDevice.link.cantAccessTrustedDevices":"无法访问你的受信任设备？","web.error.account.locked.cantAccessDevices.text.open.newWindow":"如果你无法访问受信任设备，请\u003Ca href=\"{0}\" class=\"{1} ax-outline\" id=\"{3}\" target=\"{4}\" tabindex=\"0\" aria-describedby=\"{3}\"\u003E访问 Apple 账户网站\u003Cspan class=\"sr-only\"\u003E在新窗口中打开\u003C\/span\u003E\u003C\/a\u003E，并使用恢复密钥添加受信任设备。","web.error.account.locked.cantAccessDevices.text":"如果你无法访问受信任设备，请\u003Ca href=\"{0}\" class=\"{1} ax-outline\" id=\"{3}\" target=\"{4}\" tabindex=\"0\" aria-describedby=\"{3}\"\u003E访问 Apple 账户网站\u003C\/a\u003E，并使用恢复密钥添加受信任设备。","web.footer.verifyCode.button.link.sendNewCode":"发送新验证码","web.footer.verifyCode.link.useDifferentDevice":"使用其他设备","web.title.verifyCode.verifyYourIdentity":"输入验证码","web.footer.verifyCode.hsa1.enterTheCode":"请输入发送至 \u003Cspan class=\"displayName\"\u003E“{0}”\u003C\/span\u003E 的验证码。","web.footer.verifyCode.hsa1.enterTheCodeSentToYourDevices":"输入已发送至你其他设备的验证码。","web.footer.rk.link.lostRK":"遗失了恢复密钥？","web.footer.rk.link.useTrustedDevice":"使用受信任设备","web.title.rk.enterRK":"输入恢复密钥","web.footer.rk.enterRK":"输入启用两步验证时所收到的恢复密钥。","web.info.rk.lostRK":"你的恢复密钥是你在设置两步验证时收到的 14 个字符的代码。如果你丢失了恢复密钥，请阅读\u003Ca href=\"{0}\" target=\"_blank\" class=\"ax-outline\"\u003E此文章\u003C\/a\u003E以获得指导。","web.input.rk.label":"恢复密钥","web.title.verifyCode.2fa":"双重认证","web.footer.verifyCode.hsa2.codeGenerated":"一条包含验证码的信息已发送至你的设备。输入验证码以继续。","widget.v2.hsa2.verify.device.resend.code.link.fallback.text":"重新发送验证码","widget.v2.hsa2.verify.device.cannot.get.code.link.fallback.text":"无法使用你的设备？","widget.v2.hsa2.verify.device.popup.caption.message.rates.may.apply":"可能会产生短信或数据费用。","web.hsa2.verify.tryAgain.sms.subtext":"获取新验证码。","widget.v2.hsa2.verify.tryAgain.sms":"再次通过短信发送验证码","web.hsa2.verify.tryAgain.calling":"正在呼叫…","web.hsa2.verify.tryAgain.call.subtext":"接听语音来电以获取新验证码。","widget.v2.hsa2.verify.tryAgain.call":"再次拨打语音来电","widget.v2.hsa2.verify.callMe":"接听语音来电","widget.v2.hsa2.verify.device.help.popup.get.a.call.text":"接听拨打至 \u003Cspan class=\"phone-number force-ltr\"\u003E{0:phonenumber}\u003C\/span\u003E 的语音来电","widget.v2.hsa2.verify.sendText":"通过短信发送验证码","widget.v2.hsa2.verify.device.help.popup.text.code.to.phone.text":"通过短信将验证码发送至 \u003Cspan class=\"phone-number force-ltr\"\u003E{0:phonenumber}\u003C\/span\u003E","web.hsa2.verify.usePhone":"使用电话号码","web.hsa2.verify.usePhone.subtext":"通过短信或接听语音来电获得验证码。","web.hsa2.verify.device.help.deviceNoAccess.other":"更多选项…","web.auth.oauth2.open.in.new.window.sr.only":"在新窗口中打开。","web.hsa2.verify.needHelp.subtext":"请确认你的电话号码，以获得支持。","web.restricted.verifyPhone.more.help.other":"无法使用 \u003Cspan class=\"displayName\"\u003E{0}\u003C\/span\u003E?","web.restricted.verifyPhone.more.needHelp.subtext":"出于安全考虑，你必须使用此电话号码才能登录。","widget.v2.hsa2.verify.device.help.popup.cannot.use.phone.text":"无法使用 \u003Cspan class=\"phone-number force-ltr\"\u003E{0:phonenumber}\u003C\/span\u003E","widget.v2.title.chooseDevice.sendVerificationCode":"选取用于接收验证码的电话号码。","widget.v2.hsa2.choose.phone.caption.message.rates.may.apply":"可能会产生短信或数据费用。","web.hsa2.verify.cantAccessPhoneNumbers":"无法使用这些电话号码？","web.hsa2.verify.voiceCall":"语音来电","web.hsa2.verify.textMsg":"短信","web.suppressIforgot.noDevicesHelpDialog.text":"请联系你所在组织的管理员或经理，为你的管理式 Apple 账户重设电话号码。","web.hsa2.verify.device.help.deviceOffline.question":"设备处于离线状态或没有收到验证码？","web.hsa2.verify.device.help.deviceOffline.answer":"在你的受信任设备上使用代码生成器。\u003Ca href=\"{0}\" class=\"click-handle ax-outline\" target=\"_blank\"\u003E了解如何操作\u003Ci class=\"icon icon_right_chevron\"\u003E\u003C\/i\u003E\u003C\/a\u003E","web.hsa2.verify.device.help.deviceNoAccess.question":"无法访问你的受信任设备或电话号码？","web.hsa2.use.recovery.key.description":"如果你有恢复密钥，你可以使用此密钥来登录你的账户。\u003Ca href=\"{0}\" class=\"{1}\" target=\"_blank\"\u003E了解如何操作\u003Ci class=\"icon icon_right_chevron\"\u003E\u003C\/i\u003E\u003C\/a\u003E","web.hsa2.verify.device.help.disclaimer":"账户恢复可能需要几天或更长的时间才能完成，具体取决于你可提供的用来验证你身份的信息。\u003Ca href=\"{0}\" class=\"click-handle ax-outline\" target=\"_blank\"\u003E了解更多\u003Ci class=\"icon icon_right_chevron\"\u003E\u003C\/i\u003E\u003C\/a\u003E","web.suppressIforgot.noDevicesHelpDialog.title":"无法使用你的电话号码？","web.hsa2.verify.device.help.title":"更多验证选项","widget.v2.footer.smsConsent.hsa2.codeWillBeSent":"通过发送至受信任电话号码的验证码登录账户。这可能会产生短信或数据费用。","widget.v2.verifyPhone.footer.link.can.not.use.this.number":"无法使用此号码？","web.button.text.cancel":"取消","widget.v2.footer.smsConsent.hsa2.send.code.button":"发送验证码","webAuthn.authentication.notSupported.update.browser.title":"更新网页浏览器","webAuthn.authentication.notSupported.use.different.browser.title":"此浏览器不支持安全密钥","webAuthn.authentication.notSupported.signin.error":"若要使用安全密钥登录，请将此浏览器更新至最新版本。","webAuthn.authentication.notSupported.use.different.browser":"尝试在其他浏览器中使用你的安全密钥登录。","web.hsa2.verify.key":"请使用安全密钥登录你的 Apple 账户：\u003Cb\u003E{0}\u003C\/b\u003E","widget.v2.verifyPhone.footer.link.resend.code":"重新发送验证码","widget.v2.footer.verifyCode.hsa2.codeHasBeenSent":"输入通过短信发送至 \u003Cspan class=\"displayName force-ltr\"\u003E{0:phonenumber}\u003C\/span\u003E 的验证码。","widget.v2.footer.verifyCode.hsa2.codeHasBeenSentOnVoice":"输入通过拨打至 \u003Cspan class=\"displayName force-ltr\"\u003E{0:phonenumber}\u003C\/span\u003E 的语音来电获取的验证码。","web.managedAppleID.verifyCode.link.didntReceiveCode":"没有验证码？","web.managedAppleID.verifyCode.enterCode":"输入你所在组织提供的验证码。","web.managedAppleID.verifyCode.getCodeFromAdmin":"如果你没有验证码，请联系你的教师或管理员以获得验证码。","web.title.email.checkEmailOtp":"查看电子邮件","web.footer.email.verifyEmailOtp.codeHasBeenSent":"包含验证码的信息已发送至 \u003Cspan class=\"displayName force-ltr\"\u003E{0}\u003C\/span\u003E。输入验证码以登录。","web.title.email.verify.tryAgain.emailOtp":"重新发送验证码","web.footer.emailOtp.verifyCode.link.didntReceiveCode":"没有收到验证码？","widget.emailotp.signin.with.password":"通过密码登录"}
      
        }
    </script>


    
    
        
    


<script type="application/json" class="boot_args">
    {"direct":{"isPasswordSecondStep":true,"scriptSk7Url":"https://appleid.cdn-apple.com/appleauth/static/module-assets/home-c808154549437e821782.js","isFederatedAuthEnabled":true,"accountNameAutoComplete":"username webauthn","urlBag":{"passwordReset":"https://iforgot.apple.com/password/verify/appleid","createAppleID":"https://account.apple.com/account","appleId":"https://account.apple.com","verificationCodeHelp":"https://support.apple.com/kb/HT204974","accountRecoveryHelp":"https://support.apple.com/kb/HT204921","crResetUrl":"https://gsa.apple.com/iforgot/request/questions/reset","privacyPolicyUrl":"https://gsa.apple.com/appleid/account/privacypolicy","unlockSelfService":"https://iforgot.apple.com/unlock"},"authAttributes":"qonB+ybAlxS//zfMG8H2u0h71lY5i0BqnIxPWB3zP9pCtcF6xNdgQRbJma8sgQ7H2toWq1EgsxudafxBKGo9XsrAw483eOwl1WtPdk2wu58KJ9q3/RMgrz8FV/eRlq0v/V6UuYfNDozQeKVJuNX0VNnOnUvG3wJz8uisv/xQVAWmH3GjE2KmNToMZtVni4ZOk9WKeW5d4xk8j/znWAgXK3D/h1fgaJA2F9hqbLvK9uwf+NyUpY1xMNSwxy+HXinZRMYLjhT4vTr14YAtUMxTfToPikQQ8PTHNi8AAZVE+zSuiw==","isRaFF":true,"forgotPassword":true,"swpAuth":true,"twoFactorAuthEnv":1,"countryCode":"CN","isInterstitialFedAuthPageEnabled":true,"twoFactorAuthAppKey":"SRHyr6vucF9FNfGnoXtD6psCTodEz0D1j3edwS3TysI=","isRtl":"false","hashcash":{"hashcashGenerationTimeout":10,"hcChallenge":"22d62536b3f186c3b0a645a529031fe3","hcBits":"10","isHCstrictTimeout":true},"webSRPClientWorkerScriptTag":"\u003Cscript type=\"text/javascript\" src=\"https://appleid.cdn-apple.com/appleauth/static/jsj/**********/webSRPClientWorker.js\" \u003E\u003C/script\u003E","isRaIdp":true,"consentTextV2":"您的 Apple 账户信息用于让您安全登录和访问您的数据。Apple 会出于安全、支持和报告目的记录某些数据。如果您同意，Apple 还可能使用您的 Apple 账户信息向您发送营销电子邮件和通信，包括基于您对 Apple 服务的使用情况。\u003Ca target=\"_blank\" href=\"https://www.apple.com.cn/legal/privacy/data/zh-cn/apple-id/\"\u003E了解数据的管理方式…\u003Cspan class=\"sr-only\"\u003E在新窗口中打开。\u003C/span\u003E\u003C/a\u003E","acUrl":"https://idmsa.apple.com/IDMSWebAuth/acsignin","enableSRPAuth":true,"domainId":43,"shouldSuppressIForgotLink":false,"disableChromeAutoComplete":true,"generateHashcashScriptTag":"\u003Cscript type=\"text/javascript\" src=\"https://appleid.cdn-apple.com/appleauth/static/jsj/793768123/generateHashcash.js\" \u003E\u003C/script\u003E","iframeId":"auth-ysan8zob-gmkx-394y-krkc-q8bdzd9k","meta":{"futureReservedAuthUIModes":["popped-window","dialog","tabbed"],"supportedAuthUIModes":["window","embed","inline"],"FEConfiguration":{"pmrpcTimeout":"1000","enableAllowAttribute":true,"enableSwpAuth":true,"isEyebrowTextboxEnabled":false,"skVersion":"7","pmrpcRetryCount":"5","jsLogLevel":"ERROR","appLoadDelay":"0","enablePerformanceLog":false}},"enableFpn":true,"enableSecurityKeyIndividual":false},"additional":{}}
</script>

    
    
        <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsj/1011928743/init-app.js" ></script>

    






    

    
<script src="https://appleid.cdn-apple.com/appleauth/static/module-assets/home-c808154549437e821782.js"></script>

    
        
            
            <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsj/1923713838/widget/auth/app.js" ></script>

        
    
    
        
    
    
        
            
            
                
                    
                        <script type="text/javascript"
                            src="https://appleid.cdn-apple.com/appleauth/static/jsapi/format-phonenumber/format-phonenumber.js?v=2"></script>
                    
                    
                
            
        
    
</body>
</html>

