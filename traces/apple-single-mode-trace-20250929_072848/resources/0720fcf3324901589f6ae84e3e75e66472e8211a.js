/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[9381],{1230:(e,a,t)=>{t.r(a),t.d(a,{default:()=>J});var l=t(8168),n=t(1594),r=t.n(n),c=t(5911),o=t(3416),i=t(2224),m=t.n(i);const s=e=>{const{header:a,affordabilityOverlaySections:t}=e,{emiContent:n,cashbackContent:i,tradeinContent:s}=m().get(t.items[0],"value");return r().createElement(o.Og,null,r().createElement("div",{className:"rf-affordability-overlay"},r().createElement("div",(0,l.A)({className:"rf-affordability-header"},(0,c.OH)(a))),r().createElement("ul",{className:"rf-affordability-content"},i?r().createElement("li",(0,l.A)({className:"rf-affordability-item"},(0,c.OH)(i))):null,n?r().createElement("li",(0,l.A)({className:"rf-affordability-item"},(0,c.OH)(n))):null,s?r().createElement("li",(0,l.A)({className:"rf-affordability-item"},(0,c.OH)(s))):null),r().createElement(o.An.Consumer,null,(e=>r().createElement(o.gn,{footnotes:e})))))};var d=t(6942),u=t.n(d),E=t(7856),f=t(6637),b=t(1265),p=t(2787),g=t(1229);const A=e=>{let{bankData:a,bankDetails:t,selectedBank:n,isSingleBank:o,tableCaption:i,isLoanAndEmi:m,productInfo:s}=e;const{viewport:d}=(0,f.S)(),{tableColumnNames:E,ignoreTableColumnsOrderList:p,tableColumnsOrderList:g=[]}=a;let A;if(n&&!o){const e=t.find((e=>e.name===n));A=e?e.installmentsOptions:null}else o&&(A=t[0]?.installmentsOptions||null);if(!A)return null;const N=u()("rf-emicalculator-tablewrapper",{"rf-emicalculator-tablegrouped":p,"rf-emicalculator-nogroup":!p,"rf-emicalculator-loanandemi-tablewrapper":m});return r().createElement("div",null,r().createElement("table",{className:N},i&&r().createElement("caption",(0,l.A)({className:"visuallyhidden"},(0,c.OH)(`${i}-${s?.productTitle||""}`))),r().createElement("thead",null,r().createElement("tr",{className:"rf-emicalculator-duration"},E.map((e=>r().createElement("th",{scope:"col",className:u()("rf-emicalculator-headings",b.A.BODY_REDUCED,{visuallyhidden:m}),key:e},e))))),r().createElement("tbody",null,A.map((e=>r().createElement("tr",{className:"rf-emicalculator-tablerow",key:e.termsText},p?r().createElement(r().Fragment,null,r().createElement("td",{className:"rf-emicalculator-data"},e.noCostEMIText?r().createElement("div",{className:`rf-financeoverlay-nocost-emi ${b.A.BODY_REDUCED}`},e.noCostEMIText):null,e.specialOffersText?r().createElement("div",{className:`rf-financeoverlay-specialoffers-text ${b.A.BODY_REDUCED}`},e.specialOffersText):null,r().createElement("div",{className:u()(`rf-emicalculator-term ${b.A.TOUT}`,{[b.A.BODY_TIGHT]:m})},e.termsText),e.emiDesc&&r().createElement("div",(0,l.A)({className:u()(`rf-emicalculator-desc ${b.A.BODY_REDUCED}`,{[b.A.CAPTION]:m})},(0,c.OH)(e.emiDesc)))),r().createElement("td",{className:"rf-emicalculator-data"},e.monthlyAmount?r().createElement(r().Fragment,null,r().createElement("div",(0,l.A)({className:u()("rf-financeoverlay-installment",{[b.A.BODY_TIGHT]:m})},(0,c.OH)(e.monthlyAmount))),e.totalSavingsText&&r().createElement("div",(0,l.A)({className:"rf-financeoverlay-savings"},(0,c.OH)(e.totalSavingsText))),e.noCostEmiSavingsText&&r().createElement("div",(0,l.A)({className:`rf-financeoverlay-savings ${b.A.CAPTION}`},(0,c.OH)(e.noCostEmiSavingsText))),e.cashbackSavingsText&&r().createElement("div",(0,l.A)({className:`rf-financeoverlay-savings ${b.A.CAPTION}`},(0,c.OH)(e.cashbackSavingsText)))):null)):r().createElement(r().Fragment,null,g.map(((a,t)=>r().createElement("td",{key:E[t],className:"rf-emicalculator-data"},r().createElement("div",{className:u()("rf-emicalculator-dataheader",{[b.A.BODY_REDUCED]:"small"===d})},E[t]),r().createElement("div",{className:`rf-emicalculator-${a}`},e[a])))))))))))},N=e=>{let{data:a={}}=e;const{viewport:t}=(0,f.S)(),{header:n=[],rows:o=[]}=a;return r().createElement("div",null,r().createElement("table",{className:"rf-emicalculator-tablewrapper rf-loancalculator-tablewrapper rf-emicalculator-nogroup"},r().createElement("thead",null,r().createElement("tr",{className:"rf-emicalculator-duration"},n.map((e=>r().createElement("th",(0,l.A)({key:e,scope:"col",className:"rf-emicalculator-headings rf-loancalculator-headings"},(0,c.OH)(e))))))),r().createElement("tbody",null,o.map(((e,a)=>r().createElement("tr",{key:a,className:"rf-emicalculator-tablerow"},e&&e.map(((e,a)=>r().createElement("td",{key:n[a],className:"rf-emicalculator-data rf-loancalculator-data"},r().createElement("div",(0,l.A)({className:u()("rf-emicalculator-dataheader",{[b.A.BODY_REDUCED]:"small"===t})},(0,c.OH)(n[a]))),r().createElement("div",(0,l.A)({className:"rf-emicalculator-value"},(0,c.OH)(e))))))))))))};var h=t(4067),v=t(9138);const k=e=>{let{id:a,bankDetails:t,bankLabel:l,bankValue:n,onBankListChange:c}=e;return r().createElement(r().Fragment,null,t?.length?r().createElement(h.A,{id:`rf-emicalculator-dropdown-${a}`,name:`rf-emicalculator-dropdown-${a}`,onChange:c,label:l,value:n},t.map((e=>r().createElement(v.A,{value:e.name,key:e.name},e.name)))):null)},y=e=>{let{id:a,bankDetails:t,bankLabel:l,bankValue:n,onBankListChange:c,showProductList:o,productList:i=[],onProductListChange:m,productLabel:s,productValue:d}=e;return r().createElement(r().Fragment,null,r().createElement("div",{className:"rf-emicalculator-dropdowns column"},r().createElement(k,{id:a,bankDetails:t,bankLabel:l,bankValue:n,onBankListChange:c})),o?r().createElement("div",{className:"rf-emicalculator-dropdowns column"},r().createElement(h.A,{id:`rf-emicalculator-item-dropdown-${a}`,name:`rf-emicalculator-item-dropdown-${a}`,onChange:m,label:s,value:d},i.map((e=>r().createElement(v.A,{value:e.key,key:e.key},e.value))))):null)};var C=t(8595);const O=e=>{let{id:a,bankDetails:t,bankLabel:l,bankValue:n,onBankListChange:c,showBankList:o,isSingleBank:i,textField:m,textLabel:s,textValue:d,onTextChange:E,textError:f,currencySymbol:b,onButtonClick:p,buttonLabel:g}=e;return r().createElement(r().Fragment,null,o&&r().createElement("div",{className:"rf-emicalculator-dropdowns column"},r().createElement(k,{id:a,bankDetails:t,bankLabel:l,bankValue:n,onBankListChange:c})),r().createElement("div",{className:u()("rf-emicalculator-amount column",{"rf-emicalculator-singlebank-amount":i})},r().createElement(C.A,{type:"text",name:m,label:s,value:d,onChange:E,autoComplete:"off",error:f,countryCode:b})),r().createElement("div",{className:u()("rf-emicalculator-button column",{"rf-emicalculator-singlebank-button":i})},r().createElement("button",{className:"button button-block button-super",type:"button",onClick:p},g)))},T=e=>{let{productInfo:a,productPrice:t}=e;return r().createElement("div",{className:u()("rf-emicalculator-productInfo",{"rf-emicalculator-productInfo-detailed":a.productTitle})},a.productTitle&&r().createElement("div",{className:"rf-emicalculator-productTitle"},a.productTitle),r().createElement("div",{className:u()("rf-emicalculator-productPrice",b.A.BODY_TIGHT,{"rf-emicalculator-productPrice-notitle":!a.productTitle})},t," ",a.tradeInText?a.tradeInText:""),a.appleCareText&&r().createElement("div",(0,l.A)({className:`rf-emicalculator-productInclusions ${b.A.CAPTION}`},(0,c.OH)(a.appleCareText))))};var L=t(9142);const D="amount",x=e=>{let{bankDetails:a,formIdx:t=0,formProps:l,isLoanAndEmi:c,selectedTabTitle:o}=e;const{isSingleBankOption:i,isLoanFinancing:s,selectLabel:d,buttonLabel:E,itemLabel:b,productInfo:h,label:v,url:k,onCalulateClick:C,currencySymbol:x,loanData:I,bankData:H,productList:w,amountRequiredErrorMessage:S,maximumAmount:B,minimumAmount:R,amountRangeErrorMessage:F,amountRegex:P,loanAmount:$,setLoanAmount:U,tableCaption:M}=l,{viewport:V}=(0,f.S)(),_="small"===V,Y=m().find(a,{default:!0}),q=m().get(Y,"name",null),K=m().get(a,"0.name",null),W=m().get(H,"singleIssuerEnabled",null),z=q||K,G=m().get(w,"0.value",null),j=h??{},[J,Q]=(0,n.useState)(z),[X,Z]=(0,n.useState)(G),{initialValues:ee,validationSchema:ae}=r().useMemo((()=>((e,a,t,l,n,r)=>{const c={[D]:r||""},o={};return o[D]=[],o[D].push({type:"required",message:e}),o[D].push({type:"range",restrictions:{maxVal:a,minVal:t},message:l}),n&&o[D].push({type:"regex",regEx:[n],message:l}),{initialValues:c,validationSchema:o}})(S,B,R,F,P,$)),[]),{values:te,handleChange:le,errors:ne,setValues:re}=(0,p.A)({initialValues:ee,validationSchema:ae}),ce=(0,g.A)(((e,a)=>{C(e,a)}),700);return r().useEffect((()=>{re({[D]:$})}),[$]),r().createElement(r().Fragment,null,r().createElement("div",{className:u()("rf-emicalculator-form",{"rf-emicalculator-singlebank-form":W})},r().createElement("div",{className:u()("rf-emicalculator-formtextbox row",{"form-textbox-sidebyside":!_})},c&&r().createElement(y,{id:t,bankDetails:a,bankLabel:d,bankValue:J,onBankListChange:e=>{Q(e.target.value),L.A.onLoanAndEmiCardTabDropdownChange({tabName:o,bankName:e.target.value,itemName:X})},showProductList:w?.length&&!j.productTitle,productList:w,onProductListChange:e=>{Z(e.target.value),ce(k,{spn:e.target.value}),L.A.onLoanAndEmiCardTabDropdownChange({tabName:o,bankName:J,itemName:e.target.value})},productLabel:b,productValue:X}),!c&&r().createElement(O,{id:t,bankDetails:a,bankLabel:d,bankValue:J,onBankListChange:e=>Q(e.target.value),showBankList:!(i||s),isSingleBank:W,textField:D,textLabel:v,textValue:te[D],onTextChange:le,textError:ne[D],currencySymbol:x,buttonLabel:E,onButtonClick:()=>{ne[D]||(C(k,{ca:te[D]}),U(te[D])),L.A.fireCalculateButtonClick(J)}}))),c&&r().createElement(T,{productInfo:j,productPrice:H.priceString}),s&&r().createElement(N,{data:I}),!s&&r().createElement(A,{tableCaption:M,bankData:H,bankDetails:a,selectedBank:J,isSingleBank:W,isLoanAndEmi:c,productInfo:j}))},I=e=>{const{headerTitle:a,headerSubTitle:t,specialOffersContent:i,formHeaderTitle:s,emiTermsFooter:d,footer:p,amountLabel:g,amount:A,calculateButtonLabel:N,calculateUrl:h,bankData:v={},productList:k=[],selectLabel:y,itemLabel:C,productInfo:O={},onCalulateClick:T,amountRegex:D,amountRangeErrorMessage:I,maximumAmount:H,minimumAmount:w,amountRequiredErrorMessage:S,currencySymbol:B,isSingleBankOption:R=!1,isLoanFinancing:F=!1,isLoanAndEmi:P=!1,data:$={},tableCaption:U,tags:M={}}=e,{viewport:V}=(0,f.S)(),_="small"===V,Y=m().get(v,"creditDebitBankList",[]),[q,K]=(0,n.useState)(A),[W,z]=(0,n.useState)([]),[G,j]=(0,n.useState)([]),J=[...W,...G],{FormHeader:Q="h5"}=M,X={isSingleBankOption:R,isLoanFinancing:F,selectLabel:y,buttonLabel:N,itemLabel:C,productInfo:O,label:g,url:h,onCalulateClick:T,currencySymbol:B,loanData:$,bankData:v,productList:k,amountRequiredErrorMessage:S,maximumAmount:H,minimumAmount:w,amountRangeErrorMessage:I,amountRegex:D,loanAmount:q,setLoanAmount:K,tableCaption:U};return r().createElement("div",{className:"rf-emicalculator"},r().createElement(o.Og,{onFootnotesUpdated:z},a&&r().createElement("h2",(0,l.A)({className:`rf-emicalculator-header ${b.A.HEADLINE}`,id:"rf-emicalculator-header"},(0,c.OH)(a))),t&&r().createElement("div",null,r().createElement("p",(0,l.A)({className:"rf-emicalculator-info"},(0,c.OH)(t)))),i&&r().createElement("div",(0,l.A)({className:"rf-emicalculator-financing-options-wrapper"},(0,c.OH)(i))),s&&r().createElement(Q,{className:u()("rf-emicalculator-formheader",{[b.A.EYEBROW_REDUCED]:!_,[b.A.EYEBROW]:_}),id:"calculator-table-header"},s)),Y&&Y.length>1&&r().createElement(E.Ay,{count:Y.length,className:u()("rf-emicalculator-tabcontainer"),handleChange:e=>{L.A.onLoanAndEmiCardTabChange(Y[e].title)}},r().createElement(E.jl,{"aria-labelledby":"calculator-table-header",items:Y.map((e=>r().createElement("span",(0,l.A)({key:e.title},(0,c.OH)(e.title))))),keys:Y.map((e=>`tabnav-${e.title}`)),classes:{root:"rf-emicalculator-tabnav",items:"rf-emicalculator-tabnav-items",item:"rf-emicalculator-tabnav-item",link:u()("rf-emicalculator-button button button-tertiary-neutral",{[b.A.BODY_REDUCED]:_})}}),r().createElement(E.T2,null,Y.map(((e,a)=>r().createElement(E.Kp,{index:a,key:e.title,mountOnEnter:!0,unmountOnExit:!0},r().createElement(o.Og,{onFootnotesUpdated:j},r().createElement(x,{formProps:X,bankDetails:e.bankDetails,formIdx:a,isLoanAndEmi:P,selectedTabTitle:e.title}))))))),Y&&1===Y.length&&r().createElement(o.Og,{onFootnotesUpdated:j},r().createElement(x,{formProps:X,bankDetails:m().get(Y,"[0].bankDetails",[]),isLoanAndEmi:P,selectedTabTitle:m().get(Y,"[0].title","")})),(d||p)&&r().createElement("div",(0,l.A)({className:`rf-emicalculator-legalfooter ${b.A.CAPTION}`},(0,c.OH)(d||p),{onClick:e=>{const a=e.target.closest?.("A"),t=a.getAttribute("data-financing-partner");if(a){const e=a.cloneNode(!0);e.querySelectorAll(".visuallyhidden").forEach((e=>e.remove()));const l=e.textContent.trim();L.A.onLoanAndEmiFooterLinkClick({linkText:l,partnerName:t})}}})),J&&r().createElement(o.gn,{footnotes:J,className:"rf-emicalculator-dynamic-footnotes rc-footnoteslist-overlay"}))};var H=t(5156);const w=e=>{const a=r().useRef(e);return[e=>t=>{a.current&&(a.current[e]=t)},e=>a?.current?.[e],a]};var S=t(840),B=t(6080);const R={base:"base",reduced:"reduced",tiny:"tiny"},F=(e,a)=>{let{tag:t="div",size:n=R.base,badge:o,label:i,caption:m,index:s,selected:d,onChange:E,onKeyDown:f,children:b,...p}=e;const g=r().useRef(),A=r().useRef(!1),N=n===R.reduced||n===R.tiny,h=n===R.reduced||n===R.tiny;return r().useEffect((()=>{d&&A.current&&g.current.focus(),A.current=!0}),[d]),r().createElement(t,{className:u()("rc-segmented-control-item",{"rc-segmented-control-with-badge":!!o&&n===R.base,"typography-caption":n===R.tiny}),ref:a},r().createElement("button",(0,l.A)({type:"button",role:"radio",ref:g,tabIndex:d?0:-1,"aria-checked":d,className:u()("rc-segmented-control-button",{"rc-segmented-control-selected":d}),onKeyDown:f,onClick:()=>{d?g.current.focus():E(s)}},p),b&&r().createElement(r().Fragment,null,b),!b&&r().createElement(r().Fragment,null,o&&!N&&r().createElement(B.A,(0,l.A)({className:"rc-segmented-control-badge",noScrim:!0,reduced:!0},(0,c.OH)(o))),i&&r().createElement("span",(0,l.A)({className:"rc-segmented-control-label"},(0,c.OH)(i))),m&&!h&&r().createElement("span",(0,l.A)({className:"rc-segmented-control-caption typography-caption"},(0,c.OH)(m))))))},P=r().forwardRef(F),$=e=>{let{tag:a="div",index:t=0,size:n=R.base,groupLabel:o,onChange:i=()=>{},children:m,dir:s,mowNoWrap:d=!1,...E}=e;const{viewport:f}=(0,H.S)(),b="small"===f,p=r().useMemo((()=>"rtl"===s||"rtl"===(0,c.NL)()),[s]),g=m.filter((e=>e.type===P)).length,[A,N]=w({}),h=()=>{i(t-1>-1?t-1:g-1)},v=()=>{i(t+1<g?t+1:0)},k=e=>{e.keyCode===S.HP.ArrowRight?p?h():v():e.keyCode===S.HP.ArrowLeft&&(p?v():h())},y=!!(m||[]).find((e=>!!e.props.badge)),C=!!(m||[]).find((e=>!!e.props.caption)),O=n===R.reduced,T=n===R.tiny,L=m&&2===m.length,D=!(T||L);return r().useLayoutEffect((()=>{D&&b&&N(t)?.scrollIntoView({block:"nearest",inline:"center"})}),[]),r().createElement("fieldset",{className:"rc-segmented-control-fieldset"},r().createElement("legend",{className:"visuallyhidden"},o),r().createElement(a,(0,l.A)({className:u()("rc-segmented-control typography-body-tight",{"rc-segmented-control-has-badge":y,"rc-segmented-control-has-caption":C,"rc-segmented-control-has-reduced":O,"rc-segmented-control-has-tiny":T,"rc-segmented-control-is-bimodal":L,"rc-segmented-control-mow-nowrap":d}),role:"radiogroup"},E),m&&m.map(((e,a)=>e.type===P?r().createElement(P,(0,l.A)({key:e.key,ref:A(a),index:a,size:n,selected:t===a,onChange:i,onKeyDown:k},e.props.children?{children:e.props.children}:e.props)):null))))};var U=t(7558),M=t(986),V=t(6777);const _=e=>{let{logoImage:a}=e;return r().createElement("div",{className:"rf-loan-card-image"},r().createElement(V.Ay,{data:a}))},Y=e=>{let{cardData:a,tags:t={}}=e;const{LoanCardTitle:n="h4"}=t;return r().createElement(r().Fragment,null,a?.emiOptions?.length?r().createElement("div",{className:"rf-loan-card-details"},r().createElement(n,(0,l.A)({className:u()("rf-loan-card-title",b.A.TOUT)},(0,c.OH)(a.emiTitle))),a.emiOptions.map((e=>r().createElement("div",{className:"rf-loan-emi-list",key:e.termMonths},r().createElement("div",{className:"rf-loan-emi-details"},r().createElement("span",(0,l.A)({className:"rf-loan-details-cashback-item"},(0,c.OH)(e.termMonths))),r().createElement("span",(0,l.A)({className:"rf-loan-details-cashback-item"},(0,c.OH)(e.interestRate)))),r().createElement("div",{className:"rf-loan-emi-info"},r().createElement("span",{className:u()("rf-loan-emi-info-item",b.A.CAPTION)},e.cashbackDetails),r().createElement("span",{className:u()("rf-loan-emi-info-item",b.A.CAPTION)},e.emiDetails),r().createElement("span",{className:u()("rf-loan-emi-info-item",b.A.CAPTION)},e.upgradeDetails)))))):null)},q=e=>{let{cardData:a={},tabTitle:t,tags:n={}}=e;const{viewport:o}=(0,f.S)(),i="small"===o,{LoanCardTitle:m="h4"}=n;return r().createElement("div",{className:"rf-loan-card-wrapper"},i&&r().createElement(_,{logoImage:a.logoImage}),r().createElement("div",{className:"rf-loan-card-details-wrapper"},a.showEmiFirst&&r().createElement(Y,{cardData:a,tags:n}),a.cashbackDetails?.length?r().createElement("div",{className:"rf-loan-card-details"},r().createElement(m,(0,l.A)({className:u()("rf-loan-card-title",b.A.TOUT)},(0,c.OH)(a.cashbackTitle))),a.cashbackDetails?.map((e=>e.name?r().createElement("div",{className:"rf-loan-details-list",key:e.name},r().createElement("span",(0,l.A)({className:"rf-loan-details-cashback-item"},(0,c.OH)(e.name))),r().createElement("span",(0,l.A)({className:"rf-loan-details-cashback-item"},(0,c.OH)(e.offerText)))):r().createElement("div",(0,l.A)({key:e.offerText},(0,c.OH)(e.offerText)))))):null,!a.showEmiFirst&&r().createElement(Y,{cardData:a,tags:n}),a.primaryButton?.url&&r().createElement("div",{className:"rf-loan-card-shop"},r().createElement(M.A,(0,l.A)({},a.primaryButton,{attrs:{className:"button",rel:a.primaryButton.newTab?"noopener noreferrer":void 0,onClick:()=>{L.A.onLoanAndEmiShopButtonClick(t,a?.primaryButton?.omnitureData?.linkText)}}})))),!i&&r().createElement(_,{logoImage:a.logoImage}))},K=e=>{let{items:a=[],footer:t,title:n,tags:o={}}=e;return r().createElement("div",{className:"rf-loan-details-wrapper"},a.length>0&&a.map((e=>r().createElement(q,{cardData:e,key:e.cashbackTitle,tabTitle:n,tags:o}))),t&&r().createElement("div",(0,l.A)({className:u()("rf-loan-details-legalfooter",b.A.CAPTION)},(0,c.OH)(t),{onClick:e=>{const a=e.target.closest?.("A"),t=a.getAttribute("data-financing-partner");if(a){const e=a.cloneNode(!0);e.querySelectorAll(".visuallyhidden").forEach((e=>e.remove()));const l=e.textContent.trim();L.A.onLoanAndEmiFooterLinkClick({linkText:l,partnerName:t})}}})))},W=e=>{let{items:a=[],specialOffersContent:t,footer:n,title:o,tags:i={}}=e;return r().createElement("div",{className:"rf-credit-card-details-wrapper"},t&&r().createElement("div",(0,l.A)({className:"rf-credit-card-details-header"},(0,c.OH)(t))),a.length>0&&a.map((e=>r().createElement(q,{cardData:e,key:e.cashbackTitle,tabTitle:o,tags:i}))),n&&r().createElement("div",(0,l.A)({className:u()("rf-loan-details-legalfooter",b.A.CAPTION)},(0,c.OH)(n),{onClick:e=>{const a=e.target.closest?.("A"),t=a.getAttribute("data-financing-partner");if(a){const e=a.cloneNode(!0);e.querySelectorAll(".visuallyhidden").forEach((e=>e.remove()));const l=e.textContent.trim();L.A.onLoanAndEmiFooterLinkClick({linkText:l,partnerName:t})}}})))},z=e=>{let{title:a,subtitle:t,tabheader:i,tabs:m,onCalculateClick:s,CustomCalculator:d,tags:f={}}=e;const[p,g]=(0,n.useState)((()=>m?.findIndex((e=>e.selected)))),[A,N]=(0,n.useState)([]),[h,v]=(0,n.useState)([]),k=[...A,...h],{LoanAndEMIDealsText:y="h3"}=f;return r().createElement("div",{className:"rf-loanandemi-page"},r().createElement(o.Og,{onFootnotesUpdated:N},a&&r().createElement("h2",{className:u()("rf-loanandemi-header",b.A.HEADLINE)},a),t&&r().createElement("p",(0,l.A)({className:u()("rf-loanandemi-subtitle",b.A.INTRO)},(0,c.OH)(t)))),r().createElement("div",{className:"rf-loanandemi-deals-wrapper"},r().createElement(y,{className:u()("rf-loanandemi-deals-text",b.A.HEADLINE_REDUCED)},i),m?r().createElement(E.Ay,{index:p,count:m.length,className:u()("rf-loanandemi-tabcontainer")},r().createElement($,{"aria-label":`${i} options`,index:p,groupLabel:`${i} options`,role:"tablist",onChange:e=>{g(e),L.A.onLoanAndEmiTabChange(i,m[e].title)}},m.map(((e,a)=>r().createElement(P,{id:`tab-${a}`,key:e.title,label:e.title,caption:e.subtitle,role:"tab","aria-controls":`tabpanel-${a}`,"aria-selected":p===a,tabIndex:p===a?0:-1,"data-autom":`filterButton-${e.title}`})))),r().createElement(E.T2,{className:"rf-loanandemi-tabpanels"},m.map(((e,a)=>r().createElement(r().Fragment,{key:e.title},r().createElement(E.Kp,{id:`tabpanel-${a}`,index:a,className:u()("rf-loanandemi-tabcontent",{"rf-loanandemi-tabcontent-align-center":e.offerType!==U.n.CARD}),role:"tabpanel","aria-labelledby":`tab-${a}`,mountOnEnter:!0,unmountOnExit:!0},r().createElement(o.Og,{onFootnotesUpdated:v,className:"rf-loanandemi-tabcontent-container"},e.offerType===U.n.LOAN&&r().createElement(K,(0,l.A)({},e,{tags:f})),e.offerType===U.n.CARD&&(d?r().createElement(d,e):r().createElement(I,(0,l.A)({},e,{onCalulateClick:s,isLoanAndEmi:!0,tags:f}))),e.offerType===U.n.CREDIT_CARD&&r().createElement(W,(0,l.A)({},e,{tags:f}))))))))):null,k&&r().createElement(o.gn,{footnotes:k,className:"rf-emicalculator-dynamic-footnotes"})))};var G=t(5774);const j=e=>{const{headerTitle:a,headerSubTitle:t,bankData:i,selectLabel:s,cancellationPolicyText:d,cancellationPolicyLabel:E,orderMinimumLabel:f,isRenderedInOverlay:p=!1,momoOverlayInstallment:g}=e,{creditDebitBankList:A,tableSectionHeader:N,tableColumnNames:k,tableColumnsOrderList:y,tableAriaLabel:C}=i,{title:O,bankDetails:T}=m().get(A,"0",[]),L=m().find(T,{default:!0}),D=m().get(T,"0",{}),x=m().get(L,"name"),[I,H]=(0,n.useState)(x||D?.name),[w,S]=(0,n.useState)(L||D);return r().createElement(o.Og,null,p&&g&&r().createElement("div",(0,l.A)({className:"rf-cci-static-content"},(0,c.OH)(g))),r().createElement("div",{className:u()("rf-cci",{"rf-cci-overlay":p})},r().createElement("h4",{id:"rf-cci-header"},r().createElement("span",(0,c.OH)(O))),a&&r().createElement("div",(0,l.A)({},(0,c.OH)(a),{className:u()("rf-cci-header-title",b.A.EYEBROW_SUPER)})),t&&r().createElement("div",(0,l.A)({},(0,c.OH)(t),{className:b.A.BODY})),N&&r().createElement("div",(0,l.A)({},(0,c.OH)(N),{className:u()(b.A.CAPTION,"rf-cci-tablesectionheader")})),r().createElement("div",{className:"row"},r().createElement("div",{className:"rf-cci-dropdown column"},r().createElement(h.A,{id:"rf-cci-dropdown",name:"rf-cci-dropdown",onChange:e=>{H(e?.target?.value),S(m().find(T,{name:e?.target?.value}))},label:s,value:I},T?.map((e=>r().createElement(v.A,{value:e.name,key:e.name},e.name)))))),r().createElement("table",{className:u()("rf-cci-table",b.A.BODY_REDUCED),"aria-label":C},r().createElement("thead",null,r().createElement("tr",null,k?.map((e=>r().createElement("th",(0,l.A)({key:e,className:u()("rf-cci-table-heading",b.A.CAPTION)},(0,c.OH)(e))))))),r().createElement("tbody",null,w?.installmentsOptions?.map((e=>r().createElement("tr",{key:m().get(e,"termsText","")},y?.map((a=>r().createElement("td",(0,l.A)({key:a,className:"rf-cci-table-data"},(0,c.OH)(m().get(e,a,""))))))))))),r().createElement("div",{className:u()("rf-cci-footnotes",b.A.CAPTION)},r().createElement("div",{className:"rf-cci-footnote"},E&&r().createElement("span",(0,l.A)({className:"rf-cci-label"},(0,c.OH)(E))),d&&r().createElement("span",(0,c.OH)(d))),r().createElement("div",{className:"rf-cci-footnote"},f&&r().createElement("span",(0,l.A)({className:"rf-cci-label"},(0,c.OH)(f))),r().createElement("span",(0,c.OH)(m().get(w,"orderMinimumText",""))))),r().createElement(o.An.Consumer,null,(e=>r().createElement(o.gn,{footnotes:e,className:"rf-cci-footnotes"})))))},J=e=>{let{state:a,fetchEMIData:t}=e;const{affordabilityOverlay:n,content:c,type:o,serviceError:i="",isLoanFinancing:m}=a,d=o===G.A.AFFORDABILITY,u=o===G.A.EMI||o===G.A.LOAN,E=o===G.A.CCI,f=o===G.A.LOANANDEMI;return r().createElement(r().Fragment,null,d?r().createElement(s,n):null,u?r().createElement("div",{className:"rf-emicalculator-overlay"},r().createElement(I,(0,l.A)({},c,{isLoanFinancing:m,onCalulateClick:t}))):null,f?r().createElement("div",{className:"rf-loanemicalculator-overlay"},r().createElement(z,(0,l.A)({},a,{onCalculateClick:t,tags:{FormHeader:"h5",LoanAndEMIDealsText:"h3",LoanCardTitle:"h4"}}))):null,E?r().createElement(j,(0,l.A)({},c,{isRenderedInOverlay:E})):null,i?r().createElement("div",null,i):null)}}}]);