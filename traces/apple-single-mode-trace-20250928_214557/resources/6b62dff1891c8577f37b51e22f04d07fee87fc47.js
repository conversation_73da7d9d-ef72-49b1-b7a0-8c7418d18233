/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[9762],{2731:(e,t,a)=>{a.r(t),a.d(t,{default:()=>Le});var r=a(1594),o=a.n(r),s=a(8168),l=a(6942),n=a.n(l),c=a(2224),i=a.n(c),d=a(7427),u=a(7213),p=a(6884),m=a(6777),E=a(1265),g=a(6637),S=a(5911),h=a(3416),y=a(7466),f=a(1875),v=a(2748),b=a(5646),_=a(266),C=a(3658),N=a(2905);const T=(e,t,a)=>{const r=(0,b.tG)("productlocator/handleDeliveryData");if(t.deliveryMessage){const o=i().get(t,"deliveryMessage.errorMessage");if(o)r.debug(`ResponseError: ${o}`),a({type:N.E.PL_FETCH_DELIVERY_ERROR,error:{isResponseError:!0,message:o}});else{let r=i().get(t,"deliveryMessage");r=((e,t)=>{const a={...e};return a.deliveryQuoteNorm=i().get(e,t),a.partNorm=t,a})(r,e["parts.0"]),a({type:N.E.PL_FETCH_DELIVERY_SUCCESS,data:r})}}},L={isFetching:!1,isFetched:!1,data:{},error:null},R=(e,t)=>{switch(t.type){case N.E.PL_FETCH_DELIVERY_START:return{...e,isFetching:!0,isFetched:!1,error:null};case N.E.PL_FETCH_DELIVERY_ERROR:return{...e,isFetching:!1,isFetched:!0,error:t.error,data:{}};case N.E.PL_FETCH_DELIVERY_SUCCESS:return{...e,isFetching:!1,isFetched:!0,error:null,data:t.data};case N.E.PL_CLEAR_DELIVERY_DATA:return L;default:return e}},P=e=>i().sortBy(e,(e=>i().get(e,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1)?-1:1)),A=(e,t)=>e.slice().map((e=>(t&&e.storeNumber===t&&i().get(e,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1)?e.selected=!0:e.selected=!1,e))),k=e=>i().filter(e,(e=>i().get(e,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1))).length,O=(e,t,a,r,o,s)=>(l,n)=>{r&&l({type:N.E.PL_FETCH_PICKUP_START}),o&&n({type:N.E.PL_FETCH_DELIVERY_START});const c=(0,b.tG)("productlocator/fetchFulfillmentData"),d=(0,_.q)((0,C.bj)(e,t));return d.then((e=>({pickupMessage:i().get(e,"body.content.pickupMessage"),deliveryMessage:i().get(e,"body.content.deliveryMessage")}))).then((e=>{if(r&&e.pickupMessage){const t=i().get(e,"pickupMessage.errorMessage");if(t)c.debug(`ResponseError: ${t}`),l({type:N.E.PL_FETCH_PICKUP_ERROR,data:{location:i().get(e.pickupMessage,"location","")},error:{isResponseError:!0,message:t}});else{let t=e.pickupMessage,r=(e=>e.map((e=>{const t=i().get(e,"partsAvailability",{}),a=i().keys(t)[0];return e.partsAvailabilityNorm=i().get(t,a,{}),e})))(i().get(e,"pickupMessage.stores"));r=P(r),t.stores=A(r,a),t.availableStoreCount=k(r),0===t.availableStoreCount&&(t=(e=>{const t=i().get(e,"notAvailableNearby",""),a=e.stores||[],r=t.replace("[X]",a.length);return{...e,notAvailableNearby:r}})(t)),l({type:N.E.PL_FETCH_PICKUP_SUCCESS,data:t})}}o&&e.deliveryMessage&&T(t,e,n)})).catch((e=>{c.error(e),l({type:N.E.PL_FETCH_PICKUP_ERROR,data:{},error:{isNetworkError:!0,message:s}}),n&&n({type:N.E.PL_FETCH_DELIVERY_ERROR,error:{isNetworkError:!0,message:s}})})),d},I={isFetching:!1,isFetched:!1,data:{},error:null,isShown:!0,isToggleBtnNeeded:!1},D=(e,t)=>{switch(t.type){case N.E.PL_PICKUP_SHOW_TOGGLE:return{...e,isToggleBtnNeeded:!0,isShown:!1};case N.E.PL_PICKUP_HIDE_TOGGLE:return{...e,isToggleBtnNeeded:!1,isShown:!0};case N.E.PL_PICKUP_UPDATE_STORE_SELECTION:{const a=A(i().get(e,"data.stores",[]),t.selectedStore);return{...e,data:{...e.data,stores:a}}}case N.E.PL_PICKUP_REMOVE_STORE_SELECTION:{const{stores:t}=e.data;if(t){const a=(e=>{const t=e.slice(),a=i().find(t,{selected:!0});return a&&(a.selected=!1),t})(t);return{...e,data:{...e.data,stores:a}}}return e}case N.E.PL_FETCH_PICKUP_START:return{...e,isFetching:!0,isFetched:!1,error:null};case N.E.PL_FETCH_PICKUP_ERROR:return{...e,isFetching:!1,isFetched:!0,error:t.error,data:t.data};case N.E.PL_FETCH_PICKUP_SUCCESS:return{...e,isFetching:!1,isFetched:!0,error:null,data:t.data};case N.E.PL_CLEAR_PICKUP_DATA:return I;case N.E.PL_CLEAR_PICKUP_ERROR:return{...e,error:null};default:return e}},F=(e,t,a)=>{const r=i().get(e,"PickupMessage.stores",[]),o=i().get(e,"PickupMessage.recommendedProducts");e.products=((e,t,a,r)=>{let o=[];return e.forEach((e=>{i().forEach(e.partsAvailability,((t,s)=>{const l=i().cloneDeep(e);l.partsAvailabilityNorm=t,l.selected=!1;let n=i().find(o,{part:s});n?n.stores.push(i().omit(l,"partsAvailability")):(n={stores:[]},n.part=s,n.displayPrice=(0,v.ae)(s,a,r),n.productImage=(0,v.L6)(s,a,r),n.productTitle=(0,v.A5)(s,a,r),n.isShown=!1,n.stores.push(i().omit(l,"partsAvailability")),o.push(n))}))})),t&&(o=i().sortBy(o,(e=>t.indexOf(e.part)))),o})(r,o,t,a);let s=i().omit(e,"PickupMessage");return s.products=s.products.map((e=>(e.stores=P(e.stores),e))),s=(e=>(e.products=e.products.map((t=>{const a=k(t.stores),r=a>1?e.availableStoresText:e.availableStoreText;return t.availabilityText=r&&r.replace("[X]",a),t})),e))(s),s},U={isFetching:!1,isFetched:!1,data:{},error:null},H=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:U,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case N.E.PL_FETCH_SUGGESTIONS_START:return{...e,isFetching:!0,isFetched:!1,error:null};case N.E.PL_FETCH_SUGGESTIONS_ERROR:return{...e,isFetching:!1,isFetched:!0,error:t.error,data:{}};case N.E.PL_FETCH_SUGGESTIONS_SUCCESS:return{...e,isFetching:!1,isFetched:!0,error:null,data:t.data};case N.E.PL_CLEAR_SUGGESTIONS_DATA:return U;case N.E.PL_SUGGESTIONS_UPDATE_STORE_SELECTION:{const a=((e,t,a)=>e.slice().map((e=>{const r=i().find(e.stores,{selected:!0});if(r&&(r.selected=!1),e.part===t){const t=i().find(e.stores,{storeNumber:a});t&&(t.selected=!0)}return e})))(e.data.products,t.part,t.selectedStore);return{...e,data:{...e.data,products:a}}}case N.E.PL_SUGGESTIONS_REMOVE_STORE_SELECTION:{const{products:t}=e.data;if(t){const a=(e=>e.slice().map((e=>{const t=i().find(e.stores,{selected:!0});return t&&(t.selected=!1),e})))(t);return{...e,data:{...e.data,products:a}}}return e}case N.E.PL_SUGGESTIONS_SHOW_TOGGLE:{const{products:a}=e.data;if(a){const r=((e,t)=>e.slice().map((e=>(e.part===t&&(e.isShown=!e.isShown),e))))(a,t.part);return{...e,data:{...e.data,products:r}}}return e}default:return e}};var M=a(8994);const G={submitSelection:!1,isPickupSelected:!1,isDeliverySelected:!1,isSuggestionSelected:!1},w=(e,t)=>{switch(t.type){case N.E.PL_SELECTION_UPDATE:return{...e,...t.prop};case N.E.PL_SELECTION_REMOVE:return{submitSelection:!1};case N.E.PL_SELECTION_CLEAR:return G;default:return e}};let x={isMetaReceived:!1,combinePickupWithMeta:!1,pickupDebounceInterval:300,metaRequest:{},pickupRequest:{searchNearby:!0},suggestionsRequest:{},deliveryRequest:{"mts.1":"compact"}};const q=(e,t)=>{let{fireRecommendations:a,params:r,location:o,family:s}=e;if(a){const e={...r};e["parts.0"]&&(e.product=e["parts.0"],delete e["parts.0"]),o&&(delete e.store,delete e.searchNearby),((e,t,a)=>r=>{const o=(0,b.tG)("productlocator/suggestions-reducer/fetchSuggestionsData");r({type:N.E.PL_FETCH_SUGGESTIONS_START});const s=(0,_.q)((0,C.bj)(e,t));return s.then((e=>{if(e.body){const s=i().get(e,"body.PickupMessage.errorMessage")||e.body.errorMessage;s?(o.debug(`ResponseError: ${s}`),r({type:N.E.PL_FETCH_SUGGESTIONS_ERROR,error:{isResponseError:!0,message:s}})):(e.body=F(e.body,a,t.cppart),r({type:N.E.PL_FETCH_SUGGESTIONS_SUCCESS,data:e.body}))}else o.debug("ResponseError: No response data"),r({type:N.E.PL_FETCH_SUGGESTIONS_ERROR,error:{isResponseError:!0}})})).catch((e=>{o.error(e),r({type:N.E.PL_FETCH_SUGGESTIONS_ERROR,error:{isNetworkError:!0,message:e}})})),s})(x.suggestionsURL,e,s)(t).then((e=>{const t=i().get(e,"body.products",[]);(0,M.I7)(t,o,s)}))}},V=(e,t,a,r,o)=>{let{part:s,family:l,location:n,isDeliverySelected:c,needsDeliveryData:d,fireRecommendations:u}=e;const p={...x.pickupRequest},m={...x.deliveryRequest};s&&(x.pickupRequest["parts.0"]=s,p["parts.0"]=s,m["parts.0"]=s),n&&(x.pickupRequest.location=n),x.pickupRequest.location&&(p.location=x.pickupRequest.location),p.location&&(delete p.store,delete p.searchNearby);const E=p.location||p.store,g={"mts.0":"regular",...d?m:{},...E?p:{}};(E||d)&&O(x.pickupURL,g,c?null:x.pickupRequest.store,E,d,x.errorMessage)(t,o).then((e=>({body:i().get(e,"body.content.pickupMessage")}))).then((e=>{E&&((e,t,a,r)=>{let{response:o,params:s,location:l,isDeliverySelected:n,fireRecommendations:c,family:d}=e;const u=i().get(o,"body.availableStoreCount");if(0===u?(t({type:N.E.PL_PICKUP_SHOW_TOGGLE}),q({params:s,fireRecommendations:c,location:l,family:d},a)):(t({type:N.E.PL_PICKUP_HIDE_TOGGLE}),a({type:N.E.PL_CLEAR_SUGGESTIONS_DATA}),(0,M.kG)(u,l)),!n){const e=i().find(i().get(o,"body.stores"),{storeNumber:s.store}),t=i().get(e,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1);r({type:N.E.PL_SELECTION_UPDATE,prop:{part:s["parts.0"],isPickupSelected:t,isDeliverySelected:!1,isSuggestionSelected:!1}})}})({response:e,params:{...g,store:x.pickupRequest.store},location:n,isDeliverySelected:c,fireRecommendations:u,family:l},t,a,r),d&&c&&r({type:N.E.PL_SELECTION_UPDATE,prop:{part:s,isPickupSelected:!1,isDeliverySelected:!0,isSuggestionSelected:!1}})}))},B=e=>{e&&(x.pickupRequest.cppart=e,x.deliveryRequest.cppart=e)},$=e=>{let{bootstrap:t,part:a,storeId:r,visible:s,onStoreChange:l,cppart:n,preventClearOnSave:c,tia:d,tt:u,pid:p,stickyTrigger:m}=e;x={...x,...t};const[E,g]=o().useReducer(f.ik,f.Ws),[S,h]=o().useReducer(v.z$,v.Ws),[y,b]=o().useReducer(D,I),[_,C]=o().useReducer(R,L),[T,P]=o().useReducer(H,U),[A,k]=o().useReducer(w,G),[O,F]=o().useState(""),[q,$]=o().useState(),K=i().get(x,"fireRecommendationsAvailability",!1)||!i().get(E,"data.hideRecommendations",!0),Y=i().get(S,"cppart")||i().get(S,"product.carrierModel"),{isDeliverySelected:W}=A,z=o().useRef(null),Q=o().useRef(!0);o().useEffect((()=>{t.family&&(x.metaRequest.family=t.family,x.metaRequest.node=t.node,((e,t,a)=>{e?x.metaRequest.tia=e:delete x?.metaRequest?.tia,t?x.metaRequest.pid=t:delete x?.metaRequest?.pid,a?x.metaRequest.tt=a:delete x?.metaRequest?.tt})(d,p,u),(0,f.sK)(x.metaURL,x.metaSmallURL,x.metaRequest,x.errorMessage,t.family)(g))}),[d,p,u]),o().useEffect((()=>{h({type:N.E.PL_FILTER_SET_CPPART,cppart:n}),B(n)}),[n]),o().useEffect((()=>{var e,o;s&&a&&E.isInitialized&&(x.part=a,e=h,o=t.family,x.part&&e({type:N.E.PL_FILTER_UPDATE,part:x.part,purchaseOption:x.purchaseOptionType,family:o}),k({type:N.E.PL_SELECTION_CLEAR}),x.pickupRequest.store=r,(0,M.Y$)(a,m),(0,M.gE)(r,a))}),[s,a,E.isInitialized,r,t.family]),o().useEffect((()=>{S.part&&(x.part=S.part,B(Y),V({part:S.part,family:t.family,fireRecommendations:K,isDeliverySelected:W,needsDeliveryData:!0},b,P,k,C),Q.current?Q.current=!1:(0,M.Lw)(S.part,t.family))}),[S.part,K,Y,t.family]);const j=()=>{h({type:N.E.PL_FILTER_HIDE}),b({type:N.E.PL_CLEAR_PICKUP_DATA}),C({type:N.E.PL_CLEAR_DELIVERY_DATA}),P({type:N.E.PL_CLEAR_SUGGESTIONS_DATA}),h({type:N.E.PL_FILTER_SET_PART,part:null}),$(),x.pickupRequest={searchNearby:!0},Q.current=!0};return{metaState:E,filterState:S,pickupState:y,deliveryState:_,suggestionsState:T,selectionState:A,store:O,inputRef:z,localLocation:q,setLocalLocation:$,handleFormSubmit:e=>{e.search&&V({location:e.search,fireRecommendations:K,family:t.family},b,P,k)},handleInputBlur:e=>{x.pickupRequest.location=e},handleInputChange:e=>{!e&&y?.error?.isResponseError&&b({type:N.E.PL_CLEAR_PICKUP_ERROR})},handleDimensionChange:(e,t,a)=>{z.current&&""===x.pickupRequest.location?z.current.focus():h({type:N.E.PL_FILTER_UPDATE,selection:{key:e,value:t},family:a})},handleStoreChange:(e,t)=>{x.pickupRequest.store=e,F(e),P({type:N.E.PL_SUGGESTIONS_REMOVE_STORE_SELECTION}),b({type:N.E.PL_PICKUP_UPDATE_STORE_SELECTION,selectedStore:e}),k({type:N.E.PL_SELECTION_UPDATE,prop:{store:e,part:t,isPickupSelected:!0,isDeliverySelected:!1,isSuggestionSelected:!1}})},handleDeliverySelection:e=>{b({type:N.E.PL_PICKUP_REMOVE_STORE_SELECTION}),P({type:N.E.PL_SUGGESTIONS_REMOVE_STORE_SELECTION}),k({type:N.E.PL_SELECTION_UPDATE,prop:{part:e,store:null,isPickupSelected:!1,isDeliverySelected:!0,isSuggestionSelected:!1}})},handleSuggestionsToggle:e=>{P({type:N.E.PL_SUGGESTIONS_SHOW_TOGGLE,part:e})},handleSuggestionStoreChange:(e,t)=>{b({type:N.E.PL_PICKUP_REMOVE_STORE_SELECTION}),P({type:N.E.PL_SUGGESTIONS_UPDATE_STORE_SELECTION,part:t,selectedStore:e}),k({type:N.E.PL_SELECTION_UPDATE,prop:{store:e,part:t,isDeliverySelected:!1,isSuggestionSelected:!0}})},handleSaveSelection:()=>{k({type:N.E.PL_SELECTION_UPDATE,prop:{submitSelection:!0}}),(0,M.l_)(A,t.family),l&&l({...A,submitSelection:!0}),c||j()},clearOverlayData:j}};var K=a(2662),Y=a(9283),W=a(2787),z=a(8595),Q=a(2096);const j="search",X=e=>{let{fieldData:t,form:a,inputProps:r,setOptions:l,onInputBlur:n,inputRef:c,resetA11yText:i,onReset:d=()=>{},errorMessage:u,errorMessageId:p=""}=e;return o().createElement(o().Fragment,null,o().createElement("label",{className:"visuallyhidden",htmlFor:j},t.searchPlaceholder),o().createElement(z.A,(0,s.A)({type:"text","data-autom":"zipCode",classes:{input:"rf-productlocator-form-textinput"},name:j,search:!0,placeholder:t.searchPlaceholder,autoComplete:"off",maxLength:t.maxlength,value:a.values[j],onChange:a.handleChange,error:a.touched[j]&&a.errors[j]||u?.isResponseError&&u?.message,onReset:()=>{a.setFieldValue(j,""),l([]),d()},resetA11y:i,ref:c},r,{onBlur:e=>{r?(r.onBlur?.(),n(r.value)):n(a.values[j]),a.handleBlur(e)},"aria-describedby":p})))},J=e=>{let{form:t,format:a={},inputRef:r,inputProps:s,...l}=e;const n=(0,K.Ay)({value:t.values[j]||"",format:a}),c=(0,S.E2)(r,n.ref),i={form:t,...l,inputRef:c,inputProps:{...s,onChange:e=>{const{value:a}=n.onChange(e);t.setFieldValue(j,a)},onPaste:n.handlePaste}};return o().createElement(X,i)},Z=e=>{const t=i().get(e,"fieldData.format");return t?o().createElement(J,(0,s.A)({},e,{format:t})):o().createElement(X,e)},ee=e=>{let{onSubmit:t,fieldData:a,consent:r,classes:s={},inputValue:l,onInputBlur:c,onInputChange:u,inputRef:p,errorMessage:m,errorMessageId:E}=e;const{initialValues:S,validationSchema:h}=o().useMemo((()=>((e,t)=>{const a={[j]:t||""},r={};return r[j]=[],r[j].push({type:"required",message:e.requiredError}),e.isAutoCompleteEnabled||r[j].push({type:"regEx",regEx:e.required?[e.pattern]:[e.pattern,"^$"],message:e.invalidFormatError}),{initialValues:a,validationSchema:r}})(a,l)),[a,l]),{isTypeAheadEnabled:y}=a,[f,v]=o().useState([]),[N,T]=(e=>{let{autoCompleteChoiceAllyTxt:t,autoCompleteChoicesAllyTxt:a}=e;const[r,s]=o().useState(""),l=o().useRef();return o().useEffect((()=>{r&&(l.current&&clearTimeout(l.current),l.current=setTimeout((()=>{s()}),400))}),[r]),[r,e=>{1===e&&t&&s(`${e} ${t}`),(0===e||e>1&&a)&&s(`${e} ${a}`),r&&s("")}]})(a),L=(0,W.A)({initialValues:S,validationSchema:h,onSubmit:t}),{consent:R,handleChange:P,showConsent:A}=r.cookieCompliance||{};o().useEffect((()=>{L.resetForm(),L.setValues(S)}),[S]);const k=()=>{p.current&&p.current.focus(),u("")},{assets:O}=(0,g.S)(),I=i().get(O,"reset");return o().createElement("div",{className:n()("rf-productlocator-search",s.search)},o().createElement("form",{className:"rf-productlocator-form",onSubmit:L.handleSubmit},o().createElement("div",{className:"field-wrapper",key:j},y?o().createElement(o().Fragment,null,o().createElement(Y.A,{value:L.values[j],onChange:e=>{const t=(0,b.tG)("productlocator/SearchForm/onChange");L.setFieldValue(j,e.target.value),u(e.target.value),e.target?.value?.length>=a.typeAheadMinChars&&(0,_.q)((0,C.bj)(a.typeAheadUrl,{value:e.target.value})).then((e=>{const t=i().get(e,"body.suggestions",[]).map((e=>e.displayValue));v(t),T(t.length)})).catch((e=>t.error(e)))},onSelection:e=>{L.setFieldValue(j,e),t({[j]:e})},options:f,getPosition:e=>e?{width:e.width,left:0}:{},renderInput:e=>{const t={...e,ref:p};return o().createElement(Z,{fieldData:a,form:L,inputProps:t,onReset:k,setOptions:v,onInputBlur:c,resetA11yText:I,errorMessage:m,errorMessageId:E})},classes:{root:"rf-productlocator-autocomplete"}}),o().createElement(d.Ay,{inline:!0,message:N})):o().createElement(Z,{fieldData:a,form:L,setOptions:v,onInputBlur:c,inputRef:p,onReset:k,resetA11yText:I,errorMessage:m,errorMessageId:E})),A?o().createElement(Q.f,{consent:R,handleChange:P,label:r.label}):null))};ee.displayName="SearchForm";const te=ee;var ae=a(4067),re=a(4175);const oe=e=>{let{localityLookupUrl:t,onSubmit:a,consent:r,inputValue:s,inputRef:l}=e;const[n,c]=o().useState({}),[d,u]=o().useState({}),[p,m]=o().useState(!1),{consent:E,handleChange:g,showConsent:S}=r.cookieCompliance||{};o().useEffect((()=>{m(!0),(0,_.q)(t).then((e=>{const t=i().get(e,"body");c(t);const a=t?.localityLookupFields.reduce(((e,a)=>({...e,...t[a],name:a})),{});u(a)})).finally((()=>{m(!1)}))}),[t]);const{initialValues:h,validationSchema:y}=o().useMemo((()=>((e,t)=>{const a={},r={},o=i().get(e,"localityLookupFields",[]);return e&&o.forEach((o=>{const s=e[o];"select"===s.type&&s.data&&(a[o]=t||s.data.reduce(((e,t)=>"true"===t.selected?t.value:e),"")),r[o]=[],s.required&&r[o].push({type:"required",message:s.requiredErrorMsg})})),{initialValues:a,validationSchema:r}})(n,s)),[n,s]);o().useEffect((()=>{f.resetForm(),f.setValues(h)}),[h]);const f=(0,W.A)({initialValues:h,validationSchema:y,onSubmit:a}),{name:v,data:b={},type:C,required:N}=d,T=b[0]?.text;return o().createElement("div",{className:"rc-location-field-wrapper"},!p&&d&&"select"===C&&o().createElement(ae.A,{key:v,name:v,value:f.values[v],classes:{error:"rc-location-form-errormsg"},onBlur:f.handleBlur,onChange:e=>{f.handleChange(e),a({search:e.target.value})},label:T,disabled:b.length<=1,required:N,error:b.length>1&&f.touched[v]&&f.errors[v],ref:l},b.map((e=>o().createElement(ae.n,{value:e.value,key:e.value,disabled:e.isDisabled},e.text)))),p&&o().createElement(re.A,{className:"rf-cityselector-spinner"}),S?o().createElement(Q.f,{consent:E,handleChange:g,label:r.label}):null)};var se=a(9904);const le=e=>{let{onSubmit:t,fieldData:a,consent:r,classes:s={},inputRef:l,pickup:c,localLocation:d,onLocalLocationChange:p}=e;const[m,E]=o().useState(!1),g=o().useRef(!1),{showingOptionsLabel:S,selectLocationLabel:h,provinceSelectorTabs:y,addressLookupUrl:f}=a,v=o().useMemo((()=>{const e=y.map((e=>e.name));return y.map(((t,a)=>({...t,update:e.slice(a+1)})))}),[y]),b=(0,se.vH)({addressLookupUrl:f,provinceSelectorTabs:v,onChange:(e,a)=>{E(!1),t({search:e}),p(i().get(a,"city"))}}),{consent:_,handleChange:C,showConsent:N}=r.cookieCompliance||{},T=(e=>i().get(e,"data.locationLabel")||i().get(e,"data.location"))(c),L=d||T;return o().createElement("div",{className:n()("rf-productlocator-search-province-selector",s.search)},o().createElement("form",{className:"rf-productlocator-province-selector-form"},L&&o().createElement("span",null,S),o().createElement("button",{type:"button",className:n()(s.button,"rf-productlocator-province-selector-button","icon","icon-after",{"icon-chevrondown":!m,"icon-chevronup":m}),onClick:()=>{E(!m),m||(g.current=!0)},ref:l,"aria-expanded":m},L||h),o().createElement("div",{className:"column large-12 small-12"},o().createElement(u.x,{in:m},N?o().createElement(Q.f,{consent:_,handleChange:C,label:r.label}):null,g.current&&o().createElement(se.Ay,{activeTab:b.activeTab,onTabChange:b.setActiveTab,selection:b.selection,onSelection:b.handleSelection,tabs:b.tabs,desktopColumns:2,mobileColumns:2})))))};var ne=a(7634),ce=a(4768),ie=a(3471),de=a(632),ue=a(1345),pe=a(2547);const me=e=>{let{stores:t,handleStoreChange:a,legend:r=""}=e;return o().createElement("div",{className:"rf-productlocator-stores"},o().createElement(de.c6,{name:"store",legend:r},t&&t.map(((e,t)=>o().createElement(ie.Ay,(0,s.A)({key:e.storeNumber,name:`store-${e.partsAvailabilityNorm.partNumber}`,value:e.storeNumber,checked:e.selected,withAriaLabeledBy:!0,"data-pl-partnumber":e.partsAvailabilityNorm.partNumber,disabled:!i().get(e,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1)},(0,ue.b)((t=>a(t,e.partsAvailabilityNorm.partNumber)),!0),{className:n()("rf-productlocator-storeoption","form-selector-twocol-threeline",{disabled:!i().get(e,"partsAvailabilityNorm.messageTypes.regular.storeSelectionEnabled",!1)}),tagAttrs:{"data-autom":`pickupStore_${t}`},render:t=>{let{SelectorLabel:a}=t;return o().createElement(o().Fragment,null,o().createElement(a,null,o().createElement("span",{className:"row"},o().createElement("span",{className:n()("form-selector-left-col","column","large-6")},e.recommendationLabel&&o().createElement(pe.A,{text:e.recommendationLabel}),o().createElement(de._V,{text:e.address.address}),o().createElement(de.ED,{text:e.state?`${e.city}, ${e.state}`:e.city}),o().createElement(de.ED,{text:e.storeDistanceWithUnit})),o().createElement("span",{className:n()("form-selector-right-col","column","large-6")},o().createElement("span",(0,S.OH)(e.partsAvailabilityNorm.pickupSearchQuote)),e.partsAvailabilityNorm.pickupType&&o().createElement(de.ED,{text:e.partsAvailabilityNorm.pickupType})))),o().createElement(u.x,{in:e.selected},o().createElement("div",{className:"rf-productlocator-storedetails",id:`${e.partsAvailabilityNorm.partNumber}-${e.storeNumber}-details`},o().createElement("div",null,e.address&&o().createElement("span",(0,s.A)({className:"rf-productlocator-storename row"},(0,S.OH)(e.address.address2))),o().createElement("span",{className:"rf-productlocator-storeaddress row"},e.state?`${e.city}, ${e.state}`:`${e.city}`),e.address&&e.address.postalCode&&o().createElement("span",(0,s.A)({className:"row"},(0,S.OH)(e.address.postalCode))),e.storeHours&&o().createElement("div",{className:"rf-productlocator-storehours"},o().createElement("span",{className:"visuallyhidden"},e.storeHours.storeHoursText),e.storeHours.hours&&e.storeHours.hours.map((t=>o().createElement("div",{key:`${e.storeNumber}-${t.storeDays}`,className:"rf-productlocator-storehours-row"},o().createElement("span",(0,s.A)({className:"rf-productlocator-storehoursdays"},(0,S.OH)(t.storeDays))),o().createElement("span",(0,s.A)({className:"rf-productlocator-storehourstiming"},(0,S.OH)(t.storeTimings)))))),e.specialHours&&e.specialHours.specialHoursData&&o().createElement("div",{className:"rf-productlocator-specialhours"},o().createElement("p",(0,S.OH)(e.specialHours.specialHoursText)),e.specialHours.specialHoursData.map((t=>o().createElement("div",{key:`${e.storeNumber}-${t.specialDays}`,className:"rf-productlocator-storehours-row"},o().createElement("span",(0,s.A)({className:"rf-productlocator-storehoursdays"},(0,S.OH)(t.specialDays))),o().createElement("span",(0,s.A)({className:"rf-productlocator-storehourstiming"},(0,S.OH)(t.specialTimings))))))))),e.pickupOptionsDetails&&o().createElement("div",{className:n()("rf-productlocator-pickupdetails",ce.A.CAPTION)},o().createElement("div",(0,S.OH)(e.pickupOptionsDetails.whatToExpectAtPickup)),e.pickupOptionsDetails.pickupOptions&&e.pickupOptionsDetails.pickupOptions.map((e=>o().createElement("ul",{key:e.index,className:"form-selector-group"},o().createElement("li",{className:"rf-productlocator-pickupoption"},o().createElement("div",(0,S.OH)(e.pickupOptionTitle)),o().createElement("div",(0,S.OH)(e.pickupOptionDescription)))))),o().createElement("div",(0,S.OH)(e.pickupOptionsDetails.comparePickupOptionsLink))))))}}))))))};me.displayName=me;const Ee=me,ge=e=>{let{pickup:t,localLocation:a,handleStoreChange:r,assets:l,errorMessageId:c,displayErrorMsg:d}=e;const[p,m]=o().useState(!1),{pickupHeader:E,pickupHeaderNear:g,coldStartPickupInfo:h}=l,y=i().get(t,"data.stores"),f=i().get(t,"data.availability.isComingSoon",!1),v=a||(e=>i().get(e,"data.locationLabel")||i().get(e,"data.location"))(t),b=i().get(t,"data.legendLabelText",""),_=o().createElement("span",(0,s.A)({className:"visuallyhidden"},(0,S.OH)(b))),C=(d||!t?.error?.isResponseError)&&t?.error;return o().createElement(o().Fragment,null,o().createElement("div",{className:"rf-productlocator-pickuploctionheader"},v?o().createElement("h3",null,g," ",v):o().createElement("h3",null,E),t.isFetched?"":o().createElement("div",(0,s.A)({className:"rf-productlocator-pickupinfo"},(0,S.OH)(h)))),t.isFetched&&!f&&o().createElement("div",{className:n()("rf-productlocator-pickupstoreslist",{"rf-productlocator-nopickupstores":t.isToggleBtnNeeded,"rf-productlocator-togglebtn-open":p})},t.isToggleBtnNeeded?o().createElement(o().Fragment,null,o().createElement("button",{type:"button",className:n()("rf-productlocator-nopickupstores-btn as-buttonlink",ce.A.BODY_REDUCED),"aria-expanded":p,onClick:()=>m(!p)},o().createElement("span",(0,s.A)({className:"rf-productlocator-buttontitle"},(0,S.OH)(y&&y.length>1?t.data.notAvailableNearby:t.data.notAvailableNearOneStore))),o().createElement("span",{className:"icon icon-after icon-chevrondown rf-productlocator-buttonicon"})),o().createElement(u.x,{in:p,className:"rf-productlocator-notavailablestores"},o().createElement(Ee,{stores:y,handleStoreChange:r,legend:_}))):o().createElement(o().Fragment,null,C&&o().createElement("div",{className:n()("rf-productlocator-errormsg",ce.A.BODY_REDUCED,"is-error"),id:c},o().createElement(ne.A,{error:C.message})),o().createElement(Ee,{stores:y,handleStoreChange:r,legend:_}))),t.isFetched&&f&&o().createElement("div",(0,s.A)({className:"rf-productlocator-pickupinfo rf-productlocator-comingsoon"},(0,S.OH)(i().get(t,"data.availability.comingSoonText","")))))};ge.displayName="ProductLocatorPickup";const Se=ge,he=e=>{let{delivery:t,selected:a,handleDeliverySelection:r}=e;const{deliveryQuoteNorm:s,partNorm:l}=t,c=i().get(s,"compact.label",""),d=i().get(s,"compact.quote",""),u=i().get(s,"compact.isBuyable",!0);return s?o().createElement("div",{className:"rf-productlocator-deliveryquotes"},o().createElement(de.gW,{name:"delivery",value:l,checked:a,tag:"div",disabled:!u,className:"rf-productlocator-deliveryquotesoption",skipChangeSelection:!0,handleChange:r,render:e=>{let{SelectorLabel:t}=e;return o().createElement(t,null,o().createElement("span",{className:"row"},o().createElement("span",{className:n()("form-selector-left-col","column","large-12","small-12")},o().createElement(de._V,{text:`${c} ${d}`}))))}})):""};he.displayName="Delivery";const ye=he,fe=e=>{let{suggestions:t,assets:a,handleSuggestionStoreChange:r,handleSuggestionsToggle:l}=e;const{recommendationSectionHeader:c,errorMessage:d}=a,{isFetching:p,isFetched:g,data:h,error:y}=t,f=i().get(h,"products",[]),v=0===f.length;return o().createElement("div",{className:"rf-productlocator-suggestions"},o().createElement("div",{className:"rf-productlocator-suggestionheader"},o().createElement("h3",null,c)),p&&o().createElement("div",{className:"column large-12 small-12 rf-pickupinfo-spinner"},o().createElement("div",{role:"img",tabIndex:-1,className:"waitindicator waitindicator40-blue","aria-label":"loading"})),g&&o().createElement("div",{className:n()("rf-productlocator-suggestionitems",{"rf-productlocator-nosuggestionitems":v})},y&&o().createElement("div",(0,s.A)({className:"rf-productlocator-errormsg is-error"},(0,S.OH)(y.isResponseError?y.message:d))),v?o().createElement("div",(0,s.A)({className:n()("rf-productlocator-suggestions-nosimilarmodels",E.A.BODY_REDUCED)},(0,S.OH)(h.noSimilarModelsText))):f.map(((e,t)=>{const{part:a,productImage:c,productTitle:i,displayPrice:d,availabilityText:p,stores:g}=e;return o().createElement("div",{className:"rf-productlocator-suggestionitem","data-autom":`productLocator-suggestions-${t}`,key:a},o().createElement("div",{className:"rf-productlocator-productdetails"},c&&o().createElement("div",{className:"rf-productlocator-productimg"},o().createElement(m.Ay,{data:c,className:"ir"})),o().createElement("div",{className:n()("rf-productlocator-productinfo",{"rf-productlocator-togglebtn-open":e.isShown})},o().createElement("button",{type:"button",className:"rf-productlocator-suggestionstogglebtn",onClick:()=>l(a),"aria-expanded":e.isShown},o().createElement("span",{className:"rf-productlocator-togglebtn-content"},o().createElement("span",{className:E.A.BODY_REDUCED},i),o().createElement("span",(0,s.A)({className:n()("rf-productlocator-productprice",E.A.BODY_REDUCED)},(0,S.OH)(d))),o().createElement("span",{className:n()("rf-productlocator-suggestionsstoresbtn",E.A.CAPTION)},p)),o().createElement("span",{className:"icon icon-after icon-chevrondown rf-productlocator-buttonicon"})))),o().createElement(u.A,{in:e.isShown,className:"rf-productlocator-suggestionoptions"},o().createElement(Ee,{stores:g,handleStoreChange:e=>r(e,a)})))}))))};fe.displayName="Suggestions";const ve=fe,be=e=>{let{state:t,onDimensionChange:a}=e;const r=i().get(t,"dimensions",[]),l=e=>e.showDisabledSelectors?e.options:e.options.filter((e=>e.enabled));return r.map((e=>"carrierModel"!==e.key&&o().createElement("div",{key:e.key,className:"rf-productlocator-filter-dimension"},e.header&&o().createElement("h3",(0,s.A)({className:n()("rf-productlocator-filter-dimensionlabel",ce.A.BODY)},(0,S.OH)(e.header))),"dimensionColor"===e.key?o().createElement(de.ee,{name:"color",legend:o().createElement("span",(0,s.A)({className:"visuallyhidden"},(0,S.OH)(e.header))),className:"rf-productlocator-filter-dimensiongroup"},l(e).map((t=>{const r={...t.image,alt:""};return o().createElement(de.aU,{key:t.value,name:"pl_dimensionColor",value:t.value,checked:t.selected,image:r,text:t.label,isProductRed:"product_red"===t.value,skipChangeSelection:!0,handleChange:t=>a(e.key,t)})}))):o().createElement(de.c6,{name:e.key,legend:o().createElement("span",(0,s.A)({className:"visuallyhidden"},(0,S.OH)("dimensionConnection"!==e.key?e.header:i().get(e,"modelMessage")))),className:"rf-productlocator-filter-dimensiongroup",withGutters:!0},l(e).map((t=>o().createElement(de.sY,{key:t.value,name:`pl_${e.key}`,value:t.value,checked:t.selected,disabled:!t.enabled,className:"rf-productlocator-filteroption",itemsPerRowLarge:"dimensionConnection"===e.key||"dimensionPackQty"===e.key||"dimensionScreensize"===e.key||"dimensionFinish"===e.key||"dimensionStandType"===e.key?1:3===l(e).length?3:2,itemsPerRowSmall:"dimensionConnection"===e.key||"dimensionPackQty"===e.key||"dimensionScreensize"===e.key||"dimensionFinish"===e.key||"dimensionStandType"===e.key?1:2,skipChangeSelection:!0,handleChange:t=>a(e.key,t)},o().createElement(de.ED,{text:t.label}))))))))};var _e=a(1644);const Ce=e=>{let{bootstrap:t,part:a,storeId:r,visible:l,consent:c,isSaving:f,onClose:v,onStoreChange:b,cppart:_,onMetaInitialized:C=i().noop,preventClearOnSave:N,stickyTrigger:T=!1,tia:L,tt:R,pid:P}=e;const A=$({bootstrap:t,visible:l,part:a,storeId:r,onStoreChange:b,cppart:_,preventClearOnSave:N,stickyTrigger:T,tia:L,tt:R,pid:P}),{viewport:k}=(0,g.S)(),[O,I]=o().useState(!1),[D,F]=o().useState(!1),[U,H]=(0,d.oM)({resetDelay:4e3}),G=o().useId(),{metaState:w={},filterState:x={},pickupState:q,deliveryState:V,suggestionsState:B,selectionState:K={},inputRef:Y,localLocation:W,setLocalLocation:z,handleDimensionChange:Q,handleStoreChange:j,handleDeliverySelection:X,handleFormSubmit:J,handleInputBlur:Z,handleInputChange:ee,handleSuggestionsToggle:ae,handleSuggestionStoreChange:re,handleSaveSelection:se,clearOverlayData:ne}=A,ce="small"===k,{data:ie={},error:de,isFetched:ue,isFetching:pe}=w,{overlayHeader:me,search:Ee,assets:ge={}}=ie,{deliveryHeader:he,storeSelectionButtonText:fe,filterButtonText:Ce,footer:Ne}=ge,Te=i().get(q,"data.location"),{displayPrice:Le,productImage:Re,product:Pe={}}=x,{part:Ae,isPickupSelected:ke,isDeliverySelected:Oe,isSuggestionSelected:Ie}=K,De=Boolean(Ae)&&(ke||Oe||Ie),Fe=ie.displayableDimensionVariants,Ue=Fe?.size,He=Fe?.items?.some((e=>"dimensionColor"===e.value)),{provinceSelector:Me,showingOptionsLabel:Ge,selectLocationLabel:we,provinceSelectorTabs:xe,addressLookupUrl:qe,resultVoText:Ve,resultsVoText:Be,enableCitySelector:$e,localityLookupUrl:Ke}=t,Ye=i().get(q,"data.stores",[]),We=i().get(q,"isFetched",""),ze=i().get(q,"data.availability.isComingSoon",!1),Qe=Ye.length,je=1===Qe?Ve:Be;return o().useEffect((()=>{ue&&C()}),[ue,de,C]),o().useEffect((()=>{H(`${Qe} ${je}`)}),[We]),o().createElement(p.A,{visible:l,classes:{root:"rf-productlocator-overlay",content:n()("rf-productlocator-overlay-content",{"rf-productlocator-overlay-comingsoon":ze})},fixedWidth:!0,noPadding:!0,appear:!0,"data-autom":"plOverlayContainer",ariaLabel:"rf-productlocator-overlay-label",onClose:()=>{v(),(0,M.kE)(),ne(),F(!1)},onEntered:()=>I(!0),onExited:()=>I(!1)},pe&&o().createElement(_e.A,{ariaLabel:"loading",entered:O}),We&&Qe>0&&o().createElement(d.Ay,{message:U}),ue&&(i().isEmpty(de)?o().createElement(h.Og,null,o().createElement("div",{className:"rf-productlocator-header"},o().createElement("h2",(0,s.A)({id:"rf-productlocator-overlay-label"},(0,S.OH)(me))),l&&ce&&Ee&&o().createElement(o().Fragment,null,Me&&o().createElement(le,{fieldData:{showingOptionsLabel:Ge,selectLocationLabel:we,provinceSelectorTabs:xe,addressLookupUrl:qe},onSubmit:J,onInputBlur:Z,pickup:q,inputRef:Y,consent:c,localLocation:W,onLocalLocationChange:z}),$e&&o().createElement(oe,{localityLookupUrl:Ke,onSubmit:J,consent:c,inputValue:Te,inputRef:Y}),!Me&&!$e&&o().createElement(te,{fieldData:Ee,onSubmit:J,onInputBlur:Z,onInputChange:ee,inputValue:Te,inputRef:Y,consent:c,errorMessage:q?.error,errorMessageId:G}))),o().createElement("div",{className:"row"},o().createElement("div",{className:n()("column","large-5","small-12","rf-productlocator-column",{"rf-productlocator-hidedimensions":1===Ue&&He,"rf-productlocator-filter-scrollable":!ce})},l&&!ce&&Ee&&o().createElement(o().Fragment,null,Me&&o().createElement(le,{fieldData:{showingOptionsLabel:Ge,selectLocationLabel:we,provinceSelectorTabs:xe,addressLookupUrl:qe},onSubmit:J,onInputBlur:Z,pickup:q,inputRef:Y,consent:c,localLocation:W,onLocalLocationChange:z,classes:{search:"column large-12 small-12"}}),$e&&o().createElement(oe,{localityLookupUrl:Ke,onSubmit:J,consent:c,inputValue:Te}),!Me&&!$e&&o().createElement(te,{fieldData:Ee,onSubmit:J,onInputBlur:Z,onInputChange:ee,inputValue:Te,inputRef:Y,consent:c,errorMessage:q?.error,errorMessageId:G})),o().createElement("div",{className:n()("rf-productlocator-filter",E.A.CAPTION)},ce?o().createElement(o().Fragment,null,o().createElement("button",{type:"button",className:n()("rf-productlocator-filterbtn",E.A.BODY_REDUCED,{"rf-productlocator-filterbtn-show":D}),onClick:()=>{F(!D),(0,M.Ux)(!D)},"aria-expanded":D},o().createElement("span",{className:"icon icon-after icon-chevrondown"},Ce)),o().createElement(u.A,{in:D,className:"rf-productlocator-filtercontent"},o().createElement(be,{state:x,onDimensionChange:(e,a)=>Q(e,a,t.family)}))):o().createElement(be,{state:x,onDimensionChange:(e,a)=>Q(e,a,t.family)}))),o().createElement("div",{className:n()("column","large-7","small-12","rf-productlocator-column",{"rf-productlocator-ispickuploading":q.isFetching})},o().createElement("div",{className:n()("rf-productlocator-options",{"rf-productlocator-scrollable":!ce})},q.isFetching&&o().createElement(_e.A,{ariaLabel:"loading",entered:O}),o().createElement("div",{className:"rf-productlocator-main"},o().createElement("div",{className:"rf-productlocator-productdetails"},o().createElement("div",{className:"rf-productlocator-productimg"},Re&&o().createElement(m.Ay,{data:Re})),o().createElement("div",{className:"rf-productlocator-productinfo"},o().createElement("div",{className:E.A.BODY_TIGHT},Pe.productTitle),o().createElement("div",(0,s.A)({className:n()("rf-productlocator-productprice",E.A.BODY)},(0,S.OH)(Le))))),o().createElement("div",{className:"rf-productlocator-maincontent"},!q.isFetching&&o().createElement(o().Fragment,null,o().createElement(Se,{pickup:q,localLocation:W,handleStoreChange:j,assets:ge,errorMessageId:G,displayErrorMsg:Me||$e}),V.data&&!V.error&&o().createElement(o().Fragment,null,o().createElement("div",{className:"rf-productlocator-deliveryheader"},o().createElement("h3",null,he)),o().createElement(ye,{delivery:V.data,handleDeliverySelection:X,selected:K.isDeliverySelected}))))),q.isToggleBtnNeeded&&!ze&&o().createElement(ve,{suggestions:B,handleSuggestionsToggle:ae,handleSuggestionStoreChange:re,assets:ge}),o().createElement(h.An.Consumer,null,(e=>o().createElement(o().Fragment,null,!i().isEmpty(e)&&o().createElement(h.gn,{className:"rc-overlay-footnotes rf-productlocator-footnotes",footnotes:e}),Ne&&i().isEmpty(e)&&o().createElement("div",(0,s.A)({className:n()("rf-productlocator-footer",E.A.CAPTION)},(0,S.OH)(Ne))))))),o().createElement("div",{className:"rf-productlocator-sticky"},o().createElement(y.A,{className:n()("button","button-block","rf-productlocator-selectstorebtn",{disabled:!De}),"data-autom":"continuePickUp",disabled:!De||f,type:"button",isLoading:f,onClick:()=>se()},fe))))):o().createElement("div",(0,s.A)({className:"rf-productlocator-error"},(0,S.OH)(de.message)))))},Ne=e=>{let{bootstrap:t,...a}=e;return t&&t.metaURL?o().createElement(Ce,(0,s.A)({bootstrap:t},a)):null};Ne.displayName="ProductLocatorOverlay";const Te=Ne,Le=e=>{let{bootstrap:t,fulfillment:a,cppart:r,tia:s,tt:l,pid:n}=e;return o().createElement(Te,{visible:a.overlayApuVisible,part:a.overlayPart,storeId:a.storeId||a.geoStoreId,bootstrap:t,consent:a.consent,cppart:r,onClose:a.handleCloseApuOverlay,onStoreChange:a.handleStoreSelection,stickyTrigger:a.stickyTrigger,tia:s,tt:l,pid:n})}},7634:(e,t,a)=>{a.d(t,{A:()=>r.A});var r=a(2370)}}]);