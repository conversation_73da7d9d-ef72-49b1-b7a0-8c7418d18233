{"head": {"status": "200"}, "body": {"content": {"pickupMessage": {"stores": [{"storeEmail": "<EMAIL>", "storeName": "昆明", "reservationUrl": "http://www.apple.com/cn/retail/kunming", "makeReservationUrl": "http://www.apple.com/cn/retail/kunming", "state": "云南", "storeImageUrl": "https://rtlimages.apple.com/cmc/dieter/store/4_3/R670.png?resize=828:*&output-format=jpg", "country": "CN", "city": "昆明", "storeNumber": "R670", "partsAvailability": {"MYEV3CH/A": {"storePickEligible": true, "pickupSearchQuote": "明天可取货", "partNumber": "MYEV3CH/A", "pickupDisplay": "available", "pickupType": "店内取货", "messageTypes": {"sticky": {"storeSearchEnabled": true, "storePickupLabel": "立即订购。取货 (店内)：", "storeSelectionEnabled": true, "storePickupQuote": "<button type=\"button\" class=\"rf-pickup-quote-overlay-trigger as-retailavailabilitytrigger-infobutton retail-availability-search-trigger as-buttonlink as-buttonlink-inline\" data-ase-overlay=\"buac-overlay\" data-ase-click=\"show\">Apple 昆明</button><span class=\"as-pickup-quote-availability-quote\">明天</span>取货", "storePickupLinkText": "查看其他零售店", "storePickupProductTitle": "iPhone 16 128GB 黑色"}, "regular": {"storeSearchEnabled": true, "storePickupLabel": "立即订购。取货 (店内)：", "storeSelectionEnabled": true, "storePickupQuote": "明天；Apple 昆明", "storePickupQuote2_0": "<span class=\"as-pickup-quote-availability-quote\">明天</span>; <button type=\"button\" class=\"rf-pickup-quote-overlay-trigger as-retailavailabilitytrigger-infobutton retail-availability-search-trigger as-buttonlink as-buttonlink-inline\" data-ase-overlay=\"buac-overlay\" data-ase-click=\"show\">Apple 昆明</button>", "storePickupLinkText": "查看其他零售店", "storePickupProductTitle": "iPhone 16 128GB 黑色"}}, "buyability": {"isBuyable": true, "reason": "OK", "commitCodeId": 0, "inventory": 1}}}, "phoneNumber": "4006393602", "pickupTypeAvailabilityText": "此地点提供店内取货服务。", "address": {"address": "Apple 昆明", "address3": "顺城购物中心", "address2": "昆明市五华区东风西路 11 号 ", "postalCode": "650031"}, "hoursUrl": "http://www.apple.com/cn/retail/kunming", "storeHours": {"storeHoursText": "Store Hours", "bopisPickupDays": "Days", "bopisPickupHours": "Hours", "hours": [{"storeTimings": "10:00 - 22:30", "storeDays": "周五-周六:"}, {"storeTimings": "10:00 - 22:00", "storeDays": "周一-周四, 周日:"}]}, "pickupEncodedUpperDateString": "20250929", "storelatitude": 25.0389880510382, "storelongitude": 102.************, "storedistance": 0.0, "storeDistanceVoText": "null from 650031", "retailStore": {"storeNumber": "R670", "storeUniqueId": "R670", "name": "昆明", "storeTypeKey": "1", "storeSubTypeKey": "0", "storeType": "APPLESTORE_DEFAULT", "phoneNumber": "4006393602", "email": "<EMAIL>", "latitude": 25.0389880510382, "longitude": 102.************, "address": {"companyName": "Apple 昆明", "street": "昆明市五华区东风西路 11 号 ", "street2": "顺城购物中心", "city": "昆明", "district": "五华区", "postalCode": "650031", "state": "云南", "countryCode": "CN", "daytimePhone": "4006393602", "uuid": "9d0d6384-93f9-430c-976e-efd2ece07d6f", "languageCode": "zh-cn", "type": "SHIPPING", "primaryAddress": false, "businessAddress": false, "verificationState": "UN_VERIFIED"}, "storeImageUrl": "http://rtlimages.apple.com/cmc/dieter/store/4_3/R670.png?resize=828:*&output-format=jpg", "makeReservationUrl": "http://www.apple.com/cn/retail/kunming", "hoursAndInfoUrl": "http://www.apple.com/cn/retail/kunming", "storeHours": [{"storeDays": "周五-周六", "storeTimings": "10:00 - 22:30 "}, {"storeDays": "周一-周四, 周日", "storeTimings": "10:00 - 22:00 "}], "secureStoreImageUrl": "https://rtlimages.apple.com/cmc/dieter/store/4_3/R670.png?resize=828:*&output-format=jpg", "distance": 0.0, "distanceUnit": "KM", "timezone": "Asia/Shanghai", "storeIsActive": true, "lastUpdated": 0.0, "lastFetched": 1759066585025, "dateStamp": "28-Sep-2025", "distanceSeparator": ".", "storeHolidayLookAheadWindow": 0, "storePickupMethodByType": {"INSTORE": {"type": "INSTORE", "services": ["APU"], "typeCoordinate": {"lat": 25.0389880510382, "lon": 102.************}}}, "availableNow": true}, "storelistnumber": 1, "storeListNumber": 1, "pickupOptionsDetails": {"whatToExpectAtPickup": "<h4 class=\"as-pickupmethods-intro-header\">取货须知</h4><br />当你的订单准备就绪后，我们会向你发送详细的取货说明电子邮件。有关新设备设置，你可以预约免费在线辅导，让 Specialist 专家为你提供指导。", "comparePickupOptionsLink": "<a href=\"https://www.apple.com.cn/shop/shipping-pickup\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: shop/shipping-pickup\" target=\"_blank\">进一步了解送货<br />和取货<span class=\"icon icon-after icon-external\"></span><span class=\"visuallyhidden\">(在新窗口中打开)</span></a>", "pickupOptions": [{"pickupOptionTitle": "店内", "pickupOptionDescription": "提取你的在线订单商品。你可以获得设置帮助，还能选购配件。", "index": 1}]}, "rank": 1}], "overlayInitiatedFromWarmStart": true, "viewMoreHoursLinkText": "查看更多时段", "storesCount": "1", "little": false, "pickupLocationLabel": "你的 Apple Store 零售店：", "pickupLocation": "Apple 昆明", "notAvailableNearby": "距离最近的 [X] 家零售店今日无货。", "notAvailableNearOneStore": "距离最近的零售店今天不可取货。", "warmDudeWithAPU": true, "viewMoreHoursVoText": "(在新窗口中打开)", "availability": {"isComingSoon": false}, "viewDetailsText": "查看详情", "availabilityStores": "R670", "legendLabelText": "门店", "filteredTopStore": false}, "deliveryMessage": {"geoLocated": false, "availableOptionsText": "Available Options", "geoEnabled": true, "dudeCookieSet": true, "MYEV3CH/A": {"sticky": {"orderByDeliveryBy": "今天订购。", "orderByDeliveryBySuffix": "送货至<button class=\"rf-dude-quote-overlay-trigger as-delivery-overlay-trigger as-purchaseinfo-dudetrigger as-buttonlink as-buttonlink-inline\" data-autom=\"deliveryDateChecker\" data-ase-overlay=\"dude-overlay\" data-ase-click=\"show\">五华区</button>，预计送达日期：", "deliveryOptionMessages": [{"displayName": "明天 — 免费", "inHomeSetup": "false", "encodedUpperDateString": "20250929"}], "deliveryOptions": [{"displayName": "标准送货", "date": "明天", "shippingCost": "免费"}, {"displayName": "定时送货，下午 03:00 - 下午 05:00", "date": "明天", "shippingCost": "免费"}, {"displayName": "定时送货，下午 05:00 - 下午 07:00", "date": "明天", "shippingCost": "免费"}, {"displayName": "定时送货，下午 07:00 - 下午 09:00", "date": "明天", "shippingCost": "免费"}], "deliveryOptionsLink": {"text": "五华区 的可用送货选项", "newTab": false, "isLinkAsButton": false}, "address": {"postalCode": "650031", "state": "云南", "city": "昆明", "district": "五华区"}, "showDeliveryOptionsLink": false, "messageType": "Delivery", "basePartNumber": "MYEV3", "commitCodeId": "0", "dudeAttributes": {"source": {"cutoffFormat": "actualTime", "leadByPickup": "true", "templateId": "DUDE_APU_N", "deliveryOrderSortBy": "speed"}, "templateID": "DUDE_APU_N", "resolvedLabel": "今天订购。", "shipMethodsDisplayOrder": ["A8"], "leadByPickup": "true"}, "subHeader": "iPhone 16 128GB 黑色", "stickyMessageIDL": "不提供 3 小时快送服务", "stickyMessageSTH": "<span class=\"bold\">明天</span> (免费)", "stickyMessageDeliveryOptions": "送货至：<button class=\"rf-dude-quote-overlay-trigger as-delivery-overlay-trigger as-purchaseinfo-dudetrigger as-buttonlink as-buttonlink-inline\" data-autom=\"deliveryDateChecker\" data-ase-overlay=\"dude-overlay\" data-ase-click=\"show\">五华区<span class=\"visuallyhidden\">(了解送货日期)</span></button>", "buyability": {"reason": "OK", "commitCode": "0", "isBuyable": true}, "idl": false, "defaultLocationEnabled": false, "inHomeSetup": false, "isElectronic": false, "isBuyable": true}, "regular": {"orderByDeliveryBy": "今天订购。", "orderByDeliveryBySuffix": "送货至<button class=\"rf-dude-quote-overlay-trigger as-delivery-overlay-trigger as-purchaseinfo-dudetrigger as-buttonlink as-buttonlink-inline\" data-autom=\"deliveryDateChecker\" data-ase-overlay=\"dude-overlay\" data-ase-click=\"show\">五华区</button>，预计送达日期：", "deliveryOptionMessages": [{"displayName": "明天 — 免费", "inHomeSetup": "false", "encodedUpperDateString": "20250929"}], "deliveryOptions": [{"displayName": "标准送货", "date": "明天", "shippingCost": "免费"}, {"displayName": "定时送货，下午 03:00 - 下午 05:00", "date": "明天", "shippingCost": "免费"}, {"displayName": "定时送货，下午 05:00 - 下午 07:00", "date": "明天", "shippingCost": "免费"}, {"displayName": "定时送货，下午 07:00 - 下午 09:00", "date": "明天", "shippingCost": "免费"}], "deliveryOptionsLink": {"text": "五华区 的可用送货选项", "newTab": false, "isLinkAsButton": false}, "address": {"postalCode": "650031", "state": "云南", "city": "昆明", "district": "五华区"}, "showDeliveryOptionsLink": false, "messageType": "Delivery", "basePartNumber": "MYEV3", "commitCodeId": "0", "dudeAttributes": {"source": {"cutoffFormat": "actualTime", "leadByPickup": "true", "templateId": "DUDE_APU_N", "deliveryOrderSortBy": "speed"}, "templateID": "DUDE_APU_N", "resolvedLabel": "今天订购。", "shipMethodsDisplayOrder": ["A8"], "leadByPickup": "true"}, "subHeader": "iPhone 16 128GB 黑色", "stickyMessageIDL": "不提供 3 小时快送服务", "stickyMessageSTH": "<span class=\"bold\">明天</span> (免费)", "stickyMessageDeliveryOptions": "送货至：<button class=\"rf-dude-quote-overlay-trigger as-delivery-overlay-trigger as-purchaseinfo-dudetrigger as-buttonlink as-buttonlink-inline\" data-autom=\"deliveryDateChecker\" data-ase-overlay=\"dude-overlay\" data-ase-click=\"show\">五华区<span class=\"visuallyhidden\">(了解送货日期)</span></button>", "buyability": {"reason": "OK", "commitCode": "0", "isBuyable": true}, "idl": false, "defaultLocationEnabled": false, "inHomeSetup": false, "isElectronic": false, "isBuyable": true}}, "dudeLocated": true, "locationCookieValueFoundForThisCountry": false, "accessibilityDeliveryOptions": "送货选项"}}, "footnotes": {"nextAvailableNumber": 1}}}