.rf-attach-section-top {
    padding: 95px 0 68px;
}

.cn .colornav-logo-productred {
    display: none;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro Text', 'SF Pro Icons', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
    color: #1d1d1f;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(en-IN) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro IN', 'SF Pro Text', 'SF Pro Icons', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(ar) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro AR', 'SF Pro Gulf', 'SF Pro Text', 'SF Pro Icons', 'AOS Icons', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(zh-CN) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro SC', 'SF Pro Text', 'SF Pro Icons', 'AOS Icons', 'PingFang SC', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(zh-TW) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro TC', 'SF Pro Text', 'SF Pro Icons', 'AOS Icons', 'PingFang TC', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(ko) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro KR', 'SF Pro Text', 'SF Pro Icons', 'AOS Icons', 'Apple Gothic', 'HY Gulim', 'MalgunGothic', 'HY Dotum', 'Lexi Gulim', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(ja) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro JP', 'SF Pro Text', 'SF Pro Icons', 'AOS Icons', 'Hiragino Kaku Gothic Pro', 'ヒラギノ角ゴ Pro W3', 'メイリオ', 'Meiryo', 'ＭＳ Ｐゴシック', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(th) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro TH', 'SF Pro Text', 'SF Pro Icons', 'AOS Icons', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}


.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(zh-MO),
.rf-accessory-tile-engraving .rf-engraving-item .rf-engraving-message:lang(zh-HK) {
    font-family: 'Apple Monochrome Emoji Ind', 'SF Pro HK', 'SF Pro Text', 'SF Pro Icons', 'AOS Icons', 'PingFang HK', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

.rf-engraving-font1:lang(en-IN),
.rf-engraving-singleline .form-textbox-input:lang(en-IN) {
    font-family: SF Pro IN,SF Pro Text,SF Pro Icons,AOS Icons,Helvetica Neue,Helvetica,Arial,sans-serif;
}

.rf-engraving-singleline .form-textbox-input:lang(en-IN) {
    height: 64px;
    padding-top: 17px;
 }

 .rf-engraving-singleline .form-textbox-input~.form-textbox-label:lang(en-IN),
 .rf-engraving-singleline .form-textbox-righticon:lang(en-IN) {
    padding-top: 4px;
}

.rf-engraving-singleline .form-textbox-input.focused~.form-textbox-label:lang(en-IN),
.rf-engraving-singleline .form-textbox-input.form-textarea-with-placeholder~.form-textbox-label:lang(en-IN),
.rf-engraving-singleline .form-textbox-input.form-textbox-entered~.form-textbox-label:lang(en-IN),
.rf-engraving-singleline .form-textbox-input:focus~.form-textbox-label:lang(en-IN),
.rf-engraving-singleline .form-textbox-input:valid[required]~.form-textbox-label:lang(en-IN),
.rf-engraving-singleline .form-textbox-input[placeholder]~.form-textbox-label:lang(en-IN) {
    padding-top: 0;
}

.rc-installments {
    margin-bottom: 9px;
}

.rc-installments > a {
    display: block;
}

.rc-installments > br {
    display: none;
}

.rf-attach-noncategorized .rf-attach-accessory .rc-prices {
    margin-bottom: 0;
}

.rf-attach-noncategorized .rf-attach-accessory .rc-prices-accessories {
    margin-bottom: 9px;
}
