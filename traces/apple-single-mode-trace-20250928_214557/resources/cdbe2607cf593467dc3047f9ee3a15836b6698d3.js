/*! 2.31.10 | BH: faa8f8d0d31a4460cfad | CH: c3d580d852 */
/*! License information is available at licenses.txt */"use strict";(globalThis.webpackChunkrs_iphone=globalThis.webpackChunkrs_iphone||[]).push([[8851],{1434:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(1594),o=r.n(n),a=r(2224),s=r.n(a),i=r(7335);const c=e=>{let{addressLookupUrl:t,provinceSelectorTabs:r,onChange:n=()=>{}}=e;const[a,c]=o().useState(0),[l,u]=o().useState([]),[p,d]=o().useState({}),m=l.filter((e=>e.options&&e.options.length>0));o().useEffect((()=>{(0,i.Fd)(t).then((e=>{return t=e,r.reduce(((e,r)=>{const n=s().get(t,`[${r.name}].data`);return n?[...e,{...r,options:n}]:e}),[]);var t})).then((e=>u(e)))}),[]);return{activeTab:a,setActiveTab:c,tabs:m,selection:p,handleSelection:(e,o)=>{const a=s().find(l,{name:e}),m=s().omit({...p,[e]:o},a.update),v=e=>{if(e.provinceCityDistrict)return n(e.provinceCityDistrict,m),void d(m);const t=(0,i.Td)(e),o=(p=r,v=l,f=t.options,g=a.update,p.reduce(((e,t)=>{const r=s().get(f,`[${t.name}].data`),n=s().find(v,{name:t.name});return g.includes(t.name)?r?[...e,{...t,options:r}]:e:r?[...e,{...t,options:r}]:n?[...e,n]:e}),[]));var p,v,f,g;u(o),d({...m,...t.selection}),c(Object.keys(o).length-1);const h=document.querySelectorAll(".rc-province-selector-tab");h.length&&h[h.length-1].focus()};(0,i.Fd)(t,m).then((e=>v(e)))},reset:()=>{c(0),d({}),u((e=>e.slice(0,1)))}}}},1644:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(1594),o=r.n(n);const a=e=>{let{ariaLabel:t,entered:r}=e;const n=o().useRef(null),a=o().useRef(null);return o().useEffect((()=>(r&&(a.current=document.activeElement,n.current&&n.current.focus()),()=>{a.current&&window.setTimeout((()=>{a.current.focus()}),0)})),[r]),o().createElement("div",{className:"column large-12 small-12 rf-pickupinfo-spinner"},o().createElement("div",{ref:n,role:"img",tabIndex:-1,className:"waitindicator waitindicator40-blue","aria-label":t}))};a.displayName="Loader";const s=a},1875:(e,t,r)=>{r.d(t,{Ot:()=>l,Ws:()=>m,ik:()=>g,sK:()=>d});var n=r(2224),o=r.n(n),a=r(5646),s=r(266),i=r(3658),c=r(2905);const l={},u=e=>{let{products:t,displayValues:r,dimensionsVariations:n}=e;return o().sortBy(t,(e=>((e,t,r)=>o().reduce(t.items,((t,n)=>{const o=n?.value?.dimensionValue,a=r?.[o]??[],s=e?.[o];return t+(a.find((e=>{let{dimKey:t,dimVal:r}=e;return t===o&&r===s}))??{}).sortOrder}),0))(e,r,n)))},p=(e,t)=>({...o().get(e,"overlayLevelTextAssets.namedTextAssets",{}),footer:o().get(e,"familyLevelFragments.namedFragments.footer",""),coldStartPickupInfo:o().get(e,"overlayLevelFragments.namedFragments.coldStartPickupInfo",""),filterButtonText:o().get(e,"familyLevelTextAssets.namedTextAssets.filterButtonText",""),errorMessage:t}),d=(e,t,r,n,u)=>p=>{const d=(0,a.tG)("productlocator/meta-reducer/fetchMetaData");p({type:c.E.PL_FETCH_META_START});const m=!o().isEmpty(l)&&t,v=(0,s.q)((0,i.bj)(m?t:e,r));return v.then((e=>{const t=e?.body?.productLocatorOverlayData,r=t?.errorMessage;r||o().isEmpty(t)?(d.debug(`ResponseError: ${r}`),p({type:c.E.PL_FETCH_META_ERROR,error:{isResponseError:!0,message:r||n}})):p(m?{type:c.E.PL_META_UPDATE,data:t,family:u}:{type:c.E.PL_META_INIT,data:t,family:u,errorMsg:n})})).catch((e=>{d.error(e),p({type:c.E.PL_FETCH_META_ERROR,error:{isNetworkError:!0,error:e,message:n}})})),v},m={isFetching:!1,isFetched:!1,isInitialized:!1,data:{},error:null},v=e=>{let{data:t,family:r,errorMsg:n}=e;return l[r]={...t,assets:p(t,n),noConfigurator:o().isEmpty(t.productLocatorMeta?.dimensionsVariations),productLocatorMeta:{...t.productLocatorMeta,products:u(t.productLocatorMeta)}},l[r]},f=(e,t)=>{let{data:r,family:n}=t;const a=o().isEmpty(e.data)?l[n]:e.data,s=r.productLocatorMeta?.prices;return s?(l[n].productLocatorMeta.prices=s,{...a,productLocatorMeta:{...a.productLocatorMeta,prices:s}}):a},g=(e,t)=>{switch(t.type){case c.E.PL_META_INIT:return{...e,isFetching:!1,isFetched:!0,isInitialized:!0,error:null,data:v(t)};case c.E.PL_META_UPDATE:return{...e,isFetching:!1,isFetched:!0,isInitialized:!0,error:null,data:f(e,t)};case c.E.PL_FETCH_META_START:return{...e,isFetching:!0,isFetched:!1,error:null};case c.E.PL_FETCH_META_ERROR:return{...e,isFetching:!1,isFetched:!0,error:t.error,data:{}};default:return e}}},2547:(e,t,r)=>{r.d(t,{A:()=>u});var n=r(8168),o=r(1594),a=r.n(o),s=r(6942),i=r.n(s),c=r(5911),l=r(1265);const u=e=>{let{className:t,text:r,...o}=e;return a().createElement("span",(0,n.A)({className:i()("rf-pickup-recommended-violator",l.A.CAPTION,t)},(0,c.OH)(r),o))}},2662:(e,t,r)=>{r.d(t,{Ay:()=>c});var n=r(1594),o=r(5911);const a=(e,t)=>{let r="";for(let n=0;n<t;n+=1)r+=e;return r},s=(e,t,r,n)=>{let{value:o,cursor:a}=e;const s=a>=r,i=a<r+1;return a=s&&i&&n<0?r:a,a+=s&&i&&n>0?t.length:0,a+=s&&!i?t.length:0,o=o.slice(0,r)+t+o.slice(r),{value:o,cursor:a}},i=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!e)return null;return((e,t,r)=>{const{value:n,cursor:o}=e,{inputRegex:i,suffix:c,autoCapitalization:l}=t,u=t.blocks||[],p=n.length===o&&r>0;let d=r,m={value:"",cursor:o},v=0,f=0,g=!1;for(let e=0;e<n.length;e+=1){const t=n.charAt(e),r=n.charAt(e+1),o=u[v]||{},c=u[v+1],l=(c&&c.start||"").charAt(0),b=o.inputRegex||i,E=e>=n.length-1,y=v>=u.length-1,A=o.length||o.maxLength||1/0,k=f>=A,w=void 0!==o.length,T=o.pad&&w,P=!w&&t===o.pad&&f<1,M=E&&t===o.pad,L=!P||M,N=!b||(h=b,"string"==typeof h||h instanceof String?new RegExp(h):h).test(t),S=!k&&L&&N,O=0===e&&t===l||r===l;g||!S&&!p&&w||(m=s(m,o.start||"",m.value.length,d),g=!0),f+=S?1:0,m.value+=S?t:"",m.cursor+=S||m.cursor<=m.value.length?0:-1;const x=d>0&&m.cursor===m.value.length,C=d>0&&m.cursor===m.value.length+1,R=O&&C&&T,D=O&&!w,_=f>=A||R||D;if(o.unit&&(_||E)&&!k){const e=f%3==0,t=Math.floor(f/3)-(e?1:0);for(let e=0;e<t;e+=1){const t=(3+o.unit.length)*e+3;m=s(m,o.unit,m.value.length-t,d)}}if(R||D&&o.pad&&f<1){const e=D?1:(o.length||0)-f,t=m.value.length-f;m=s(m,a(o.pad||"",e),t,d),m.cursor+=D&&m.cursor===t?1:0}_&&!y&&(v+=1,f=0,g=!1,d=d>0&&!w&&x?0:d)}var h;return c&&m.value&&(m.value+=c),l&&(m.value=m.value?.toString?.().toUpperCase?.()),m})({value:(arguments.length>3&&void 0!==arguments[3]?arguments[3]:"")||e.value,cursor:e.selectionStart||0},t,r)},c=e=>{const{value:t,format:r,...a}=e,[s,c]=(0,n.useState)(t),[l,u]=(0,n.useState)(-1),p=(0,n.useRef)(null),d=e.ref||p,m=(0,n.useRef)(!1);let v="";(0,n.useEffect)((()=>{const e=d.current;e&&l>-1&&m.current&&(e.selectionStart=l,e.selectionEnd=l,m.current=!1)}));const f={value:s,ref:d,onPaste:e=>{const t=e.clipboardData||window.clipboardData,r=e.target;v=r&&t?[r.value.slice(0,r.selectionStart||0),t.getData("text/plain"),r.value.slice(r.selectionEnd||r.value.length)].join(""):""},onChange:e=>{const t=e.nativeEvent.inputType||e.target.inputType,n=e.target,o=i(n,r,"deleteContentBackward"===t?-1:1,v);return v="",m.current=!0,c(o.value),u(o.cursor),o}};return(0,o.v6)({...f},{...a})}},2748:(e,t,r)=>{r.d(t,{A5:()=>v,L6:()=>m,Tg:()=>f,Ws:()=>h,ae:()=>d,xp:()=>c,z$:()=>b});var n=r(2224),o=r.n(n),a=r(2905),s=r(1875);const i=(e,t,r,n)=>{const a=o().get(t,e,[]),s=o().map(o().sortBy(a,"sortOrder"),"key"),i=o().sortBy(o().uniq(o().map(o().filter(r,e),e)),(e=>s.indexOf(e)));return o().map(i,(t=>{const r=o().find(a,{key:t})||{};return{value:t,image:r.swatchImage,label:r.value,selected:!!n&&n[e]===t}}))},c=(e,t)=>{const r=s.Ot[t]?.productLocatorMeta??{},{displayValues:n,products:a}=r,i=o().find(a,{partNumber:e})||{},c={};return n.items.forEach((e=>{const t=o().get(e,"value.dimensionValue");Object.prototype.hasOwnProperty.call(i,t)&&(c[t]=i[t])})),c},l=e=>e.filter((e=>o().find(e.options,"selected"))).map((e=>({key:e.key,value:o().find(e.options,"selected").value}))).reduce(((e,t)=>(e[t.key]=t.value,e)),{}),u=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const{key:n,value:a}=e,s=l(t);if(!function(e,t){let r=!1;return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:[]).forEach((n=>{n.key===e&&o().findIndex(n.options,{value:t})>=0&&(r=!0)})),r}(n,a,t))return s;if(!r)return{...s,[n]:a};const i=((e,t)=>{let r=!1;const n=t.filter((t=>{if(t.key===e&&(r=!0),!r)return t.key}));return o().map(n,"key")})(n,t);return{...o().pick(s,i),[n]:a}},p=(e,t,r)=>{let n=o().find(r,e);if(n)return n.partNumber;const a=Object.keys(e).length-1,s=a&&t[a]&&t[a].key,i=o().omit(e,s);return n=o().find(r,i),p(i,t,r)},d=(e,t,r)=>{const n=s.Ot[t],a=n?.productLocatorMeta??{},{products:i,prices:c}=a;if(!i||!c)return null;const l=r?{partNumber:e,carrierModel:r}:{partNumber:e},u=o().find(i,l)||{},p=o().get(c,u.price,{});switch(n.purchaseOption||"fullPrice"){case"bfi":return p?.bfiPrice||p?.fullPrice;case"ipp":return o().get(p,"iupPrice");case"iupPriceNonDefault":return o().get(p,"iupPriceNonDefault");case"ipi":return o().get(p,"ipiPrice");case"cp":return o().get(p,"carrierInstallment");default:return o().get(p,"fullPrice")}},m=(e,t,r)=>{const n=s.Ot[t]?.productLocatorMeta??{},{products:a,imageDictionary:i}=n;if(!a||!i)return{};const c=r?{partNumber:e,carrierModel:r}:{partNumber:e},l=o().find(a,c)||{};return o().get(i,l.image)},v=(e,t,r)=>{const n=s.Ot[t]?.productLocatorMeta?.products;if(!n)return null;const a=r?{partNumber:e,carrierModel:r}:{partNumber:e};return(o().find(n,a)||{}).productTitle},f=function(e){const t=s.Ot[e]?.productLocatorMeta?.products;if(!t)return null;for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.map((e=>(o().find(t,{partNumber:e})||{}).basePartNumber))},g=(e,t)=>{const r=s.Ot[t.family],n=r?.productLocatorMeta??{},{products:a,displayValues:c,dimensionsVariations:v}=n;let f;if(t.part)s.Ot[t.family]={...r,purchaseOption:t.purchaseOption},f=t.part;else{const r=l(e.dimensions),{key:n,value:o}=t.selection,s={...r,[n]:o};f=p(s,e.dimensions,a)}const g={partNumber:f},{cppart:h}=e;h&&(g.carrierModel=h);const b=o().find(a,g),E=d(f,t.family,h),y=m(f,t.family);let A=((e,t,r,n,a)=>{const s=e.items.map((e=>({label:e.value.dimensionLabel,key:e.value.dimensionValue,showDisabledSelectors:e.value.showDisabledSelectors}))),c={partNumber:n};a&&(c.carrierModel=a);const l=o().find(r,c);return s.map(((e,n)=>({header:e.label,key:e.key,id:`pl-dim-${n}`,options:i(e.key,t,r,l),modelMessage:o().get(window,"fulfillmentBootstrap.modelMessage",""),showDisabledSelectors:e.showDisabledSelectors})))})(c,v,a,f,h);return A=((e,t)=>e.map((r=>(r.options=r.options.map((n=>{const a={key:r.key,value:n.value},s=u(a,e,!0),i=o().find(t,s);return{...n,enabled:!!i}})),r))))(A,a),{...e,dimensions:A,part:f,product:b,displayPrice:E,productImage:y}},h={isShown:!1},b=(e,t)=>{switch(t.type){case a.E.PL_FILTER_UPDATE:return{...e,...g(e,t)};case a.E.PL_FILTER_SET_CPPART:return{...e,cppart:t.cppart};case a.E.PL_FILTER_SET_PART:return{...e,part:t.part};case a.E.PL_FILTER_SHOW:return{...e,isShown:!0};case a.E.PL_FILTER_HIDE:return{...e,isShown:!1};default:return e}}},3658:(e,t,r)=>{r.d(t,{bj:()=>i});var n=r(2224),o=r.n(n);const a=(e,t)=>encodeURI(`${e}=${t}`),s=e=>Object.keys(e).filter((t=>t in e&&void 0!==e[t]&&null!==e[t]&&""!==e[t])).map((t=>"parts"===t?e[t].map(((e,t)=>{if(e&&"object"==typeof e){const r=[];return e.part&&r.push(a(`parts.${t}`,e.part)),e.option&&r.push(a(`option.${t}`,e.option.join(","))),e.cppart&&r.push(a("cppart",e.cppart)),e.purchaseOption&&r.push(a("purchaseOption",e.purchaseOption)),r.join("&")}return a(`parts.${t}`,`${e}`)})).join("&"):a(`${t}`,`${e[t]}`))).join("&"),i=function(e){return((e,t)=>(e=(e=o().unescape(e)).indexOf("?")>-1?/&$/.test(e)?e:`${e}&`:`${e}?`,e+t))(e,s(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}))}},4175:(e,t,r)=>{r.d(t,{A:()=>n.A});var n=r(8203)},7335:(e,t,r)=>{r.d(t,{Fd:()=>c,Td:()=>l,UR:()=>i});var n=r(2224),o=r.n(n),a=r(5646),s=r(2684);const i=(e,t,r,n)=>{if(!!n){const t=Math.ceil(e/n);return Math.round(t*r)||0}const o=e*r,a=t*r;return Math.min(o,a)},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=(0,a.tG)("province-selector/utils/fetchData"),n=Object.keys(t).length?s.k0(e,t):e;return(0,a.hI)(n).then((e=>o().get(e,"body",{}))).catch((e=>r.error(e)))},l=e=>Object.keys(e).reduce(((t,r)=>("object"==typeof e[r]&&(t.options[r]=e[r]),"string"==typeof e[r]&&(t.selection[r]=e[r]),t)),{selection:{},options:{}})},8203:(e,t,r)=>{r.d(t,{A:()=>d,W:()=>p});var n=r(8168),o=r(1594),a=r.n(o),s=r(6942),i=r.n(s),c=r(5067),l=r(5911);const u=a().forwardRef(((e,t)=>{let{visible:r=!0,tiny:o,elevated:s,ariaLabel:c,className:u,...p}=e;return a().createElement("div",(0,n.A)({ref:t,className:i()("progress-indicator-container",u),role:"progressbar"},p),a().createElement("div",(0,n.A)({className:i()("progress-indicator","progress-indicator-indeterminate",{"progress-indicator-indeterminate-size-tiny":o,"progress-indicator-indeterminate-size-elevated":s,"progress-indicator-visible":r}),"aria-label":c},(0,l.OH)('\n<svg class="progress-indicator-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 56 56" aria-hidden="true">\n    <g class="progress-indicator-spokes">\n        <path class="progress-indicator-spoke" d="M28,8.5A2.5,2.5,0,0,1,30.5,11v7a2.5,2.5,0,0,1-5,0V11A2.5,2.5,0,0,1,28,8.5Z"></path>\n        <path class="progress-indicator-spoke" d="M41.79,14.21a2.52,2.52,0,0,1,0,3.54L36.84,22.7a2.5,2.5,0,0,1-3.54-3.54l5-4.95A2.52,2.52,0,0,1,41.79,14.21Z"></path>\n        <path class="progress-indicator-spoke" d="M47.5,28A2.5,2.5,0,0,1,45,30.5H38a2.5,2.5,0,0,1,0-5h7A2.5,2.5,0,0,1,47.5,28Z"></path>\n        <path class="progress-indicator-spoke" d="M41.79,41.79a2.52,2.52,0,0,1-3.54,0l-5-4.95a2.5,2.5,0,0,1,3.54-3.54l4.95,5A2.52,2.52,0,0,1,41.79,41.79Z"></path>\n        <path class="progress-indicator-spoke" d="M28,47.5A2.5,2.5,0,0,1,25.5,45V38a2.5,2.5,0,0,1,5,0v7A2.5,2.5,0,0,1,28,47.5Z"></path>\n        <path class="progress-indicator-spoke" d="M14.21,41.79a2.52,2.52,0,0,1,0-3.54l4.95-5a2.5,2.5,0,0,1,3.54,3.54l-4.95,4.95A2.52,2.52,0,0,1,14.21,41.79Z"></path>\n        <path class="progress-indicator-spoke" d="M8.5,28A2.5,2.5,0,0,1,11,25.5h7a2.5,2.5,0,0,1,0,5H11A2.5,2.5,0,0,1,8.5,28Z"></path>\n        <path class="progress-indicator-spoke" d="M14.21,14.21a2.52,2.52,0,0,1,3.54,0l4.95,4.95a2.5,2.5,0,0,1-3.54,3.54l-4.95-4.95A2.52,2.52,0,0,1,14.21,14.21Z"></path>\n    </g>\n</svg>'))))}));u.displayName="ProgressIndicator";const p=e=>{let{in:t,transitionProps:r={},...o}=e;const s=a().useRef(),{mountOnEnter:i=!0,unmountOnExit:l=!0,...p}=r;return a().createElement(c.Ay,(0,n.A)({in:t,timeout:400,nodeRef:s,mountOnEnter:i,unmountOnExit:l},p),(e=>a().createElement(u,(0,n.A)({ref:s,visible:"entering"===e||"entered"===e},o))))},d=u},8994:(e,t,r)=>{r.d(t,{I7:()=>f,Lw:()=>h,Ux:()=>b,Y$:()=>A,gE:()=>m,kE:()=>y,kG:()=>v,l_:()=>g,tj:()=>E});var n=r(2224),o=r.n(n),a=r(2748);const s=window.asMetrics,{sendUserInteraction:i=()=>{},util:c={}}=window.asMetrics||{},{OmnitureCollection:l,OmnitureEvent:u}=c.omnitureCollection;let p,d="storeLocator";const m=(e,t)=>{const r=new l(new u("event29"));e?r.add(new u("event32")):r.add(new u("event31"));const n=['D=pageName+"',"",d,"",`${e?"See more stores":"Check availability"}"`].join("|"),o=t.substring(0,5);i({name:d,beacon:{linkTrackVars:"eVar67,prop37,events",linkTrackEvents:r,events:r,prop37:n,eVar67:o}})},v=(e,t)=>{const r=new l(new u("event127")),n=['D=pageName+"',"",d,"APU",`${e}"`].join("|");if(t){i({name:d,beacon:{events:r,eVar6:n}})}else s.fireMicroEvent({part:"APU",feature:d,eVar:"eVar6",action:e})},f=(e,t,r)=>{let n=o().map(e,(e=>e.part));n=(0,a.Tg)(r,...n);const s=['D=pageName+"',"",d,"Recommended",`${n}"`].join("|"),c=['D=pageName+"',"",d,'No Availaibility"'].join("|"),p=new l(new u("event127"));let m={eVar6:s,eVar5:c};t&&(m={...m,events:p}),i({name:d,beacon:m})},g=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;const r=(0,a.Tg)(t,e.part),n=e.isDeliverySelected?"STH":"APU",o=new l(new u("event128")),s=['D=pageName+"',"",d,"Continue",`${n}"`].join("|"),c=r[0];e.part!==p&&(e.isSuggestionSelected?o.add(new u("event130")):o.add(new u("event129")));i({name:d,beacon:{events:o,eVar6:s,eVar67:c}})},h=(e,t)=>{const r=(0,a.xp)(e,t)||{},n=o().toArray(o().omit(r,"carrierModel")).join(" > ").toUpperCase();s.fireMicroEvent({part:n,feature:d,eVar:"eVar6",action:"Selected"})},b=e=>{const t=e?"Check availability of another device - open":"Check availability of another device - close";s.fireMicroEvent({part:"Link",feature:d,eVar:"eVar6",action:t})},E=()=>{const e=new l(new u("event37")),t=['D=pageName+"',"",d,"","Retail store selected"].join("|");i({name:d,beacon:{events:e,prop37:t}})},y=()=>{s.fireMicroEvent({feature:d,eVar:"eVar6",action:"Close"})},A=(e,t)=>{p=e,d=t?"storeLocator - sticky":"storeLocator"}},9283:(e,t,r)=>{r.d(t,{A:()=>m});var n=r(8168),o=r(1594),a=r.n(o),s=r(6942),i=r.n(s),c=r(840),l=r(5911),u=r(6444);const p=(e,t)=>{if(!t.current)return;const r=t.current,n=r?r.querySelector(`[data-option-index="${e}"]`):null;if(r&&n&&r.scrollHeight>r.clientHeight){const e=n,t=r.clientHeight+r.scrollTop,o=e.offsetTop+e.offsetHeight;o>t?r.scrollTop=o-r.clientHeight:e.offsetTop<r.scrollTop&&(r.scrollTop=e.offsetTop)}},d=(e,t)=>{let{id:r,className:o,value:s,options:d,onChange:m=()=>{},onFocus:v=()=>{},onBlur:f=()=>{},onKeyDown:g=()=>{},onSelection:h=()=>{},onHover:b=()=>{},getOptionValue:E=e=>e,getOptionDisabled:y=()=>!1,getPosition:A,renderInput:k=e=>a().createElement("input",e),renderOption:w=e=>e,selectFirst:T=!1,selectOnHover:P=!1,closeOnSelection:M=!0,classes:L={},optionsHeader:N,noOptionsLabel:S,wrapperAttrs:O={},...x}=e;const[C,R]=a().useState(!1),[D,_]=a().useState(-1),H=a().useRef(null),F=a().useRef(null),I=a().useRef(null),$=(0,u.y)(I,{observe:C}),V=(0,l.E2)(I,t),j=a().useRef(!1),U=()=>{R(!1),_(-1)},B=e=>h(E(d[e]),e),q=e=>{-1!==D&&e.preventDefault();const t=(e=>{if(e===d.length-1)return e;let t=e,r=!1;for(;!r&&t<d.length-1;)t+=1,r=!y(d[t]);return r?t:e})(D);_(t),p(t,F),b(E(d[t]),t),C||R(!0),P&&B(t)},z=e=>{-1!==D&&e.preventDefault();const t=(e=>{if(-1===e)return e;if(0===e)return-1;let t=e,r=!1;for(;!r&&t>0;)t-=1,r=!y(d[t]);return r?t:-1})(D);_(t),p(t,F),b(E(d[t]),t),C||R(!0),P&&B(t)},Z=a().useId(),W=r||Z,K=`${W}_listbox`,G=D>=0?`${K}_option_${D}`:void 0,Y=S?C:Boolean(C&&d.length),X=A?A($):(e=>{if(!e)return{};const{width:t,right:r}=e;return{width:t,left:`${r-t+window.pageXOffset}px`}})($),{onBlur:J,...Q}=O;return a().createElement("div",(0,n.A)({ref:H,"data-core-autocomplete":!0,className:i()(o,L.root),onBlur:e=>{(e=>{requestAnimationFrame((()=>{if(j.current)return e.preventDefault(),void(j.current=!1);H.current?.contains(document.activeElement)||(U(),f(e))}))})(e),J?.(e)}},Q),k({id:W,value:s,role:"combobox","aria-autocomplete":"list","aria-expanded":Y,...x,"aria-controls":K,"aria-activedescendant":G,ref:V,onChange:e=>{C||R(!0),m(e),T&&_(0)},onFocus:e=>{R(!0),_(-1),v(e)},onKeyDown:e=>{switch(e.keyCode){case c.HP.ArrowUp:z(e);break;case c.HP.ArrowDown:q(e);break;case c.HP.Home:e.preventDefault(),_(0),p(0,F),b(E(d[0]),0),C||R(!0),P&&B(0);break;case c.HP.End:e.preventDefault(),(()=>{const e=d.length-1;_(e),p(e,F),b(E(d[e]),e),C||R(!0),P&&B(e)})();break;case c.HP.Return:(e=>{const t=d[D],r=y(t);t&&!r&&C&&(e.preventDefault(),B(D),M&&U())})(e);break;case c.HP.Esc:C&&e.stopPropagation(),U()}g(e)}}),a().createElement("div",{"data-core-autocomplete-popover":!0,"data-core-autocomplete-popover-show":Y,style:X,ref:F,onMouseDown:()=>{j.current=!0},onMouseUp:()=>{j.current=!1},className:i()(L.popoverWrapper,{[L.popoverWrapperShow||""]:Y,[L.popoverWrapperHide||""]:!Y})},N,0===d.length&&S,a().createElement("ul",{role:"listbox",id:K,"data-core-autocomplete-options-list":!0,className:i()(L.optionsList,{[L.optionsListShow||""]:Y,[L.optionsListHide||""]:!Y}),tabIndex:-1},d.map(((e,t)=>{const r=E(e),n=y(e),o=D===t;return a().createElement("li",{key:r,id:`${K}_option_${t}`,role:"option","data-core-autocomplete-option":!0,className:L.option,"data-option-index":t,onClick:()=>{n||(h(r,t),I.current&&I.current.focus(),M&&U())},onMouseOver:()=>{n||(_(t),b(r,t),P&&h(r,t))},tabIndex:-1,"aria-selected":o,"aria-disabled":n},w(e,o,t))})))))},m=a().forwardRef(d)},9904:(e,t,r)=>{r.d(t,{Ay:()=>w,vH:()=>T.A});var n=r(8168),o=r(1594),a=r.n(o),s=r(2224),i=r.n(s),c=r(6942),l=r.n(c),u=r(840),p=r(3694),d=r(4768),m=r(5156);const v=a().createContext({}),f=()=>a().useContext(v);var g=r(7335);const h=e=>{let{className:t,...r}=e;return a().createElement(p.wb,(0,n.A)({rootTag:"ul",className:l()("rc-province-selector-tablist",t)},r))},b=a().forwardRef(((e,t)=>{let{index:r,name:o,label:s,className:i,...c}=e;const{selection:u}=f(),d=u[o]?a().createElement(a().Fragment,null,u[o],a().createElement("span",{className:"visuallyhidden"},s)):s;return a().createElement("li",{role:"presentation"},a().createElement(p.oz,(0,n.A)({index:r,className:l()("rc-province-selector-tab",i),ref:t},c),d))}));b.displayName="ProvinceSelectorTab";const E=e=>{let{className:t,children:r,...o}=e;return a().createElement(p.T2,(0,n.A)({className:l()("rc-province-selector-tab-panels",t)},o),r)},y=e=>{let{index:t,optionsLength:r,classes:o={},children:s,...i}=e;const{isMobile:c,optionsPerColumn:u,optionsHeight:d,mobileColumns:m,desktopColumns:v}=f(),h=c?m:v,b={height:`${(0,g.UR)(r,u,d,h)}px`};return a().createElement(p.Kp,(0,n.A)({index:t,className:l()("rc-province-selector-tab-panel",o.root)},i),a().createElement("ol",{className:l()("rc-province-selector-tab-options",o.list),style:b,role:"list"},s))},A=a().forwardRef(((e,t)=>{let{name:r,value:o,index:s,className:c,children:p,onClick:m,...v}=e;const{selection:g,onSelection:h}=f(),b=a().useRef(!1);return a().createElement("li",{role:"listitem",ref:b},a().createElement("button",(0,n.A)({type:"button",className:l()("rc-province-selector-option",d.A.BODY,c,{"rc-province-selector-option-selected":g[r]===o},`rc-province-selector-option-${r}`),"aria-current":g[r]===o||null,onClick:()=>{h(r,o),m&&m()},onKeyDown:e=>{const t=(()=>{const e=i().get(b,"current.nextSibling");return e?e.querySelector(".rc-province-selector-option"):null})(),n=(()=>{const e=i().get(b,"current.previousSibling");return e?e.querySelector(".rc-province-selector-option"):null})(),a=document.querySelectorAll(`.rc-province-selector-option-${r}`);switch(e.keyCode){case u.HP.Home:e.preventDefault(),a.length&&a[0].focus();break;case u.HP.End:e.preventDefault(),a.length&&a[a.length-1].focus();break;case u.HP.ArrowDown:case u.HP.ArrowRight:t&&t.focus(),e.preventDefault();break;case u.HP.ArrowUp:case u.HP.ArrowLeft:n&&n.focus(),e.preventDefault();break;case u.HP.Space:case u.HP.Return:e.preventDefault(),h(r,o)}},tabIndex:g[r]===o||0===s&&!g[r]?"0":"-1",ref:t},v),p))}));A.displayName="ProvinceSelectorOption";const k=a().forwardRef(((e,t)=>{let{activeTab:r,onTabChange:n=()=>{},tabs:o=[],selection:s={},onSelection:i=()=>{},optionsPerColumn:c=10,optionsHeight:u=62,mobileColumns:d,desktopColumns:f,classes:g={},children:h}=e;const{viewport:b}=(0,m.S)(),E="small"===b;return a().createElement(v.Provider,{value:{selection:s,onSelection:i,isMobile:E,optionsPerColumn:c,optionsHeight:u,mobileColumns:d,desktopColumns:f}},a().createElement("div",{className:l()("rc-province-selector",g.root),ref:t},a().createElement(p.Ay,{id:"rc-province-selector-tabs",className:l()("rc-province-selector-tabs",g.tabs),index:r,handleChange:n,count:o.length},h)))}));k.displayName="ProvinceSelectorTabs";const w=e=>{let{tabs:t,activeTab:r,onTabChange:o,selection:s,onSelection:c,...l}=e;return a().createElement(k,(0,n.A)({activeTab:r,onTabChange:o,selection:s,onSelection:c,tabs:t},l),a().createElement(h,null,t.map(((e,t)=>a().createElement(b,{key:e.name,index:t,name:e.name,label:e.label})))),a().createElement(E,null,t.map(((e,t)=>a().createElement(y,{key:e.name,index:t,name:e.name,"aria-label":e.label,optionsLength:i().get(e,"options.length",0)},i().get(e,"options",[]).map(((t,r)=>a().createElement(A,{key:t.value,name:e.name,value:t.value,index:r},t.label))))))))};var T=r(1434)}}]);